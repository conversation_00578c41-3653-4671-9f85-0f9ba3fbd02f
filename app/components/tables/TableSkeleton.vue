<script setup lang="ts">
import { USkeleton } from '#components'

defineProps<{
  rows: number
  columns: number
}>()
</script>

<template>
  <div class="overflow-x-auto w-full">
    <table class="min-w-full divide-y divide-gray-200">
      <thead>
        <tr>
          <th v-for="col in columns" :key="col" class="px-4 py-2">
            <USkeleton class="h-4 w-24" />
          </th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-100">
        <tr v-for="row in rows" :key="row">
          <td
            v-for="col in columns"
            :key="`row-${row}-col-${col}`"
            class="px-4 py-3"
          >
            <USkeleton class="h-4 w-full" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
