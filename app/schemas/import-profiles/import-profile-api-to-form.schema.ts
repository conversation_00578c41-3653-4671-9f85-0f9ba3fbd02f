import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { z } from 'zod'
import { EncodingType } from '~/utils/types/api/api'

export const importProfileDataSchema = z.object({
  id: z.string().uuid(),
  status: z.string(),
  name: z.string(),
  integration_id: z.string().uuid(),
  asset_type_id: z.string().uuid(),
  path: z.string().nullable(),
  config: z.any().nullable(),
  mapping: z.array(z.any()).nullable(),
  schedule: z.string().nullable(),
  encoding: z.nativeEnum(EncodingType).nullable(),
})

export const importProfileDataToFormSchema = importProfileDataSchema.transform((data): ImportProfileFormData => ({
  name: data.name,
  status: data.status,
  integration_id: data.integration_id,
  asset_type_id: data.asset_type_id,
  path: data.path ?? '/',
  config: data.config,
  mapping: data.mapping,
  schedule: data.schedule,
  encoding: data.encoding,
}))
