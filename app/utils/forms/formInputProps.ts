import type { ErrorDisplay } from './errorDisplayProp'
import type { FormField } from '~/utils/forms/FormField'
import { errorDisplayProp } from './errorDisplayProp'

export interface FormInputProps {
  name: string
  label: string
  field: FormField
  errors: Record<string, string[]>
  isReadOnly: boolean
  crudOperations: boolean
  errorDisplay: ErrorDisplay
}

export const formInputProps = {
  name: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  field: {
    type: Object as PropType<FormField>,
    required: true,
  },
  errors: {
    type: Object as PropType<Record<string, string[]>>,
    default: () => ({}),
  },
  isReadOnly: {
    type: Boolean,
    default: false,
  },
  crudOperations: {
    type: Boolean,
    default: false,
  },
  ...errorDisplayProp,
}
