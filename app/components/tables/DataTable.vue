<script setup lang="ts">
import type { ColumnDef, Row } from '@tanstack/vue-table'
import type { MappingItemData } from '~/utils/types/api/api'
import type { ColumnMeta } from '~/utils/types/table/column-meta'
import type { ColumnType } from '~/utils/types/table/ColumnType'
import {
  getCoreRowModel,
  getSortedRowModel,
  useVueTable,
} from '@tanstack/vue-table'
import { computed, h, watch } from 'vue'
import { useColumnComponentMap } from '@/composables/table/useColumnComponentMap'
import DefaultHeader from './DefaultHeader.vue'
import MappingHeader from './MappingHeader.vue'

const props = withDefaults(
  defineProps<{
    columnsMeta: ColumnMeta[]
    data: Record<string, any>[]
    rowErrors?: Record<number, string[]>
    editable?: boolean
    sortable?: boolean
    mappingMeta?: MappingItemData[]
    mappingOptions?: { label: string, value: string }[]
  }>(),
  {
    editable: false,
    sortable: false,
  },
)
const emit = defineEmits<{
  (e: 'cellUpdate', payload: { rowIndex: number, columnKey: string, value: any }): void
  (e: 'updateMapping', payload: { from: string, to: string, skip: boolean, mapping: any | null }): void
}>()

const columnComponentMap = useColumnComponentMap()

function getCellComponent(columnType: ColumnType, isEditable: boolean) {
  const componentSet = columnComponentMap[columnType]
  return markRaw(
    isEditable && 'edit' in componentSet
      ? componentSet.edit
      : componentSet.view,
  )
}

function getCellProps<T>(col: ColumnMeta, row: Row<T>, value: T) {
  return {
    value,
    row: row.original,
    config: col.config,
    rowIndex: row.index,
    columnKey: col.key,
    onUpdate: (newValue: T) =>
      emit('cellUpdate', {
        rowIndex: row.index,
        columnKey: col.key,
        value: newValue,
      }),
  }
}

function isColumnEditable(col: ColumnMeta): boolean {
  return !!col.editable && props.editable
}

function getValueFromPath(obj: any, path: string): any {
  if (!obj || typeof obj !== 'object')
    return undefined

  if (Object.prototype.hasOwnProperty.call(obj, path)) {
    return obj[path]
  }

  return path.split('.').reduce((current, key) => {
    return current && typeof current === 'object' && key in current
      ? current[key]
      : undefined
  }, obj)
}

function generateColumns() {
  return props.columnsMeta.map((col): ColumnDef<any, any> & { cellComponent?: any, config?: any, weight?: number } => {
    const ConcreteCellComponent = getCellComponent(col.type as ColumnType, isColumnEditable(col))

    return {
      id: col.id,
      accessorFn: (row: any) => {
        return getValueFromPath(row, col.key)
      },
      header: col.label,
      enableSorting: props.sortable && col.sortable,
      config: col.config,
      cellComponent: ConcreteCellComponent,
      weight: col.weight ?? 1,
      cell: ({ getValue, row }) => {
        let value = getValue()
        if (col.type === 'relationship' && typeof value === 'object' && value !== null) {
          value = value.id
        }

        return h(ConcreteCellComponent, getCellProps(col, row, value))
      },
    }
  })
}

const dataRef = toRef(props.data)

const table = useVueTable({
  columns: generateColumns(),
  data: dataRef,
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
})

watch(
  () => props.data,
  (newValue) => {
    dataRef.value = [...newValue]
  },
  { deep: true },
)

watch(() => props.editable, (newValue, oldValue) => {
  if (newValue === oldValue) {
    return
  }

  table.setOptions(prev => ({
    ...prev,
    columns: generateColumns(),
  }))
})

function resolveHeaderLabel(header: any): string {
  const raw = header.column.columnDef.header
  if (typeof raw === 'string')
    return raw
  return header.column.id
}

const columnWidths = computed(() => {
  const columns = table.getAllColumns()
  const totalWeight = columns.reduce((sum, col) => {
    const weight = (col.columnDef as any)?.weight ?? 1
    return sum + (weight === 0 ? 0 : weight)
  }, 0)

  return columns.map((col) => {
    const weight = (col.columnDef as any)?.weight ?? 1
    if (weight === 0) {
      return 'min-content'
    }
    const percentage = (weight / totalWeight) * 100
    return `${percentage}%`
  })
})
</script>

<template>
  <div class="w-full overflow-x-auto">
    <table class="w-full text-sm table-fixed">
      <colgroup>
        <col
          v-for="(width, index) in columnWidths"
          :key="`col-${index}`"
          :style="{ width }"
        >
      </colgroup>
      <MappingHeader
        v-if="mappingMeta && mappingOptions"
        :headers="table.getFlatHeaders().map(h => ({ id: h.column.id, label: resolveHeaderLabel(h) }))"
        :mapping-meta="mappingMeta"
        :mapping-options="mappingOptions"
        @update-mapping="emit('updateMapping', $event)"
      />
      <DefaultHeader
        v-else
        :headers="table.getFlatHeaders().map(h => ({ id: h.column.id, label: resolveHeaderLabel(h) }))"
      />
      <tbody>
        <tr
          v-for="row in table.getRowModel().rows"
          :key="row.id"
          class="border-y border-gray-300"
          :class="[
            props.rowErrors?.[row.index]?.length ? 'bg-red-50 border-l-4 border-red-400' : '',
          ]"
        >
          <td
            v-for="cell in row.getVisibleCells()"
            :key="cell.id"
            class="text-left px-1 py-2 truncate align-middle"
          >
            <component
              :is="(cell.column.columnDef as any).cellComponent"
              :value="cell.getValue()"
              :row="cell.row.original"
              :config="(cell.column.columnDef as any).config"
              :row-index="cell.row.index"
              :column-key="cell.column.id"
              @update="(newValue: any) =>
                emit('cellUpdate', {
                  rowIndex: cell.row.index,
                  columnKey: cell.column.id,
                  value: newValue,
                })"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
