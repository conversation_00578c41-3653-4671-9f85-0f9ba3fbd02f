import type { WebhookConfigurationFormData } from '~/schemas/webhooks/webhook-form-schema'
import type { StatusMessage, WebhookData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { createWebhookApiSchema } from '~/schemas/webhooks/create-webhook.schema'
import { updateWebhookApiSchema } from '~/schemas/webhooks/update-webhook.schema'
import { useApiClient } from '~/utils/api/api-client'
import { transformOrThrow } from '~/utils/zod/zod-transformer'
import { WEBHOOK_API_ROUTES } from './webhook-service.routes'

export class WebhookService {
  public static async getAll(): Promise<WebhookData[]> {
    const response = await useApiClient()<ApiResponse<WebhookData[]>>(
      WEBHOOK_API_ROUTES.ALL,
    )

    return response.data
  }

  public static async getById(id: string): Promise<WebhookData> {
    const response = await useApiClient()<ApiResponse<WebhookData>>(
      WEBHOOK_API_ROUTES.BY_ID(id),
    )

    return response.data
  }

  public static async getByIdOrFail(id: string): Promise<WebhookData> {
    const webhook = await this.getById(id)

    if (!webhook) {
      throw new Error(`Webhook with ID ${id} not found`)
    }

    return webhook
  }

  public static async create(data: WebhookConfigurationFormData): Promise<WebhookData> {
    const parsedData = transformOrThrow(createWebhookApiSchema, data)

    const response = await useApiClient()<ApiResponse<WebhookData>>(
      WEBHOOK_API_ROUTES.ALL,
      {
        method: 'POST',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async update(
    id: string,
    data: WebhookConfigurationFormData,
  ): Promise<WebhookData> {
    const parsedData = transformOrThrow(updateWebhookApiSchema, data)

    const response = await useApiClient()<ApiResponse<WebhookData>>(
      WEBHOOK_API_ROUTES.BY_ID(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async delete(id: string): Promise<void> {
    await useApiClient()(
      WEBHOOK_API_ROUTES.BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }

  public static async test(data: WebhookData): Promise<StatusMessage> {
    const response = await useApiClient()<ApiResponse<StatusMessage>>(
      WEBHOOK_API_ROUTES.TEST,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }
}
