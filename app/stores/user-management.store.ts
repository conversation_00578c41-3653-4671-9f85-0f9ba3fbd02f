import type QueryData from '~/utils/query/QueryData'
import type { CreateUserData, UpdateUserData, UserData } from '~/utils/types/api/api'
import type { ApiResponse, PaginatedApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useUserManagementStore = defineStore('user-management', () => {
  async function fetchAll(queryData: QueryData) {
    return await useApiClient()<PaginatedApiResponse<UserData[]>>(
      `api/identity/users?${queryData.toQueryString()}`,
    )
  }

  async function fetch(userId: string) {
    return await useApiClient()<ApiResponse<UserData>>(`api/identity/users/${userId}`)
  }

  async function create(createUserData: CreateUserData) {
    return await useApiClient()<ApiResponse<UserData>>(`api/identity/users`, {
      method: 'POST',
      body: JSON.stringify(createUserData),
    })
  }

  async function update(userId: string, updateUserData: UpdateUserData) {
    return await useApiClient()<ApiResponse<UserData>>(`api/identity/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(updateUserData),
    })
  }

  async function destroy(userId: string) {
    await useApiClient()<ApiResponse<boolean>>(`api/identity/users/${userId}`, {
      method: 'DELETE',
    })
    return true
  }

  return {
    fetchAll,
    fetch,

    create,
    update,
    destroy,
  }
})
