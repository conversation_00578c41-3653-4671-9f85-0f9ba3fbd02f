<script setup lang="ts">
import { ref, watch } from 'vue'
import { DateFormat } from '~/utils/dates/date.format'

const props = defineProps<{
  value: string | null | undefined
}>()

const emit = defineEmits<{
  (e: 'update', value: string | null | undefined): void
}>()

const dayjs = useDayjs()

const inputValue = ref<string | null>(props.value ? dayjs(props.value).format(DateFormat.YYYY_MM_DD_HH_MM_DASH) : null)

watch(() => props.value, (val) => {
  inputValue.value = val ? dayjs(val).format(DateFormat.YYYY_MM_DD_HH_MM_DASH) : null
})

watch(inputValue, (val) => {
  if (val) {
    const iso = dayjs(val, DateFormat.DD_MM_YYYY_DASH).toISOString()
    emit('update', iso)
  }
  else {
    emit('update', null)
  }
})
</script>

<template>
  <UInput
    v-model="inputValue"
    type="date"
    class="w-full"
    :placeholder="DateFormat.DD_MM_YYYY"
  />
</template>
