import { createQueryKeys } from '@lukemorales/query-key-factory'
import { ImportProfileService } from '../services/import-profile.service'

export const importProfileKeys = createQueryKeys('importProfile', {
  all: {
    queryKey: null,
    queryFn: () => ImportProfileService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => ImportProfileService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => ImportProfileService.getByIdOrFail(id),
  }),
  files: (id: string, path: string) => ({
    queryKey: [id, path],
    queryFn: () => ImportProfileService.files(id, path),
  }),
  records: (id: string) => ({
    queryKey: [id],
    queryFn: () => ImportProfileService.records(id),
  }),
})
