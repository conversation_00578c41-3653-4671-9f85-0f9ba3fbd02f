<script setup lang="ts">
import type { ApiIntegrationForm } from '~/schemas/integrations/api-integration-form.schema'
import type { FtpIntegrationForm } from '~/schemas/integrations/ftp-integration-form.schema'
import { useCreateIntegrationMutation, useTestIntegrationMutation, useUpdateIntegrationMutation } from '~/api/integrations/mutations/useIntegrationMutations'
import { useIntegrationByIdQuery } from '~/api/integrations/queries/useIntegrationQueries'
import { useIntegrationFormHandlers } from '~/composables/integrations/useIntegrationFormHandlers'
import { replaceEmptyStringsWithUndefined } from '~/utils/replace-empty-strings'
import { IntegrationProvider } from '~/utils/types/api/api'
import IntegrationsUpsertApi from './IntegrationsUpsertApi.vue'
import IntegrationsUpsertFtp from './IntegrationsUpsertFtp.vue'

const props = defineProps<{
  integrationId?: string
  integrationType: string
  isCreateMode: boolean
}>()

const provider = computed(() => {
  switch (props.integrationType) {
    case 'api':
      return IntegrationProvider.API
    case 'ftp':
      return IntegrationProvider.FTP
    default:
      return null
  }
})

const { handleFormSubmitError, handleFormSubmitSuccess, handleQueryError } = useIntegrationFormHandlers()

const {
  data: integration,
} = useIntegrationByIdQuery(
  props.integrationId as string,
  {
    enabled: computed(() => !props.isCreateMode && !!props.integrationId),
    onErrorCaptured: handleQueryError,
  },
)

const createIntegrationMutation = useCreateIntegrationMutation()
const updateIntegrationMutation = useUpdateIntegrationMutation()

const isSubmitting = computed(() => createIntegrationMutation.isPending.value || updateIntegrationMutation.isPending.value)

const testIntegrationMutation = useTestIntegrationMutation()
const testResult = computed(() => testIntegrationMutation.data.value)
function testConnection(formData: ApiIntegrationForm | FtpIntegrationForm) {
  if (!provider.value)
    return

  const cleanedFormData = replaceEmptyStringsWithUndefined(formData)

  testIntegrationMutation.mutate({
    ...cleanedFormData,
    provider: provider.value,
  })
}

async function handleSubmit(formData: ApiIntegrationForm | FtpIntegrationForm) {
  const providerValue = provider.value
  if (!providerValue)
    return

  const cleaned = replaceEmptyStringsWithUndefined(formData)

  const action = props.isCreateMode
    ? () => createIntegrationMutation.mutateAsync({ ...cleaned, provider: providerValue })
    : () => updateIntegrationMutation.mutateAsync({ integrationId: props.integrationId!, data: cleaned })

  try {
    await action()
    handleFormSubmitSuccess(props.isCreateMode)
  }
  catch (error) {
    handleFormSubmitError(error, props.isCreateMode)
  }
}
</script>

<template>
  <IntegrationsUpsertApi
    v-if="provider === IntegrationProvider.API"
    :integration-id="integrationId"
    :is-create-mode="isCreateMode"
    :integration="integration"
    :is-submitting="isSubmitting"
    @submit="handleSubmit"
  />
  <IntegrationsUpsertFtp
    v-else-if="provider === IntegrationProvider.FTP"
    :integration-id="integrationId"
    :is-create-mode="isCreateMode"
    :integration="integration"
    :is-submitting="isSubmitting"
    :test-result="testResult"
    :test-mutation="testIntegrationMutation"
    @test="testConnection"
    @submit="handleSubmit"
  />
  <div v-else class="p-6 text-center">
    <p class="text-gray-500">
      Integration type "{{ integrationType }}" is not yet implemented.
    </p>
  </div>
</template>
