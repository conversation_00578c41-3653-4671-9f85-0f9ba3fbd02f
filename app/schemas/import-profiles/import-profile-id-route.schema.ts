import { z } from 'zod'
import { enumToTuple } from '~/utils/enum/enum-to-tuple.converter'
import { IMPORT_PROFILE_STATUSES } from '~/utils/import-profiles/import-profile-statusses'

export const importProfileQuerySchema = z.object({
  step: z
    .enum(enumToTuple(IMPORT_PROFILE_STATUSES))
    .optional(),
})

export const importProfileRouteParamsSchema = z.object({
  id: z.union([
    z.literal('new'),
    z.string().uuid({ message: 'Invalid UUID for import profile ID' }),
  ]),
})

export type ImportProfileQueryParams = z.infer<typeof importProfileQuerySchema>
export type ImportProfileRouteParams = z.infer<typeof importProfileRouteParamsSchema>
