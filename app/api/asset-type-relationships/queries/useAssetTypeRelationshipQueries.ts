import type { RelationshipFilter } from '~/stores/asset-type-relationship.store'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeRelationshipKeys } from '../keys/asset-type-relationship.keys'

export function useAssetTypeRelationshipsQuery() {
  return useQuery({
    ...assetTypeRelationshipKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeRelationshipsByAssetTypeQuery(
  assetTypeId: string,
  filters?: RelationshipFilter[],
) {
  return useQuery({
    ...assetTypeRelationshipKeys.byAssetTypeId(assetTypeId, filters),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeRelationshipQuery(id: string) {
  return useQuery({
    ...assetTypeRelationshipKeys.byId(id),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeRelationshipByIdOrFailQuery(id: string) {
  return useQuery({
    ...assetTypeRelationshipKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.INFITE,
  })
}
