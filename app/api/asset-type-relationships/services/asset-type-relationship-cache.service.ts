import type { AssetTypeRelationshipData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeRelationshipKeys } from '../keys/asset-type-relationship.keys'

interface RelationshipFilter<T = any> {
  field: keyof AssetTypeRelationshipData
  value: T
}

export class AssetTypeRelationshipCacheService {
  private static getCachedAssetTypeRelationships(): AssetTypeRelationshipData[] | undefined {
    const { getCachedQuery } = useCachedQueries()
    return getCachedQuery<AssetTypeRelationshipData[]>([assetTypeRelationshipKeys.all]).value
  }

  private static applyFilters(data: AssetTypeRelationshipData[], filters: RelationshipFilter[]): AssetTypeRelationshipData[] {
    return data.filter((item) => {
      return filters.every((filter) => {
        if (Array.isArray(filter.value)) {
          return filter.value.includes(item[filter.field])
        }
        return item[filter.field] === filter.value
      })
    })
  }

  static getById(id: string): AssetTypeRelationshipData | undefined {
    const relationships = this.getCachedAssetTypeRelationships()
    return relationships?.find((r: AssetTypeRelationshipData) => r.id === id)
  }

  static getByIdOrFail(id: string): AssetTypeRelationshipData {
    const relationship = this.getById(id)

    if (!relationship) {
      throw new Error(`AssetTypeRelationship with id ${id} not found`)
    }

    return relationship
  }

  static getByAssetTypeId(assetTypeId: string, filters?: RelationshipFilter[]): AssetTypeRelationshipData[] {
    const relationships = this.getCachedAssetTypeRelationships()
    const byAssetType = relationships?.filter((r: AssetTypeRelationshipData) => r.asset_type_id === assetTypeId) || []

    if (!filters || filters.length === 0) {
      return byAssetType
    }

    return byAssetType.filter((item) => {
      return filters.every((filter) => {
        if (Array.isArray(filter.value)) {
          return filter.value.includes(item[filter.field])
        }

        return item[filter.field] === filter.value
      })
    })
  }
}
