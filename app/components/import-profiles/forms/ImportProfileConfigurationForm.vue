<script setup lang="ts">
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { cloneDeep } from 'lodash-es'
import { computed, ref, watch } from 'vue'
import { useAssetTypesQuery } from '~/api/asset-types'
import { useIntegrationsQuery } from '~/api/integrations/queries/useIntegrationQueries'
import { createImportProfileUiSchema } from '~/schemas/import-profiles/create-import-profile.schema'

const props = defineProps<{
  profile: ImportProfileFormData
  isCreateMode: boolean
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const formState: Ref<ImportProfileFormData> = ref(cloneDeep(props.profile))

watch(() => props.profile, (newProfile) => {
  formState.value = cloneDeep(newProfile)
}, { immediate: true })

const {
  data: assetTypes,
  isFetching: isAssetTypesFetching,
} = useAssetTypesQuery()

const mappedAssetTypes = computed(() =>
  (assetTypes.value ?? [])
    .filter(assetType => assetType.supporting_data === true)
    .map(assetType => ({
      label: assetType.label,
      value: assetType.id,
    })),
)

const {
  data: integrations,
  isFetching: isIntegrationsFetching,
} = useIntegrationsQuery()

const mappedIntegrations = computed(() =>
  (integrations.value ?? []).map(integration => ({
    label: integration.label,
    value: integration.id,
  })),
)

async function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  await formRef.value.submit()

  emit('submit', formState.value, isSaveAndBackToOverview)
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="createImportProfileUiSchema"
    class="space-y-4"
  >
    <h2 class="text-lg font-bold">
      Profile Configuration
    </h2>

    <UFormField label="Profile name" name="name" required>
      <UInput v-model="formState.name" placeholder="Enter profile name" />
    </UFormField>

    <UFormField
      label="Select an integration to use"
      name="integration_id"
      required
    >
      <USelect
        v-model="formState.integration_id"
        placeholder="Select an item..."
        :loading="isIntegrationsFetching"
        :items="mappedIntegrations"
        :disabled="!isCreateMode"
        class="w-full"
      />
    </UFormField>

    <UFormField
      label="Select the asset type you want to import"
      name="asset_type_id"
      required
    >
      <USelect
        v-model="formState.asset_type_id"
        placeholder="Select an item..."
        :loading="isAssetTypesFetching"
        :items="mappedAssetTypes"
        :disabled="!isCreateMode"
        class="w-full"
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Next</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="outline"
        block
        @click="handleSubmit(true)"
      >
        <p>Save & Go Back to Overview</p>
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
