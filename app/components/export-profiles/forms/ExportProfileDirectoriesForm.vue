<script setup lang="ts">
import type { ExportProfileDirectoriesFormData } from '~/schemas/export-profiles/export-profile-directories.schema'
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { useExportProfileDirectoriesQuery } from '~/api/export-profiles'
import BaseFileExplorerSelector from '~/components/base/BaseFileExplorerSelector.vue'
import { exportProfileDirectoriesSchema } from '~/schemas/export-profiles/export-profile-directories.schema'

const props = defineProps<{
  id: string
  profile: ExportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ExportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

function getInitialDirectory(path: string | null | undefined): string {
  if (!path || path === '/')
    return '/'
  if (path.endsWith('/'))
    return path

  const segments = path.split('/')
  segments.pop()
  const parentPath = segments.join('/') || '/'
  return parentPath.endsWith('/') ? parentPath : `${parentPath}/`
}

const currentPath = ref(getInitialDirectory(props.profile.path))

const formState = ref<ExportProfileDirectoriesFormData>({
  path: currentPath.value,
  file_name: props.profile.file_name || '',
})

async function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  if (!formState.value.path)
    return

  await formRef.value.submit()

  emit('submit', {
    ...props.profile,
    ...formState.value,
  }, isSaveAndBackToOverview)
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="exportProfileDirectoriesSchema"
    class="space-y-4"
  >
    <UFormField label="Browse to the folder you want to export to:" name="path" required>
      <BaseFileExplorerSelector
        :id="props.id"
        :initial-path="formState.path"
        selection-type="folder"
        :query-fn="useExportProfileDirectoriesQuery"
        @update:path="val => formState.path = val"
      />
    </UFormField>

    <UFormField label="Extension" name="file_name" required>
      <UInput
        v-model="formState.file_name"
        type="text"
        placeholder="xml"
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Next</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="outline"
        block
        @click="handleSubmit(true)"
      >
        <p>Save & Go Back to Overview</p>
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
