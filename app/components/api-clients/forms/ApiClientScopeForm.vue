<script setup lang="ts">
import type { ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import { cloneDeep } from 'lodash-es'
import { apiClientScopeSchema } from '~/schemas/api-client/api-client-form.schema'

const props = defineProps<{
  apiClient: ApiClientFormData
}>()

defineEmits<{
  submit: [data: ApiClientFormData]
  previousStep: []
}>()

const formState: Ref<ApiClientFormData> = ref(cloneDeep(props.apiClient))

watch(() => props.apiClient, (newApiClient) => {
  formState.value = cloneDeep(newApiClient)
}, { immediate: true })
</script>

<template>
  <UForm
    :state="formState"
    :schema="apiClientScopeSchema"
    class="space-y-6"
    @submit="$emit('submit', formState)"
  >
    <h2 class="text-lg font-bold">
      Scope Configuration
    </h2>

    <UFormField label="Token Name" name="tokenName" required>
      <UInput v-model="formState.tokenName" placeholder="Enter token name" size="lg" />
    </UFormField>

    <UFormField label="Expiration Settings" name="hasExpiration">
      <div class="space-y-3">
        <UCheckbox
          v-model="formState.hasExpiration"
          label="Set expiration date"
        />
        <UFormField v-if="formState.hasExpiration" name="expiration_date" label="Expiration Date" required>
          <UInput
            v-model="formState.expiration_date"
            type="date"
            placeholder="Select expiration date"
            size="lg"
            class="w-64"
          />
        </UFormField>
      </div>
    </UFormField>

    <UFormField label="API Permissions" name="permissions" required>
      <div class="space-y-4">
        <div>
          <UCheckbox
            id="readSupportingData"
            v-model="formState.permissions.readReferenceData"
            label="Read reference data"
          />
          <p class="text-sm text-gray-500 ml-6 mt-1">
            Grants the ability to pull reference data from Hyperfox.
          </p>
        </div>

        <div>
          <UCheckbox
            id="writeSupportingData"
            v-model="formState.permissions.writeReferenceData"
            label="Write reference data"
          />
          <p class="text-sm text-gray-500 ml-6 mt-1">
            Grants the ability to create, update or delete supporting data in Hyperfox.
          </p>
        </div>
      </div>

      <div>
        <UCheckbox
          id="readOrderData"
          v-model="formState.permissions.readOrderData"
          label="Read order data"
        />
        <p class="text-sm text-gray-500 ml-6 mt-1">
          Grants the ability to pull order data from Hyperfox.
        </p>
      </div>

      <div>
        <UCheckbox
          id="writeOrderData"
          v-model="formState.permissions.writeOrderData"
          label="Write order data"
        />
        <p class="text-sm text-gray-500 ml-6 mt-1">
          Grants the ability to write order data to Hyperfox.
        </p>
      </div>
    </UFormField>

    <UButton type="submit" size="lg" color="neutral" block class="mt-4">
      <p>Save</p>
      <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
    </UButton>

    <div class="w-full flex justify-center">
      <UButton type="button" size="lg" color="neutral" variant="link" @click="$emit('previousStep')">
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
