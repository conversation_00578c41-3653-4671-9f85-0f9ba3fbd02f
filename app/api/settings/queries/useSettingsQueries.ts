import type { ModelContract } from '~/utils/environment/model.contract'
import { useQuery } from '@tanstack/vue-query'
import { ModelFactory } from '~/utils/environment/model.factory'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { settingsKeys } from '../keys/settings.keys'

export function useSettingsQuery() {
  const query = useQuery({
    ...settingsKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })

  const environment = computed<ModelContract | undefined>(() => {
    if (query.data.value) {
      return new ModelFactory().create(query.data.value.defaultModel)
    }
    return undefined
  })

  return {
    ...query,
    environment,
  }
}
