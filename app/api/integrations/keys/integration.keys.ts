import { createQueryKeys } from '@lukemorales/query-key-factory'
import { IntegrationService } from '../services/integration.service'

export const integrationKeys = createQueryKeys('integration', {
  all: {
    queryKey: null,
    queryFn: () => IntegrationService.getAll(),
  },
  allAvailable: {
    queryKey: null,
    queryFn: () => IntegrationService.getALlAvailable(),
  },
  byId: (integrationId: string) => ({
    queryKey: [integrationId],
    queryFn: () => IntegrationService.getById(integrationId),
  }),
  byIdOrFail: (integrationId: string) => ({
    queryKey: [integrationId],
    queryFn: () => IntegrationService.getByIdOrFail(integrationId),
  }),
})
