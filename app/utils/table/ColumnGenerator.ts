import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeTableFieldData, BadgeConfigData } from '~/utils/types/api/api'
import { AssetTypeFieldCacheService } from '~/api/asset-type-fields/services/asset-type-field-cache.service'
import { BadgeColumn } from '~/utils/table/BadgeColumn'
import { DateTimeColumn } from '~/utils/table/DateTimeColumn'
import { RelationshipColumn } from '~/utils/table/RelationshipColumn'
import { TextColumn } from '~/utils/table/TextColumn'
import {
  AssetTypeTableFieldType,
  BadgeConfigMode,
} from '~/utils/types/api/api'

export class ColumnGenerator<T> {
  public generateColumns(
    tableFields: AssetTypeTableFieldData[],
      dynamicRelationships: boolean = false,
      sortable: boolean = false,
  ): BaseColumn<T>[] {
    return tableFields.map((tableField) => {
      return this.generateColumn(tableField, dynamicRelationships, sortable)
    })
  }

  private generateColumn(
    tableField: AssetTypeTableFieldData,
    dynamicRelationships: boolean,
    sortable: boolean,
  ): BaseColumn<T> {
    const fieldName = this.resolveColumnFieldName(tableField) as keyof T

    if (dynamicRelationships && tableField.asset_type_relationship_id) {
      return new RelationshipColumn(fieldName, tableField.label, false, tableField)
    }

    switch (tableField.type) {
      case AssetTypeTableFieldType.Badge:
        return new BadgeColumn(fieldName, tableField.label, this.convertToBadgeConfig(tableField), sortable)

      case AssetTypeTableFieldType.DateTime:
        return new DateTimeColumn(fieldName, tableField.label, sortable)

      case AssetTypeTableFieldType.Date:
        return new DateTimeColumn(fieldName, tableField.label, sortable, true)

      default:
        return new TextColumn(fieldName, tableField.label, sortable)
    }
  }

  private resolveColumnFieldName(tableField: AssetTypeTableFieldData): string {
    const field = AssetTypeFieldCacheService.getByIdOrFail(tableField.asset_type_field_id)
    const fieldName = field?.field

    // if (tableField.asset_type_relationship_id) {
    //   const relationship = useAssetTypeRelationshipStore().getById(tableField.asset_type_relationship_id);
    //   fieldName = `${useSnakeCase(relationship?.relationship_function_name)}.${fieldName}`;
    // }

    return fieldName ?? ''
  }

  private convertToBadgeConfig(tableField: AssetTypeTableFieldData): BadgeConfigData {
    if (!tableField.badge_config) {
      return { mode: BadgeConfigMode.Status, statuses: [], thresholds: [] }
    }
    return {
      ...tableField.badge_config,
    }
  }
}
