import type { DirectoryData, ImportProfileData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { importProfileKeys } from '../keys/import-profile.keys'
import { ImportProfileService } from '../services/import-profile.service'

export function useImportProfilesQuery() {
  return useQuery({
    ...importProfileKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useImportProfileByIdQuery(
  id: string,
  options?: ComposableQueryOptions<ImportProfileData, typeof importProfileKeys.byId>,
) {
  return useQuery({
    ...importProfileKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useImportProfileByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<ImportProfileData, typeof importProfileKeys.byIdOrFail>,
) {
  return useQuery({
    ...importProfileKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useImportProfileFilesQuery(
  id: string,
  path: MaybeRefOrGetter<string>,
) {
  return useQuery<DirectoryData>({
    queryKey: ['importProfile', 'byId', id, 'files', path],
    queryFn: () => ImportProfileService.files(id, toValue(path)),
    staleTime: CACHE_CONFIG.IMMEDIATE,
  })
}

export function useImportProfileRecordsQuery(id: string) {
  return useQuery({
    ...importProfileKeys.records(id),
    staleTime: CACHE_CONFIG.IMMEDIATE,
  })
}
