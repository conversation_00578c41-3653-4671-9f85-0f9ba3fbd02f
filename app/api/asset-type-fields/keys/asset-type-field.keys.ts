import { createQueryKeys } from '@lukemorales/query-key-factory'
import { AssetTypeFieldCacheService } from '../services/asset-type-field-cache.service'
import { AssetTypeFieldService } from '../services/asset-type-field.service'

export const assetTypeFieldKeys = createQueryKeys('assetTypeFields', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeFieldService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeFieldCacheService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeFieldCacheService.getByIdOrFail(id),
  }),
  byAssetTypeId: (assetTypeId: string) => ({
    queryKey: [assetTypeId],
    queryFn: () => AssetTypeFieldCacheService.getByAssetTypeId(assetTypeId),
  }),
})
