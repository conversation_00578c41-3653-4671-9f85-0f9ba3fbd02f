import { useUserInfo } from '~/api/user'
import { getUserFullName } from '~/utils/user/get-user-full-name'

export function useIntercom() {
  const { data: userData } = useUserInfo()

  const initializeIntercom = (userData: {
    id: string
    name: string
    email: string
    company: string
  }) => {
    if (!import.meta.client) {
      return
    }

    (window as any).intercomSettings = {
      api_base: 'https://api-iam.intercom.io',
      app_id: 'ktdgvohf',
      user_id: userData.id,
      name: userData.name,
      email: userData.email,
      company: userData.company,
      hide_default_launcher: true,
      custom_launcher_selector: '#hf_chat_button',
    }

    const w = window as any
    const ic = w.Intercom

    if (typeof ic === 'function') {
      ic('reattach_activator')
      ic('update', w.intercomSettings)
    }
    else {
      const d = document

      interface IntercomFunction {
        (...args: any[]): void
        q: any[]
        c: (args: any) => void
      }

      const i = function (this: any, ...args: any[]) {
        i.c(args)
      } as IntercomFunction

      i.q = []
      i.c = function (args: any) {
        i.q.push(args)
      }

      w.Intercom = i

      const l = function () {
        const s = d.createElement('script')
        s.type = 'text/javascript'
        s.async = true
        s.src = 'https://widget.intercom.io/widget/ktdgvohf'

        const scriptTags = d.getElementsByTagName('script')
        if (scriptTags.length > 0) {
          const x = scriptTags[0]
          if (x && x.parentNode) {
            x.parentNode.insertBefore(s, x)
          }
          else {
            d.head.appendChild(s)
          }
        }
        else {
          d.head.appendChild(s)
        }
      }

      if (document.readyState === 'complete') {
        l()
      }
      else if (w.attachEvent) {
        w.attachEvent('onload', l)
      }
      else {
        w.addEventListener('load', l, false)
      }
    }
  }

  const setupIntercomWithUserData = async () => {
    if (!import.meta.client)
      return

    try {
      const { hydrate: hydrateTenant, tenant } = useTenantStore()
      await hydrateTenant()

      watch(userData, (userInfo) => {
        if (!userInfo) {
          return
        }

        initializeIntercom({
          id: userInfo.email || userInfo.id,
          name: getUserFullName(userInfo),
          email: userInfo.email,
          company: tenant?.key || 'No company info',
        })
      }, { immediate: true })
    }
    catch (error) {
      console.warn('Failed to initialize Intercom with user data:', error)
    }
  }

  return {
    initializeIntercom,
    setupIntercomWithUserData,
  }
}
