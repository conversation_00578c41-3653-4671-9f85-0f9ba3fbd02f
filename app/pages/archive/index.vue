<script setup lang="ts">
import type { TableColumn } from '#ui/types'
import { AssetQueryFactory } from '~/api/assets'
import QueryData from '~/utils/query/QueryData'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { ColumnGenerator } from '~/utils/table/ColumnGenerator'
import { DynProcessingStatus } from '~/utils/types/api/api'

definePageMeta({
  middleware: ['auth'],
})

// Store imports
const settingsStore = useSettingsStore()
const assetTypeStore = useAssetTypeStore()
const assetTypeTableFieldStore = useAssetTypeTableFieldStore()

const pageNumber = ref(1)
const assetType = assetTypeStore.getByTableName(settingsStore.settings?.defaultModel ?? '')
const tableFields = assetTypeTableFieldStore.getByAssetTypeId(assetType.id)
const generatedColumns = [...new ColumnGenerator().generateColumns(tableFields), new ActionColumn<any>('Actions', [
  {
    label: 'View',
    onClick: async (row) => {
      await navigateTo(`/archive/${row.id}`)
    },
  },
])]
const columnConfig: TableColumn<any>[] = generatedColumns.map(column => column.getConfig())

const query: Ref<QueryData> = shallowRef(new QueryData())

query.value.setFilters({ processing_status: [
  DynProcessingStatus.VALIDATED,
  DynProcessingStatus.REJECTED,
  DynProcessingStatus.SUBMITTED,
  DynProcessingStatus.COMPLETED,
] })
query.value.setSort([{ desc: true, id: 'updated_at' }])

async function pageChanged() {
  const newQuery = useClone(query.value)
  newQuery.setPage(pageNumber.value)

  query.value = newQuery
}

const { useAssetTableDataQuery } = AssetQueryFactory
  .getQueryInstance(assetType?.table_name)
  .getQueries()

const { data: assetData, isFetching: isFetchingAssets } = useAssetTableDataQuery(query)
</script>

<template>
  <div v-if="!isFetchingAssets && assetData" class="h-full flex flex-col gap-4 sm:gap-6 flex-1 overflow-y-auto p-4 sm:p-6">
    <UTable
      :data="assetData.data"
      :columns="columnConfig"
    />
    <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
      <div class="text-sm text-(--ui-text-muted)">
        <GeneralEntries :meta-data="assetData.meta" />
      </div>
      <div class="flex items-center gap-1.5">
        <UPagination
          v-model:page="pageNumber"
          class="justify-center"
          :total="assetData.meta.total"
          @click="pageChanged"
        />
      </div>
    </div>
  </div>
</template>
