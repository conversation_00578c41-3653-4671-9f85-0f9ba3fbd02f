import type { ValidateAction } from '~/utils/validate/validate-action'
import { FetchError } from 'ofetch'
import { isDataContainableAction } from '~/utils/validate/validate-action'

export class ValidateActionExecutor {
  constructor(private readonly action: any) {
  }

  execute = async (
    id: string,
    action: ValidateAction,
    showSuccess: boolean = true,
    showError: boolean = true,
  ): Promise<boolean> => {
    const data = this.getData(action)
    const toast = useToast()

    try {
      await this.action({
        id,
        action: action.action,
        data,
      })
      if (showSuccess) {
        toast.add({ title: action.successMessage, color: 'success' })
      }
      return true
    }
    catch (e: any) {
      if (showError && e instanceof FetchError) {
        toast.add({
          title: 'Error',
          description: e.data?.message || 'An error occurred',
          color: 'error',
        })
      }
      throw e
    }
  }

  private getData = (action: ValidateAction): any | undefined => {
    if (isDataContainableAction(action)) {
      return action.data
    }
    return undefined
  }
}
