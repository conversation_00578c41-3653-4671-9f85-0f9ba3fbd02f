import type { ColumnDef } from '@tanstack/vue-table'
import type { AssetTypeTableFieldData } from '~/utils/types/api/api'
import { TablesRendererTypesRelationship } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'

export class RelationshipColumn<T> extends BaseColumnWithField<T> {
  constructor(
    field: keyof T,
    label: string,
      sortable: boolean = false,
      private tableField: AssetTypeTableFieldData,
  ) {
    super(field, label, sortable, ColumnType.Relationship, undefined)
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesRelationship, {
        rowData: row.original,
        tableField: this.tableField,
      }),
    }
  }
}
