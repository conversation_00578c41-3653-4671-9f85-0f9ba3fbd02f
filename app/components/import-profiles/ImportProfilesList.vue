<script setup lang="ts">
import type { ImportProfileData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import { useRunImportProfileMutation } from '~/api/import-profiles'
import { getNextExecution } from '~/utils/cron/next-cron-execution.calculator'
import { DateFormat } from '~/utils/dates/date.format'
import { isImportProfileReady } from '~/utils/import-profiles/import-profile-status.checker'

defineProps<{
  profiles: ImportProfileData[]
}>()

const emit = defineEmits<{
  configureProfile: [profile: ImportProfileData]
  deleteProfile: [profileId: string]
}>()

function configureProfile(profile: ImportProfileData) {
  emit('configureProfile', profile)
}

const router = useRouter()
function handleErrorLog() {
  router.push({ path: '/admin/logs' })
}

async function showDeleteConfirmation(profile: ImportProfileData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: 'Delete Profile',
    description: `Are you sure you want to delete the profile "${profile.name}"? This action cannot be undone.`,
  })

  if (!await overlay.result)
    return

  emit('deleteProfile', profile.id)
}

const { mutate: runImportProfile } = useRunImportProfileMutation()
const profileCooldowns = ref<Record<string, boolean>>({})

function runWithCooldown(profile: ImportProfileData) {
  const { id: profileId, name } = profile

  if (profileCooldowns.value[profileId])
    return

  runImportProfile(profileId)

  useToast().add({
    title: 'Profile run initiated',
    description: `The profile "${name}" is now running.`,
    color: 'success',
  })

  profileCooldowns.value[profileId] = true

  setTimeout(() => {
    profileCooldowns.value[profileId] = false
  }, 1000)
}

const dayjs = useDayjs()

function getLastRanAt(profile: ImportProfileData) {
  return profile.last_ran_at
    ? dayjs(profile.last_ran_at).format(DateFormat.DD_MM_YYYY_HH_MM_SLASH_DASH)
    : '/'
}
</script>

<template>
  <div>
    <h2 class="font-bold mb-4">
      Profiles
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="profile in profiles"
        :key="profile.id"
        class="flex items-start justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-2">
          <p class="font-bold flex items-center">
            <slot name="profile-name" :profile="profile">
              {{ profile.name }}
            </slot>
          </p>
          <div
            v-if="!isImportProfileReady(profile)"
            class="flex items-start gap-2 text-sm text-gray-500"
          >
            <UIcon name="i-lucide-info" class="bg-error-500 w-[18px] h-[18px] flex-shrink-0" />
            <p>
              This profile is not ready for import yet. Please configure it further.
            </p>
          </div>
          <div v-else class="flex flex-col gap-1">
            <p class="text-sm text-gray-500">
              Last run: {{ getLastRanAt(profile) }}
            </p>
            <p class="text-sm text-gray-500">
              Next run: {{ getNextExecution({ cronExpression: profile.schedule }) ?? '/' }}
            </p>
            <div
              class="flex items-center -ml-2"
            >
              <UButton
                variant="link"
                color="primary"
                class="text-sm ml-2 underline px-0 py-0 h-auto min-h-0"
                label="Run now"
                :disabled="profileCooldowns[profile.id]"
                @click="runWithCooldown(profile)"
              />
              <UButton
                variant="link"
                color="primary"
                class="text-sm ml-2 underline px-0 py-0 h-auto min-h-0"
                label="View error log"
                @click="handleErrorLog"
              />
            </div>
          </div>
        </div>
        <div class="flex gap-2 ml-10">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            label="Configure"
            @click="configureProfile(profile)"
          />
          <UButton
            variant="link"
            color="primary"
            class="text-sm ml-2 px-0 py-0 h-auto min-h-0"
            label="Delete"
            @click="showDeleteConfirmation(profile)"
          />
        </div>
      </div>
      <p v-if="!profiles?.length" class="p-4 text-center text-gray-500">
        No configured profiles found.
      </p>
    </UPageCard>
  </div>
</template>
