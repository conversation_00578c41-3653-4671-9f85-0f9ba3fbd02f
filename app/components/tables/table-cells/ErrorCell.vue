<script setup lang="ts">
const props = defineProps<{
  config?: {
    hasError?: (row: any) => boolean
  }
  row: Record<string, any>
}>()

const hasError = computed(() => props.config?.hasError?.(props.row) ?? false)
</script>

<template>
  <div class="flex items-center">
    <UIcon
      v-if="hasError"
      size="large"
      class="text-error-500"
      name="i-ph-warning"
    />
  </div>
</template>
