import type { UseQueryOptions } from '@tanstack/vue-query'

export type ExtractQueryKey<T extends (...args: any[]) => { queryKey: any }>
  = ReturnType<T>['queryKey']

export type ComposableQueryOptions<
  TQueryFnData,
  TKeyFactory extends (...args: any[]) => { queryKey: any },
  TData = TQueryFnData,
> = Omit<
  UseQueryOptions<TQueryFnData, unknown, TData, ExtractQueryKey<TKeyFactory>>,
  'queryKey' | 'queryFn'
>
