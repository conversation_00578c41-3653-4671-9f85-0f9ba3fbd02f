<script setup lang="ts">
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useAssetTypeFormFieldsByAssetTypeQuery } from '~/api/asset-type-form-fields/queries/useAssetTypeFormFieldQueries'
import { AssetQueryFactory } from '~/api/assets'
import { FormGenerator } from '~/utils/forms/FormGenerator'

definePageMeta({
  middleware: ['auth'],
})

// Component setup
const route = useRoute()

const settingsStore = useSettingsStore()
const assetTypeStore = useAssetTypeStore()
const orderInquiryStore = useOrderInquiryStore()

const orderInquiry = ref()
const assetId = route.params.id as string

const defaultModel = settingsStore.settings?.defaultModel ?? ''

const assetType = assetTypeStore.getByTableName(defaultModel)

const { data: formFields } = useAssetTypeFormFieldsByAssetTypeQuery(assetType.id)

const { data: assetTypeFields } = useAssetTypeFieldsQuery()

const formDefinition = computed<FormDefinition | undefined>(() => {
  return formFields.value && assetTypeFields.value
    ? new FormGenerator(assetTypeFields.value ?? []).generateFormDefinition(formFields.value)
    : undefined
})

const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(defaultModel)
  .getQueries()

const { data: assetData, isFetching: isFetchingAsset } = useAssetQuery(
  assetId,
  { include: settingsStore.environment?.includedRelationships },
)

const editableData = ref(assetData.value)

const isLoadingInquiry = ref(true)

watch(assetData, async (newValue) => {
  if (!newValue) {
    return
  }

  editableData.value = newValue

  orderInquiry.value = await orderInquiryStore.fetchById(assetData.value.order_inquiry_id)

  isLoadingInquiry.value = false
}, { immediate: true })
</script>

<template>
  <div v-if="!isLoadingInquiry && !isFetchingAsset" class="flex flex-col overflow-hidden h-full">
    <UDashboardToolbar>
      <template #left>
        <UNavigationMenu
          :items="[
            { label: 'Order', value: 'order', active: true },
          ]"
          highlight
        />
      </template>
    </UDashboardToolbar>
    <div class="grid grid-cols-2 mx-8 h-full">
      <div class="col-span-1 bg-neutral-50 p-4">
        <OrdersOrderInquiry :order-inquiry="orderInquiry.data" />
      </div>
      <div class="col-span-1  p-4">
        <FormsRendererForm
          v-if="formDefinition"
          v-model="assetData"
          :form-definition="formDefinition"
          submit-label="Save"
          :is-sub-form="true"
          :is-read-only="true"
        />
        <UCard v-else>
          <p>No form fields defined for this master data.</p>
        </UCard>
      </div>
    </div>
  </div>
</template>
