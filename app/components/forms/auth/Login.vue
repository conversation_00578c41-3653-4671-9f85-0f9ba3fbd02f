<script setup lang="ts">
import type { FetchError } from 'ofetch'

const data = ref({
  email: '',
  password: '',
  remember: false,
})
const localError = ref<string>()
const isPending = ref(false)

async function onLogin() {
  try {
    localError.value = undefined
    isPending.value = true
    // Commented out for new auth
    // If we ever need to use Sanctum again, uncomment this
    // await useSanctumAuth().login(data.value);
  }
  catch (e) {
    const error = e as FetchError
    const errors = error.data?.errors || {}
    localError.value = Object.values(errors)
      .flat()
      .join(' ') || 'An error occurred'
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <form class="flex flex-col gap-4" @submit.prevent="onLogin">
    <UFormField label="Email">
      <UInput v-model="data.email" placeholder="Email" />
    </UFormField>

    <UFormField label="Password">
      <GeneralPasswordInput v-model="data.password" />
    </UFormField>

    <UFormField>
      <UCheckbox v-model="data.remember" label="Remember me" />
    </UFormField>

    <UButton
      type="submit"
      :loading="isPending"
      :disabled="isPending"
    >
      Login
    </UButton>
  </form>
  <div v-if="localError">
    <UAlert
      color="error"
      variant="subtle"
      :description="localError"
      icon="i-ph-warning"
    />
  </div>
</template>
