<script setup lang="ts">
import type { ImportProfileData } from '~/utils/types/api/api'
import { useDeleteImportProfileMutation } from '~/api/import-profiles/mutations/useImportProfileMutations'
import { useImportProfilesQuery } from '~/api/import-profiles/queries/useImportProfileQueries'
import { isImportProfileReady, isValidImportProfileStatus } from '~/utils/import-profiles/import-profile-status.checker'
import { IMPORT_PROFILE_STATUSES } from '~/utils/import-profiles/import-profile-statusses'

definePageMeta({
  layout: 'admin',
})

const {
  data: profiles,
  isFetching: isFetchingProfiles,
} = useImportProfilesQuery()

const router = useRouter()

function configureProfile(profile: ImportProfileData) {
  const step = profile.status === IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING
    ? IMPORT_PROFILE_STATUSES.MAPPING
    : profile.status

  const status = isValidImportProfileStatus(step)
    ? isImportProfileReady(profile)
      ? IMPORT_PROFILE_STATUSES.BASIC_INFORMATION
      : step
    : IMPORT_PROFILE_STATUSES.BASIC_INFORMATION

  router.push({
    path: `/admin/import-profiles/${profile.id}`,
    query: { step: status },
  })
}

const deleteImportProfile = useDeleteImportProfileMutation()

async function deleteProfile(profileId: string) {
  await deleteImportProfile.mutateAsync(profileId)

  useToast().add({
    title: 'Import profile deleted',
    description: 'The import profile has been successfully deleted.',
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Import Profiles">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            Import Profiles
          </h1>
          <p class="text-gray-500 mt-3">
            Keep your data up-to-date with automated imports
          </p>
        </div>
        <UButton
          label="Create import profile"
          size="lg"
          @click="$router.push('/admin/import-profiles/new')"
        />
      </div>
      <ImportProfilesList
        v-if="!isFetchingProfiles && profiles"
        :profiles="profiles"
        @configure-profile="configureProfile"
        @delete-profile="deleteProfile"
      />
    </template>
  </UDashboardPanel>
</template>
