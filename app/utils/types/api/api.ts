export enum AgentType {
  TransportOrder = 'transport_order',
}
export interface ApiClientData {
  id: string
  name: string
  expiration_date: string | null
  scopes: Array<any>
  key: string
  secret: string | null
}
export enum ApiClientScope {
  READ_REFERENCE_DATA = 'read:reference_data',
  WRITE_REFERENCE_DATA = 'write:reference_data',
  READ_VALIDATED_ORDER = 'read:validated_order',
  READ_ORDER_DATA = 'read:order_data',
  WRITE_ORDER_DATA = 'write:order_data',
}
export interface ApiConfiguration {
  webservice_url: string
  webservice_auth_method: WebserviceAuthType
  webservice_username?: string
  webservice_password?: string
  webservice_ntlm?: boolean
}
export interface ApiImportProfileData {
  page_parameter: string | null
  page_start_index: number | null
  page_step_size: number | null
  page_size_parameter: string | null
  page_size_length: number | null
  data_wrapper: string | null
  additional_parameters: string | null
}
export interface AssetTypeData {
  id: string
  name: string
  table_name: string
  label: string
  display_field_id: string | null
  auditing_since: string | null
  supporting_data: boolean
  locked: boolean
}
export interface AssetTypeFieldData {
  id: string
  asset_type_id: string
  label: string
  field: string
  type: AssetTypeFieldType
  precision: number | null
  nullable: boolean
  locked: boolean
  required: boolean
  searchable: boolean
  enum_values: Array<any> | null
  weight: number
}
export enum AssetTypeFieldType {
  Boolean = 'boolean',
  Date = 'date',
  Time = 'time',
  DateTime = 'dateTime',
  Decimal = 'decimal',
  LongText = 'longText',
  Integer = 'integer',
  String = 'string',
  Text = 'text',
  Accuracy = 'accuracy',
  Enum = 'enum',
  Array = 'array',
}
export interface AssetTypeFormFieldData {
  id: string
  asset_type_id: string
  asset_type_field_id: string | null
  asset_type_relationship_id: string | null
  form: AssetTypeFormFieldForm
  type: AssetTypeFormFieldType
  required: boolean
  locked: boolean
  crud: boolean
  order: number
  span: number
  template: string | null
  filterable: boolean
}
export enum AssetTypeFormFieldForm {
  Edit = 'edit',
}
export enum AssetTypeFormFieldType {
  Checkbox = 'checkbox',
  Date = 'date',
  Time = 'time',
  DateTime = 'datetime',
  Number = 'number',
  Textarea = 'textarea',
  Text = 'text',
  Select = 'select',
  AdvancedMultilineSelect = 'advancedmultilineselect',
  AdvancedSelect = 'advancedselect',
  ManyForm = 'manyform',
  Table = 'table',
}
export interface AssetTypeIndexData {
  id?: string
  asset_type_id: string
  asset_type_field_id: string
  name: string
  type: AssetTypeIndexType
  locked: boolean
}
export enum AssetTypeIndexType {
  Index = 'Index',
  Unique = 'Unique',
}
export interface AssetTypeInfoApiData {
  field: string
  type: string
  nullable: boolean
  required: boolean
}
export enum AssetTypeInternalFieldType {
  ForeignUuid = 'foreignUuid',
}
export interface AssetTypeRelationshipData {
  id: string
  asset_type_id: string
  inverse_relationship_id: string | null
  label: string
  relationship_function_name: string
  relationship_method: AssetTypeRelationshipMethod
  relationship_asset_type_id: string | null
  relationship_key: string
  nullable: boolean
  include: boolean
  locked: boolean
  cascade_delete: boolean
  required: boolean
}
export enum AssetTypeRelationshipMethod {
  BelongsTo = 'belongsTo',
  HasOne = 'hasOne',
  HasMany = 'hasMany',
}
export interface AssetTypeTableFieldData {
  id?: string
  asset_type_id: string
  asset_type_field_id: string
  asset_type_relationship_id: string | null
  type: AssetTypeTableFieldType
  label: string
  locked: boolean
  weight: number
  order: number
  badge_config: BadgeConfigData | null
  suffix: string | null
  filterable: boolean
  template: string | null
}
export enum AssetTypeTableFieldType {
  Text = 'text',
  Numeric = 'numeric',
  Badge = 'badge',
  Date = 'date',
  DateTime = 'datetime',
  AdvancedMultilineSelect = 'advancedmultilineselect',
}
export interface AttachmentData {
  id: string
  filename: string
  path: string
  mime_type: string
  disk: string
  attachable_type: string
  attachable_id: string
}
export interface AvailableChannelData {
  name: string
}
export interface AvailableIntegrationData {
  name: string
}
export interface BadgeConfigData {
  mode: BadgeConfigMode
  statuses: Array<StatusConfigData> | null
  thresholds: Array<ThresholdConfigData> | null
  suffix?: string | null
}
export enum BadgeConfigMode {
  Status = 'status',
  Threshold = 'threshold',
}
export interface BatchOrderInquiryData {
  orderInquiryIds: Array<string>
}
export interface ChannelData {
  id: string
  label: string
  type: string
  data: any
  last_ran_at: string | null
  is_active: boolean
}
export enum ChannelProvider {
  MYNUMA = 'mynuma',
}
export interface CreateAssetTypeData {
  label: string
  auditing_enabled: boolean
  supporting_data: boolean
  locked: boolean
}
export interface CreateAssetTypeRelationshipData {
  id?: string
  label: string
  relationship_method: AssetTypeRelationshipMethod
  relationship_asset_type_id: string | null
  relationship_class: string | null
  relationship_key: string | null
  inverse_relationship_id: string | null
  nullable: boolean
  include: boolean
  locked: boolean
  cascade_delete: boolean
  required: boolean
}
export interface CreateChannelData {
  label: string
  provider: ChannelProvider
  data: object | Array<any>
  is_active: boolean
}
export interface CreateExportProfileData {
  name: string
  integration_id: string
}
export interface CreateImportData {
  asset_type_id: string
  file: any
  delimiter: string
}
export interface CreateImportProfileData {
  name: string
  integration_id: string
  asset_type_id: string
}
export interface CreateIntegrationData {
  label: string
  provider: IntegrationProvider
  data: object | Array<any>
}
export interface CreateUserData {
  email: string
  first_name: string
  last_name: string
  password: string
}
export interface CreateWebhookData {
  name: string
  event: WebhookEvent
  target_url: string
}
export interface DirectoryData {
  path: string
  items: Array<FileSystemItemData>
}
export enum DynProcessingStatus {
  PENDING_VALIDATION = 'pending_validation',
  SAVED = 'saved',
  VALIDATED = 'validated',
  REJECTED = 'rejected',
  SUBMITTED = 'submitted',
  SUBMIT_FAILED = 'submit_failed',
  COMPLETED = 'completed',
}
export interface EditAssetTypeFieldData {
  locked: boolean
  required: boolean
  searchable: boolean
  weight: number
}
export interface EmailOrderData {
  id: string
  from: string
  to: string
  subject: string | null
  mail_body_text: string | null
  mail_body_html: string | null
  raw_email_attachment_id: string
  attachments?: Array<AttachmentData>
  created_at: any | null
  updated_at: any | null
}
export enum EncodingType {
  UTF_8 = 'UTF-8',
  ISO_8859_1 = 'ISO-8859-1',
  ISO_8859_15 = 'ISO-8859-15',
  ISO_8859_2 = 'ISO-8859-2',
}
export interface ExportProfileData {
  id: string
  status: string
  name: string
  integration_id: string
  crlf: boolean
  path: string | null
  file_name: string | null
  template: string | null
}
export interface FileSystemItemData {
  name: string
  type: FileType
}
export enum FileType {
  DIRECTORY = 'directory',
  FILE = 'file',
}
export interface FtpConfiguration {
  ftp_protocol: FtpProtocol
  ftp_host: string
  ftp_port: number
  ftp_username: string
  ftp_password: string | null
  ftp_encryption: FtpEncryption
  ftp_private_key: string | null
  ftp_passphrase: string | null
  ftp_passive_mode: boolean
  base_folder: string
}
export enum FtpEncryption {
  NONE = 'none',
  SSL = 'ssl',
  TLS = 'tls',
  AUTO = 'auto',
}
export enum FtpProtocol {
  FTP = 'ftp',
  SFTP = 'sftp',
}
export interface ImportData {
  id: string
  asset_type_id: string
  status: ImportStatus
  mapping: MappingWrapperData | null
  unique_field_id: string | null
  failed_reason: string | null
}
export interface ImportProfileData {
  id: string
  status: string
  name: string
  integration_id: string
  asset_type_id: string
  path: string | null
  config: ApiImportProfileData | null
  mapping: Array<MappingItemData> | null
  schedule: string | null
  encoding: EncodingType | null
  last_ran_at: any | null
}
export enum ImportStatus {
  Pending = 'Waiting for completion',
  Ready = 'Waiting on job',
  Processing = 'Processing',
  Finished = 'Ready',
  Failed = 'Failed',
}
export interface IntegrationData {
  id: string
  label: string
  type: string
  data: any
  created_at: any | null
  updated_at: any | null
}
export enum IntegrationProvider {
  FTP = 'ftp',
  API = 'api',
}
export interface LogData {
  id: number
  module: string
  service: string
  reason: string
  technical_reason: string | null
  severity: LogSeverity
  created_at: string
  updated_at: string
}
export enum LogSeverity {
  INFO = 'info',
  ERROR = 'error',
}
export interface MappingData {
  from: string
  to: string | null
}
export interface MappingItemData {
  from: string
  to: string | null
  skip: boolean
  mapping: { [key: string]: string } | null
}
export interface MappingWrapperData {
  mappings: Array<MappingData>
}
export interface MetricData {
  type: string
  description: string
  value: any
}
export interface MetricsData {
  metrics: Array<MetricData>
}
export enum MetricsPeriod {
  TODAY = 'today',
  LAST_7_DAYS = 'last_7_days',
  CURRENT_MONTH = 'current_month',
  CUSTOM = 'custom',
}
export interface MynumaData {
  token: string
  aiMapping: string
}
export interface OrderInquiryData {
  id: string
  state: string
  channel_id: string
  payload_id: string | null
  payload_type: string | null
  payload: EmailOrderData | string | null
  created_at: any
  channel: ChannelData | null
  emailOrder: EmailOrderData | null
  aiResponse: string | null
}
export enum RecipeModelType {
  TRANSPORT = 'Transport order',
  STOCK = 'Stock order',
}
export interface SettingsData {
  recipeModel: RecipeModelType | null
  recipeVersion: string | null
  defaultModel: string | null
  autoProcess: boolean
  renderOfficeDocuments: boolean
}
export interface StatusConfigData {
  name: string
  color: string
}
export interface StatusMessage {
  status: string
  message: string
}
export interface TenantData {
  key: string
  name: string
  domains: Array<TenantDomainData> | null
}
export interface TenantDomainData {
  domain: string
}
export interface TestIntegrationData {
  provider: IntegrationProvider
  data: object | Array<any>
}
export interface ThresholdConfigData {
  min: number
  color: string
}
export interface UpdateAssetTypeData {
  display_field_id: string
  supporting_data: boolean
  locked: boolean
}
export interface UpdateAssetTypeRelationshipData {
  nullable: boolean
  include: boolean
  locked: boolean
  cascade_delete: boolean
  required: boolean
}
export interface UpdateChannelData {
  label: string
  data: object | Array<any>
  is_active: boolean
}
export interface UpdateExportProfileGeneralInfoData {
  name: string
}
export interface UpdateExportProfilePathData {
  path: string
  file_name: string
}
export interface UpdateExportProfileTemplateData {
  template: string
  crlf: boolean
}
export interface UpdateImportData {
  mapping: MappingWrapperData
  ready_for_processing: boolean
  unique_field_id: string
}
export interface UpdateImportProfileConfigurationData {
  path: string | null
  encoding: EncodingType | null
  config: ApiImportProfileData | null
}
export interface UpdateImportProfileGeneralInfoData {
  name: string
}
export interface UpdateImportProfileMappingData {
  mapping: Array<MappingItemData>
}
export interface UpdateImportProfileScheduleData {
  schedule: string
}
export interface UpdateIntegrationData {
  label: string
  data: object | Array<any>
}
export interface UpdateSettingsData {
  autoProcess: boolean
  renderOfficeDocuments: boolean
}
export interface UpdateUserData {
  first_name: string
  last_name: string
  password?: string
}
export interface UpdateWebhookData {
  name: string
  event: WebhookEvent
  target_url: string
}
export interface UpsertApiClientData {
  name: string
  expiration_date: string | null
  scopes: Array<any>
}
export interface UpsertAssetTypeFieldData {
  id?: string
  label: string
  type: AssetTypeFieldType
  precision: number | null
  nullable: boolean
  locked: boolean
  required: boolean
  searchable: boolean
  enum_values: Array<any> | null
  weight: number
}
export interface UpsertAssetTypeFormFieldData {
  id?: string
  asset_type_field_id: string | null
  asset_type_relationship_id: string | null
  form: AssetTypeFormFieldForm
  type: AssetTypeFormFieldType
  span: number
  required: boolean
  locked: boolean
  crud: boolean
  template: string | null
  filterable: boolean
}
export interface UpsertAssetTypeIndexData {
  id?: string
  asset_type_field_id: string
  type: AssetTypeIndexType
  locked: boolean
}
export interface UpsertAssetTypeTableFieldData {
  id?: string
  asset_type_field_id: string
  asset_type_relationship_id: string | null
  type: AssetTypeTableFieldType
  label: string
  locked: boolean
  weight: number
  badge_config: BadgeConfigData | null
  filterable: boolean
  suffix: string | null
  template: string | null
}
export interface UserData {
  id: string
  email: string
  first_name: string
  last_name: string
  status: string
}
export interface WebhookData {
  id: string
  name: string
  event: WebhookEvent
  target_url: string
  secret: string | null
}
export enum WebhookEvent {
  ORDER_VALIDATED = 'order.validated',
}
export enum WebserviceAuthType {
  NONE = 'None',
  BASIC = 'Basic',
  BEARER = 'Bearer',
}
