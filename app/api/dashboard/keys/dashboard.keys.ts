import type { MetricsPeriod } from '~/utils/types/api/api'
import type { CalenderDateRange } from '~/utils/types/dates/daterange.type'
import { createQueryKeys } from '@lukemorales/query-key-factory'
import { DashboardService } from '../services/dashboard.service'

type SerializableDateRange = {
  start: string
  end: string
} | undefined

export const dashboardKeys = createQueryKeys('dashboard', {
  all: (
    period?: MetricsPeriod,
    dateRange?: CalenderDateRange,
  ) => {
    const serializableDateRange: SerializableDateRange = dateRange
      ? {
          start: dateRange.start.toString(),
          end: dateRange.end.toString(),
        }
      : undefined

    return {
      queryKey: [period, serializableDateRange],
      queryFn: () => DashboardService.getAll(period, dateRange),
    }
  },
})
