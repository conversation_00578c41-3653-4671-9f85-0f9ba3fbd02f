<script setup lang="ts">
import type { IntegrationData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'

defineProps<{
  integrations: IntegrationData[]
}>()

const emit = defineEmits<{
  configureIntegration: [integration: IntegrationData]
  deleteIntegration: [integrationId: string]
}>()

function configureIntegration(integration: IntegrationData) {
  emit('configureIntegration', integration)
}

async function showDeleteConfirmation(integration: IntegrationData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: 'Delete Integration',
    description: `Are you sure you want to delete the integration "${integration.label}"? This action cannot be undone.`,
  })

  if (!await overlay.result)
    return

  emit('deleteIntegration', integration.id)
}
</script>

<template>
  <div>
    <h2 class="font-bold mb-4">
      Active integrations
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="integration in integrations"
        :key="integration.id"
        class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-1">
          <p class="font-bold flex items-center">
            <slot name="integration-name" :integration="integration">
              {{ integration.label }}
            </slot>
          </p>
          <p class="text-sm text-gray-500">
            <slot name="integration-description" :integration="integration">
              {{ integration.type.toUpperCase() }}
            </slot>
          </p>
        </div>
        <div class="flex gap-2">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            label="Configure"
            @click="configureIntegration(integration)"
          />
          <UButton
            variant="link"
            color="primary"
            class="text-sm ml-2 px-0 py-0 h-auto min-h-0"
            label="Delete"
            @click="showDeleteConfirmation(integration)"
          />
        </div>
      </div>
      <p v-if="!integrations?.length" class="p-4 text-center text-gray-500">
        No configured integrations found.
      </p>
    </UPageCard>
  </div>
</template>
