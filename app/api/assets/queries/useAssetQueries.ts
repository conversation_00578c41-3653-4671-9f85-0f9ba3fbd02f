import type QueryData from '~/utils/query/QueryData'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { createAssetKeysFactory } from '../keys/asset.keys'
import { AssetService } from '../services/asset.service'

export function createAssetQueriesFactory(table: string) {
  const assetKeys = createAssetKeysFactory(table)

  const useAssetsQuery = (
    queryData?: MaybeRef<QueryData>,
    options?: ComposableQueryOptions<{ queryData: QueryData }, typeof assetKeys.all>,
  ) => {
    return useQuery({
      queryKey: computed(() => {
        const unreffedData = unref(queryData)
        return assetKeys.all(unreffedData).queryKey
      }),
      queryFn: () => {
        const unreffedData = unref(queryData)
        return AssetService.getAll(table, unreffedData)
      },
      staleTime: CACHE_CONFIG.SHORT_TERM,
      ...options,
    })
  }

  const useAssetQuery = (
    id: MaybeRef<string>,
    query?: object,
    options?: ComposableQueryOptions<{ id: string, query?: object }, typeof assetKeys.byId>,
  ) => {
    return useQuery({
      queryKey: computed(() => {
        const unreffedId = unref(id)
        return assetKeys.byId(unreffedId, query).queryKey
      }),
      queryFn: () => {
        const unreffedId = unref(id)
        return AssetService.getById(table, unreffedId, query)
      },
      enabled: computed(() => !!unref(id)),
      ...options,
    })
  }

  const useAssetTableDataQuery = (
    queryData?: MaybeRef<QueryData>,
    options?: ComposableQueryOptions<{ queryData: QueryData }, typeof assetKeys.tableData>,
  ) => {
    return useQuery({
      queryKey: computed(() => {
        const unreffedData = unref(queryData)
        return assetKeys.tableData(unreffedData).queryKey
      }),
      queryFn: () => {
        const unreffedData = unref(queryData)
        return AssetService.getTableData(table, unreffedData)
      },
      staleTime: CACHE_CONFIG.SHORT_TERM,
      ...options,
    })
  }

  const useAssetSearchQuery = (
    queryData?: MaybeRef<QueryData>,
    options?: ComposableQueryOptions<{ queryData: QueryData }, typeof assetKeys.tableData>,
  ) => {
    return useQuery({
      queryKey: computed(() => {
        const unreffedData = unref(queryData)
        return assetKeys.search(unreffedData).queryKey
      }),
      queryFn: () => {
        const unreffedData = unref(queryData)
        return AssetService.search(table, unreffedData)
      },
      staleTime: CACHE_CONFIG.SHORT_TERM,
      ...options,
    })
  }

  return {
    useAssetsQuery,
    useAssetQuery,
    useAssetTableDataQuery,
    useAssetSearchQuery,
  }
}
