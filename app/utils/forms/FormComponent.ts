import {
  FormsRendererInputsHFCheckbox,
  FormsRendererInputsHFInputDate,
  FormsRendererInputsHFInputDateTime,
  FormsRendererInputsHFInputNumber,
  FormsRendererInputsHFInputText,
  FormsRendererInputsHFInputTime,
  FormsRendererInputsHFRelationshipAdvancedMultiLineSelect,
  FormsRendererInputsHFRelationshipAdvancedSelect,
  FormsRendererInputsHFRelationshipManyForm,
  FormsRendererInputsHFRelationshipSelect,
  FormsRendererInputsHFRelationshipTable,
  FormsRendererInputsHFTextarea,
} from '#components'
import { AssetTypeFormFieldType } from '~/utils/types/api/api'

export class FormComponent {
  private constructor(public readonly component: Component) {
  }

  static create(type: AssetTypeFormFieldType): FormComponent | undefined {
    return new FormComponent(this.componentMap[type])
  }

  private static componentMap: Record<AssetTypeFormFieldType, Component> = {
    [AssetTypeFormFieldType.Text]: FormsRendererInputsHFInputText,
    [AssetTypeFormFieldType.Textarea]: FormsRendererInputsHFTextarea,
    [AssetTypeFormFieldType.Checkbox]: FormsRendererInputsHFCheckbox,
    [AssetTypeFormFieldType.Date]: FormsRendererInputsHFInputDate,
    [AssetTypeFormFieldType.DateTime]: FormsRendererInputsHFInputDateTime,
    [AssetTypeFormFieldType.Time]: FormsRendererInputsHFInputTime,
    [AssetTypeFormFieldType.Number]: FormsRendererInputsHFInputNumber,

    [AssetTypeFormFieldType.Select]: FormsRendererInputsHFRelationshipSelect,
    [AssetTypeFormFieldType.AdvancedSelect]: FormsRendererInputsHFRelationshipAdvancedSelect,
    [AssetTypeFormFieldType.AdvancedMultilineSelect]: FormsRendererInputsHFRelationshipAdvancedMultiLineSelect,
    [AssetTypeFormFieldType.Table]: FormsRendererInputsHFRelationshipTable,

    [AssetTypeFormFieldType.ManyForm]: FormsRendererInputsHFRelationshipManyForm,
  }
}
