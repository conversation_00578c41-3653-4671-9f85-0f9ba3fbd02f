import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeFormFieldKeys } from '../keys/asset-type-form-field.keys'

export function useAssetTypeFormFieldsQuery() {
  return useQuery({
    ...assetTypeFormFieldKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeFormFieldsByAssetTypeQuery(assetTypeId: string) {
  return useQuery({
    ...assetTypeFormFieldKeys.byAssetTypeId(assetTypeId),
    staleTime: CACHE_CONFIG.INFITE,
  })
}
