import type { Dayjs } from 'dayjs'
import type { Day } from '~/utils/dates/day.enum'
import dayjs from 'dayjs'
import { useCronSchedule } from '~/composables/cron/useCronSchedule'
import { DateFormat } from '../dates/date.format'

interface DayOffset {
  scheduledDay: Day
  dayOffset: number
}

export function getNextExecution(input: {
  cronExpression?: string | null
  fromDate?: Dayjs | null
  format?: string | null
}): string | null {
  const { cronExpression, format } = input
  const fromDate = input.fromDate ?? dayjs()

  if (!cronExpression || typeof cronExpression !== 'string') {
    return null
  }

  const { fromCron } = useCronSchedule()
  const schedule = fromCron(cronExpression)

  if (!schedule.time || schedule.days.length === 0)
    return null

  const [hourString, minuteString] = schedule.time.split(':')
  const hour = Number(hourString ?? '0')
  const minute = Number(minuteString ?? '0')

  const currentDate = fromDate.startOf('minute')
  const currentDayOfWeek = currentDate.day()

  const sortedDayOffsets: DayOffset[] = schedule.days
    .map((scheduledDay: Day): DayOffset => {
      const dayOffset
          = scheduledDay >= currentDayOfWeek
            ? scheduledDay - currentDayOfWeek
            : scheduledDay + 7 - currentDayOfWeek

      return { scheduledDay, dayOffset }
    })
    .sort((first: DayOffset, second: DayOffset) => first.dayOffset - second.dayOffset)

  for (const { dayOffset } of sortedDayOffsets) {
    const candidateDate = currentDate
      .add(dayOffset, 'day')
      .hour(hour)
      .minute(minute)

    if (candidateDate.isAfter(currentDate)) {
      return candidateDate.format(format ?? DateFormat.DD_MM_YYYY_HH_MM_SLASH_DASH)
    }
  }

  const fallbackDate = currentDate.add(1, 'week').hour(hour).minute(minute)

  return fallbackDate.format(format ?? DateFormat.DD_MM_YYYY_HH_MM_SLASH_DASH)
}
