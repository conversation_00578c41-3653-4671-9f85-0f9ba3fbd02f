import type { ColumnType } from '../types/table/ColumnType'
import { BaseColumn } from './BaseColumn'

export abstract class BaseColumnWithField<T> extends BaseColumn<T> {
  constructor(
    public field: keyof T,
    public override label: string,
    public override sortable: boolean,
    public override type: ColumnType,
    public override valueCallback?: (obj: T) => string,
  ) {
    super(label, sortable, type, valueCallback)
  }

  getField(obj: T, raw: boolean = false): string {
    if (this.valueCallback) {
      return this.valueCallback(obj)
    }

    if (!this.field) {
      throw new Error('Field is undefined but getField was called.')
    }

    if (raw) {
      return useGet(obj, this.field)
    }

    const value = useGet(obj, this.field) ?? ''
    if (value.length === 0) {
      return 'n/a'
    }
    return value
  }
}
