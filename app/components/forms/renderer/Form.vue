<script setup lang="ts">
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import { errorDisplayProp } from '~/utils/forms/errorDisplayProp'

const props = defineProps({
  formDefinition: {
    type: Object as PropType<FormDefinition>,
    required: true,
  },
  submitLabel: {
    type: String,
    default: 'Submit',
  },
  isSubForm: {
    type: Boolean,
    default: false,
  },
  errors: {
    type: Object as PropType<Record<string, string[]>>,
    default: () => ({}),
  },
  isReadOnly: {
    type: Boolean,
    default: false,
  },
  ...errorDisplayProp,
})
const emits = defineEmits(['submit'])

const model = defineModel({
  type: Object as PropType<Record<string, any>>,
  required: true,
})

const type = props.isSubForm ? 'div' : 'form'

function handleSubmit() {
  emits('submit')
}
</script>

<template>
  <component :is="type" class="flex flex-col gap-4" @submit.prevent="handleSubmit">
    <FormsRendererFormPanel
      v-model="model"
      :errors="errors"
      :elements="formDefinition.elements"
      :is-read-only="isReadOnly"
      error-display="border"
    />
    <UButton v-if="!isSubForm" class="col-span-12" type="submit" :label="submitLabel" />
  </component>
</template>
