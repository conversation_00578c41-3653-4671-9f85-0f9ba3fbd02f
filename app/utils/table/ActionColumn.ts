import type { ColumnDef } from '@tanstack/vue-table'
import { TablesRendererTypesActionButtons } from '#components'
import { BaseColumn } from '~/utils/table/BaseColumn'
import { ColumnType } from '~/utils/types/table/ColumnType'

export class ActionColumn<T> extends BaseColumn<T> {
  constructor(
    label: string,
    private actions: Action<T>[],
  ) {
    super(label, false, ColumnType.Text)
  }

  getConfig(): ColumnDef<T> {
    return {
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesActionButtons, {
        actions: this.actions,
        row: row.original,
      }),
    }
  }
}

export interface Action<T> {
  label: string
  onClick: (row: T) => void | Promise<void>
  shouldShow?: (row: T) => boolean
}
