<script setup lang="ts">
import type { ImportProfileApiFormData } from '~/schemas/import-profiles/import-profile-api.schema'
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { importProfileApiSchema } from '~/schemas/import-profiles/import-profile-api.schema'

const props = defineProps<{
  profile: ImportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const formState = ref<ImportProfileApiFormData>({
  path: props.profile.path ?? null,
  page_parameter: props.profile.config?.page_parameter ?? null,
  page_start_index: props.profile.config?.page_start_index ?? null,
  page_step_size: props.profile.config?.page_step_size ?? null,
  page_size_parameter: props.profile.config?.page_size_parameter ?? null,
  page_size_length: props.profile.config?.page_size_length ?? null,
  data_wrapper: props.profile.config?.data_wrapper ?? null,
  additional_parameters: props.profile.config?.additional_parameters ?? null,
})

watch(() => props.profile.config, (newConfig) => {
  formState.value = {
    path: props.profile.path ?? null,
    page_parameter: newConfig?.page_parameter ?? null,
    page_start_index: newConfig?.page_start_index ?? null,
    page_step_size: newConfig?.page_step_size ?? null,
    page_size_parameter: newConfig?.page_size_parameter ?? null,
    page_size_length: newConfig?.page_size_length ?? null,
    data_wrapper: newConfig?.data_wrapper ?? null,
    additional_parameters: newConfig?.additional_parameters ?? null,
  }
}, { immediate: true })

async function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  await formRef.value.submit()

  emit(
    'submit',
    {
      ...props.profile,
      path: formState.value.path,
      config: {
        page_parameter: formState.value.page_parameter,
        page_start_index: formState.value.page_start_index,
        page_step_size: formState.value.page_step_size,
        page_size_parameter: formState.value.page_size_parameter,
        page_size_length: formState.value.page_size_length,
        data_wrapper: formState.value.data_wrapper,
        additional_parameters: formState.value.additional_parameters,
      },
    },
    isSaveAndBackToOverview,
  )
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="importProfileApiSchema"
    class="space-y-4"
  >
    <h2 class="text-lg font-bold">
      API Configuration
    </h2>

    <UFormField label="API Path" name="path" hint="The API endpoint path (e.g., '/api/v1/items')">
      <UInput
        v-model="formState.path"
        placeholder="/api/v1/items"
        block
      />
    </UFormField>

    <UFormField label="Page Parameter" name="page_parameter" hint="Parameter name for pagination (e.g., 'page', 'offset')">
      <UInput
        v-model="formState.page_parameter"
        placeholder="page"
        block
      />
    </UFormField>

    <UFormField label="Page Start Index" name="page_start_index" hint="Starting index for pagination (typically 0 or 1)">
      <UInput
        v-model.number="formState.page_start_index"
        type="number"
        min="0"
        placeholder="0"
        block
      />
    </UFormField>

    <UFormField label="Page Step Size" name="page_step_size" hint="Step size for pagination (typically 1)">
      <UInput
        v-model.number="formState.page_step_size"
        type="number"
        min="1"
        placeholder="1"
        block
      />
    </UFormField>

    <UFormField label="Page Size Parameter" name="page_size_parameter" hint="Parameter name for page size (e.g., 'limit', 'per_page')">
      <UInput
        v-model="formState.page_size_parameter"
        placeholder="limit"
        block
      />
    </UFormField>

    <UFormField label="Page Size Length" name="page_size_length" hint="Number of items per page">
      <UInput
        v-model.number="formState.page_size_length"
        type="number"
        min="1"
        placeholder="100"
        block
      />
    </UFormField>

    <UFormField label="Data Wrapper" name="data_wrapper" hint="JSON path to the data array (e.g., 'data', 'results.items')">
      <UInput
        v-model="formState.data_wrapper"
        placeholder="data"
        block
      />
    </UFormField>

    <UFormField label="Additional Parameters" name="additional_parameters" hint="Extra query parameters as JSON object">
      <UTextarea
        v-model="formState.additional_parameters"
        placeholder="{&quot;key&quot;: &quot;value&quot;, &quot;filter&quot;: &quot;active&quot;}"
        :rows="3"
        block
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Next</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
