import { defineNuxtPlugin } from '#imports'
import {
  dehydrate,
  hydrate,
  QueryClient,
  VueQueryPlugin,
} from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: CACHE_CONFIG.MEDIUM_TERM,
      gcTime: CACHE_CONFIG.LONG_TERM,
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
})

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(VueQueryPlugin, { queryClient })

  if (import.meta.client) {
    const state = nuxtApp.payload.vueQueryState
    if (state) {
      hydrate(queryClient, state)
    }
  }
  else {
    nuxtApp.hooks.hook('app:rendered', () => {
      nuxtApp.payload.vueQueryState = dehydrate(queryClient)
    })
  }
})
