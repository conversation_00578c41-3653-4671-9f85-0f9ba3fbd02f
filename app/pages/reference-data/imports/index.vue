<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { ImportData } from '~/utils/types/api/api'
import { FormsImportsAddImport, FormsImportsEditImport, ModalsConfirmModal } from '#components'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

definePageMeta({
  middleware: ['auth'],
})

// Component setup
const toast = useToast()
const overlay = useOverlay()

const assetTypeStore = useAssetTypeStore()
const importStore = useImportStore()

const importsData = ref<ImportData[]>()
const loading = ref<boolean>(true)
const columns: BaseColumn<ImportData>[] = [
  new CopyColumn<ImportData>('id', 'Id'),
  new TextColumn<ImportData>('status', 'Status'),
  new TextColumn<ImportData>('asset_type_id', 'Master data', false, (importData) => {
    try {
      return assetTypeStore.getById(importData.asset_type_id).label
    }
    catch {
      return '---'
    }
  }),
  new ActionColumn<ImportData>('Actions', [
    { label: 'Edit', onClick: row => onEdit(row) },
    { label: 'Delete', onClick: row => onDelete(row) },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const addImportModal = overlay.create(FormsImportsAddImport)
const editImportModal = overlay.create(FormsImportsEditImport)
const confirmModal = overlay.create(ModalsConfirmModal)

onMounted(async () => {
  importsData.value = (await importStore.fetchAll()).data
  loading.value = false
})

async function onAdd() {
  const overlay = addImportModal.open()

  const res = await overlay.result as ImportData | undefined

  importsData.value = (await importStore.fetchAll()).data
  if (!res) {
    return
  }
  onEdit(res)
}

function onEdit(importData: ImportData) {
  editImportModal.open({ importId: importData.id })
}

async function onDelete(importData: ImportData) {
  const overlay = confirmModal.open({
    title: 'Delete Import',
  })

  if (!await overlay.result)
    return

  try {
    await importStore.destroy(importData.id)
    importsData.value = (await importStore.fetchAll()).data
    toast.add({ title: 'Import deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Could not delete import',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UContainer>
    <UPageHeader title="Import">
      <template #links>
        <UButton @click="onAdd">
          Add import
        </UButton>
      </template>
    </UPageHeader>
    <UPageBody>
      <UTable
        v-if="!loading"
        :data="importsData"
        :columns="columnConfig"
      />
    </UPageBody>
  </UContainer>
</template>
