import { z } from 'zod'
import { WebserviceAuthType } from '~/utils/types/api/api'

export const apiIntegrationFormSchema = z.object({
  label: z.string().min(1, 'Label is required'),
  data: z.object({
    webservice_url: z.string().min(1, 'URL is required'),
    webservice_auth_method: z.nativeEnum(WebserviceAuthType),
    webservice_username: z.string().optional(),
    webservice_password: z.string().optional(),
    webservice_ntlm: z.boolean().optional(),
  }).superRefine((data, ctx) => {
    if (data.webservice_auth_method === WebserviceAuthType.BASIC) {
      if (!data.webservice_username || data.webservice_username.trim() === '') {
        ctx.addIssue({
          path: ['webservice_username'],
          code: z.ZodIssueCode.custom,
          message: 'Username is required for Basic authentication',
        })
      }
      if (data.webservice_ntlm === undefined) {
        ctx.addIssue({
          path: ['webservice_ntlm'],
          code: z.ZodIssueCode.custom,
          message: 'NTLM must be true or false for Basic authentication',
        })
      }
    }

    if (data.webservice_auth_method === WebserviceAuthType.BEARER) {
      if (!data.webservice_password || data.webservice_password.trim() === '') {
        ctx.addIssue({
          path: ['webservice_password'],
          code: z.ZodIssueCode.custom,
          message: 'Token is required for Bearer authentication',
        })
      }
    }
  }),
})

export type ApiIntegrationForm = z.output<typeof apiIntegrationFormSchema>
