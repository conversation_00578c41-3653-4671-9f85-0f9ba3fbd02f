<script setup lang="ts">
import type { ExportProfileData } from '~/utils/types/api/api'
import { useDeleteExportProfileMutation } from '~/api/export-profiles'
import { useExportProfilesQuery } from '~/api/export-profiles/queries/useExportProfileQueries'
import { isExportProfileReady, isValidExportProfileStatus } from '~/utils/export-profiles/export-profile-status.checker'
import { EXPORT_PROFILE_STATUSES } from '~/utils/export-profiles/export-profile-statusses'

definePageMeta({
  layout: 'admin',
})

const {
  data: profiles,
  isFetching: isFetchingProfiles,
} = useExportProfilesQuery()

const router = useRouter()

function configureProfile(profile: ExportProfileData) {
  const status = isValidExportProfileStatus(profile.status)
    ? isExportProfileReady(profile)
      ? EXPORT_PROFILE_STATUSES.BASIC_INFORMATION
      : profile.status
    : EXPORT_PROFILE_STATUSES.BASIC_INFORMATION

  router.push({
    path: `/admin/export-profiles/${profile.id}`,
    query: { step: status },
  })
}

const deleteExportProfile = useDeleteExportProfileMutation()

async function deleteProfile(profileId: string) {
  await deleteExportProfile.mutateAsync(profileId)

  useToast().add({
    title: 'Export profile deleted',
    description: 'The export profile has been successfully deleted.',
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Export Profiles">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            Export Profiles
          </h1>
          <p class="text-gray-500 mt-3">
            Keep your data up-to-date with automated exports. The profiles run automatically whenever an order is validated.
          </p>
        </div>
        <UButton
          label="Create export profile"
          size="lg"
          @click="$router.push('/admin/export-profiles/new')"
        />
      </div>
      <ExportProfilesList
        v-if="!isFetchingProfiles && profiles"
        :profiles="profiles"
        @configure-profile="configureProfile"
        @delete-profile="deleteProfile"
      />
    </template>
  </UDashboardPanel>
</template>
