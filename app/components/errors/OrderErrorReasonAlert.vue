<script setup lang="ts">
interface Props {
  reason?: string | Record<string, string> | string[] | null | undefined
}

const props = defineProps<Props>()

const errorMessage = computed(() => {
  const { reason } = props

  if (!reason)
    return ''

  if (typeof reason === 'string') {
    return reason
  }

  if (Array.isArray(reason)) {
    return reason.join(', ')
  }

  if (typeof reason === 'object') {
    return Object.entries(reason)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')
  }

  return ''
})
</script>

<template>
  <UAlert
    v-if="errorMessage"
    color="error"
    variant="subtle"
    class="mb-4"
    :description="errorMessage"
  />
</template>
