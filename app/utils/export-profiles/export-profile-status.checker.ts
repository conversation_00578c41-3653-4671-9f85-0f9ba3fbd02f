import type { ExportProfileData } from '../types/api/api'
import { EXPORT_PROFILE_STATUSES } from './export-profile-statusses'

export function isExportProfileReady(
  profile: ExportProfileData,
): boolean {
  const status = profile.status

  if (!status) {
    return false
  }

  return status === EXPORT_PROFILE_STATUSES.READY
}

export function isValidExportProfileStatus(
  status: string | EXPORT_PROFILE_STATUSES | undefined | null,
): boolean {
  if (!status) {
    return false
  }

  return Object.values(EXPORT_PROFILE_STATUSES).includes(status as EXPORT_PROFILE_STATUSES)
}
