import type { SortingState } from '@tanstack/vue-table'
import qs from 'qs'

interface Pagination {
  page: number
  perPage: number
}

export default class QueryData {
  private pagination: Pagination = {
    page: 1,
    perPage: 10,
  }

  private includes?: string[]
  private sort?: SortingState
  private filter?: Record<string, any>
  private params?: Record<string, string>

  setPage(page: number): QueryData {
    this.pagination.page = page
    return this
  }

  setPerPage(perPage: number): QueryData {
    this.pagination.perPage = perPage
    return this
  }

  setSort(sort: SortingState): QueryData {
    if (sort.length === 0) {
      this.sort = undefined
      return this
    }

    this.sort = sort
    return this
  }

  getSort(): SortingState {
    return this.sort || []
  }

  setFilters(filters: Record<string, any>): QueryData {
    this.filter = filters
    return this
  }

  setIncludes(includes: string[]): QueryData {
    this.includes = includes
    return this
  }

  setParams(params: Record<string, string>): QueryData {
    this.params = params
    return this
  }

  toQueryString(): string {
    const query: Record<string, any> = {}

    if (this.pagination.perPage > 0) {
      query.page = this.pagination.page
      query.perPage = this.pagination.perPage
    }

    if (this.includes && this.includes.length > 0) {
      query.include = this.includes.join(',')
    }

    if (this.sort && this.sort.length > 0) {
      const sortParams = this.sort.map(state => state.desc ? `-${state.id}` : state.id)
      query.sort = sortParams.join(',')
    }

    if (this.filter && Object.keys(this.filter).length > 0) {
      query.filter = this.filter
    }

    if (this.params && Object.keys(this.params).length > 0) {
      Object.entries(this.params).forEach(([key, value]) => {
        query[key] = value
      })
    }

    return qs.stringify(query, { encode: true, arrayFormat: 'comma' })
  }
}
