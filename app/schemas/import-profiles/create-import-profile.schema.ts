import { z } from 'zod'

export const createImportProfileUiSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  asset_type_id: z.string().min(1, 'Asset Type is required').uuid(),
  integration_id: z.string().min(1, 'Integration is required').uuid(),
})

export const createImportProfileApiSchema = createImportProfileUiSchema.transform(data => ({
  name: data.name,
  asset_type_id: data.asset_type_id,
  integration_id: data.integration_id,
}))
