<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type {
  AssetTypeFieldData,
  EditAssetTypeFieldData,
} from '~/utils/types/api/api'
import { useUpdateAssetTypeFieldMutation } from '~/api/asset-type-fields'

const props = defineProps({
  assetTypeField: {
    type: Object as PropType<AssetTypeFieldData>,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const data = ref<EditAssetTypeFieldData>({
  locked: props.assetTypeField.locked ?? false,
  required: props.assetTypeField.required ?? false,
  searchable: props.assetTypeField.searchable ?? false,
  weight: props.assetTypeField.weight ?? 1,
})

const editAssetTypeFieldMutation = useUpdateAssetTypeFieldMutation()

async function onSubmit() {
  try {
    await editAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeField.asset_type_id,
      fieldId: props.assetTypeField.id,
      data: data.value,
    })
    emit('close', false)
    toast.add({ title: 'Master data field updated', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error updating master data field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <USlideover :title="`Update field: ${assetTypeField.label}`" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Weight" class="col-span-3">
            <UInput v-model="data.weight" placeholder="1" type="number" />
          </UFormField>
          <div class="col-span-9" />
          <UFormField label="Locked" class="col-span-4">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField label="Required" class="col-span-4">
            <USwitch v-model="data.required" />
          </UFormField>
          <UFormField label="Searchable" class="col-span-4">
            <USwitch v-model="data.searchable" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="editAssetTypeFieldMutation.isPending.value"
          :disabled="editAssetTypeFieldMutation.isPending.value"
        >
          Update
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
