import type { ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { apiClientKeys } from '../keys/api-client.keys'
import { ApiClientService } from '../services/api-client.service'

export function useCreateApiClientMutation() {
  return useMutation({
    mutationFn: (data: ApiClientFormData) => ApiClientService.create(data),
    onSuccess: () => {
      batchInvalidateKeys(queryClient, [
        apiClientKeys.all,
      ])
    },
  })
}

export function useUpdateApiClientMutation() {
  return useMutation({
    mutationFn: ({ apiClientId, data }: { apiClientId: string, data: ApiClientFormData }) =>
      ApiClientService.update(apiClientId, data),
    onSuccess: (_, { apiClientId }) => {
      batchInvalidateKeys(queryClient, [
        apiClientKeys.all,
        apiClientKeys.byId(apiClientId),
        apiClientKeys.byIdOrFail(apiClientId),
      ])
    },
  })
}

export function useDeleteApiClientMutation() {
  return useMutation({
    mutationFn: (apiClientId: string) => ApiClientService.delete(apiClientId),
    onSuccess: (_, apiClientId) => {
      batchInvalidateKeys(queryClient, [
        apiClientKeys.all,
        apiClientKeys.byId(apiClientId),
        apiClientKeys.byIdOrFail(apiClientId),
      ])
    },
  })
}

export function useRevokeAndGenerateApiClientMutation() {
  return useMutation({
    mutationFn: (apiClientId: string) => ApiClientService.revokeAndGenerate(apiClientId),
    onSuccess: (_, apiClientId) => {
      batchInvalidateKeys(queryClient, [
        apiClientKeys.all,
        apiClientKeys.byId(apiClientId),
        apiClientKeys.byIdOrFail(apiClientId),
      ])
    },
  })
}
