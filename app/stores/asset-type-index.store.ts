import type { AssetTypeIndexData, UpsertAssetTypeIndexData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useAssetTypeIndexStore = defineStore('asset-type-index', () => {
  const assetTypeIndexes = ref<AssetTypeIndexData[]>([])

  // Local state functions

  function getAll(): AssetTypeIndexData[] {
    return assetTypeIndexes.value
  }

  function getByAssetTypeId(assetTypeId: string): AssetTypeIndexData[] {
    return assetTypeIndexes.value.filter(index => index.asset_type_id === assetTypeId)
  }

  function getByAssetTypeFieldId(assetTypeFieldId: string): AssetTypeIndexData[] {
    return assetTypeIndexes.value.filter(index => index.asset_type_field_id === assetTypeFieldId)
  }

  // API functions

  async function hydrate(
    assetTypeId?: string,
  ) {
    const url = assetTypeId
      ? `api/dynamic-assets/asset-types/${assetTypeId}/indexes`
      : `api/dynamic-assets/asset-type-indexes`

    const response = await useApiClient()<ApiResponse<AssetTypeIndexData[]>>(url)

    if (assetTypeId) {
      assetTypeIndexes.value = [
        ...assetTypeIndexes.value.filter(field => field.asset_type_id !== assetTypeId),
        ...response.data,
      ]
    }
    else {
      assetTypeIndexes.value = response.data
    }
  }

  async function create(
    assetTypeId: string,
    upsertAssetTypeIndexData: UpsertAssetTypeIndexData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeIndexData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/indexes`,
      {
        method: 'POST',
        body: JSON.stringify(upsertAssetTypeIndexData),
      },
    )

    assetTypeIndexes.value = [...assetTypeIndexes.value, response.data]

    return response
  }

  async function destroy(
    assetTypeId: string,
    assetTypeIndexId: string,
  ) {
    await useApiClient()<ApiResponse<boolean>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/indexes/${assetTypeIndexId}`,
      {
        method: 'DELETE',
      },
    )

    assetTypeIndexes.value = assetTypeIndexes.value.filter(field => field.id !== assetTypeIndexId)
  }

  return {
    assetTypeIndexes,

    getAll,
    getByAssetTypeId,
    getByAssetTypeFieldId,

    hydrate,
    create,
    destroy,
  }
})
