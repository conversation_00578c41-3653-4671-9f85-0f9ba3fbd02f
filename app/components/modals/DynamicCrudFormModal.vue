<script setup lang="ts">
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useAssetTypeFormFieldsByAssetTypeQuery } from '~/api/asset-type-form-fields/queries/useAssetTypeFormFieldQueries'
import { AssetQueryFactory } from '~/api/assets'
import { FormGenerator } from '~/utils/forms/FormGenerator'

const props = defineProps({
  title: { type: String, default: 'Form' },
  assetTypeId: { type: String, required: true },
  id: { type: String, default: undefined },
})
const emit = defineEmits<{ close: [any] }>()
const errors = ref<any>({})

const assetTypeStore = useAssetTypeStore()
const assetType = assetTypeStore.getById(props.assetTypeId)

const { data: formFields } = useAssetTypeFormFieldsByAssetTypeQuery(assetType.id)
const { data: assetTypeFields } = useAssetTypeFieldsQuery()

const formDefinition = computed(() =>
  new FormGenerator(assetTypeFields.value ?? [])
    .generateFormDefinition(formFields.value ?? []),
)

const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(assetType?.table_name)
  .getQueries()

const formData = ref<Record<string, any>>({})

const { data: assetData, isFetching: isFetchingAsset } = useAssetQuery(
  props.id!,
  { enabled: !!props.id },
)

watch(assetData, (newVal) => {
  if (newVal)
    formData.value = { ...newVal }
}, { immediate: true })

const { useCreateAssetMutation, useUpdateAssetMutation } = AssetQueryFactory
  .getQueryInstance(assetType?.table_name)
  .getMutations()

const createAssetMutation = useCreateAssetMutation()
const updateAssetMutation = useUpdateAssetMutation()

async function onsubmit() {
  try {
    let result

    if (props.id) {
      result = await updateAssetMutation.mutateAsync({
        id: props.id,
        data: formData.value,
      })
    }
    else {
      result = await createAssetMutation.mutateAsync(formData.value)
    }

    emit('close', result)
  }
  catch (e: unknown) {
    if (e && typeof e === 'object' && 'data' in e) {
      errors.value = (e as any).data.errors
    }
    else {
      console.error('Unexpected error', e)
    }
  }
}
</script>

<template>
  <USlideover :title="title" :overlay="false">
    <template #body>
      <FormsRendererForm
        v-if="!isFetchingAsset"
        v-model="formData"
        :form-definition="formDefinition"
        :errors="errors"
        @submit="onsubmit"
      />
    </template>
  </USlideover>
</template>
