import type { RelationshipFilter } from '~/stores/asset-type-relationship.store'
import { createQueryKeys } from '@lukemorales/query-key-factory'
import { AssetTypeRelationshipCacheService } from '../services/asset-type-relationship-cache.service'
import { AssetTypeRelationshipService } from '../services/asset-type-relationship.service'

export const assetTypeRelationshipKeys = createQueryKeys('assetTypeRelationships', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeRelationshipService.getAll(),
  },
  byAssetTypeId: (assetTypeId: string, filters?: RelationshipFilter[]) => ({
    queryKey: [assetTypeId, filters],
    queryFn: () => AssetTypeRelationshipCacheService.getByAssetTypeId(assetTypeId, filters),
  }),
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeRelationshipCacheService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeRelationshipCacheService.getByIdOrFail(id),
  }),
})
