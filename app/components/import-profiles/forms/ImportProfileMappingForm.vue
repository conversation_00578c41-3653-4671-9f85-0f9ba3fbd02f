<script setup lang="ts">
import type { AssetTypeFieldData, AssetTypeRelationshipData, MappingItemData } from '~/utils/types/api/api'
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'

import { cloneDeep } from 'lodash-es'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useImportProfileRecordsQuery } from '~/api/import-profiles/queries/useImportProfileQueries'
import TableSkeleton from '~/components/tables/TableSkeleton.vue'
import { generateColumnsMetaFromRows } from '~/utils/table/columns-meta.generator'
import {

  AssetTypeRelationshipMethod,

} from '~/utils/types/api/api'

const props = defineProps<{
  id: string | null
  profile: ImportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const {
  data: records,
  isFetching: isFetchingRecords,
  isError: isErrorFetchingRecords,
} = useImportProfileRecordsQuery(props.id!)

watch(isErrorFetchingRecords, () => {
  useToast().add({
    title: 'Error fetching records',
    description: 'There was an error fetching the records for this import profile.',
    color: 'error',
  })
})

const mapping = ref(
  cloneDeep(
    markUnmappedItemsAsSkipped(props.profile.mapping ?? []),
  ),
)

function markUnmappedItemsAsSkipped(data: MappingItemData[]): MappingItemData[] {
  if (!data || data.length === 0) {
    return []
  }

  return data.map((item) => {
    if (!item.to && !item.skip) {
      return {
        ...item,
        skip: true,
      }
    }

    return item
  })
}

const isEveryMappingItemSelected = computed(() =>
  mapping.value?.every(item => (item.to && item.to !== '') || item.skip) ?? false,
)

const columnsMeta = computed(() => {
  return generateColumnsMetaFromRows(records.value ?? []).sort((a, b) =>
    a.key.localeCompare(b.key),
  )
})

function handleMappingUpdate(update: { from: string, to: string, skip: boolean, mapping: any }) {
  const index = mapping.value?.findIndex(m => m.from === update.from)
  if (index !== -1) {
    mapping.value[index] = update
  }
}

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const assetTypeRelationships = useAssetTypeRelationshipStore().getByAssetTypeId(props.profile.asset_type_id)

function getFieldsByAssetTypeId(assetTypeId: string) {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}

const assetTypeFields = computed(() => getFieldsByAssetTypeId(props.profile.asset_type_id))

function generateMappingOptionsFromFields(
  fields: AssetTypeFieldData[],
  relationships: AssetTypeRelationshipData[],
) {
  const mappedRelationships = relationships.filter(
    relationship => relationship.relationship_method === AssetTypeRelationshipMethod.BelongsTo
      && relationship.relationship_asset_type_id,
  ).map(relationship => ({
    label: `${relationship.label} ID`,
    value: relationship.id,
  }))

  const mappedFields = fields.map(field => ({
    label: field.label,
    value: field.id,
  }))

  return mappedFields.concat(mappedRelationships)
}

const mappingOptions = computed(() => generateMappingOptionsFromFields(assetTypeFields.value, assetTypeRelationships))
function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  if (!mapping.value)
    return

  emit(
    'submit',
    {
      ...props.profile,
      mapping: mapping.value,
    },
    isSaveAndBackToOverview,
  )
}
</script>

<template>
  <template v-if="!isFetchingRecords && records && mapping">
    <UForm
      ref="formRef"
      :state="{ mapping }"
    >
      <h2 class="text-lg font-bold mb-4">
        Field mapping
      </h2>

      <UFormField name="mapping">
        <TablesDataTable
          :columns-meta="columnsMeta"
          :data="records"
          :mapping-meta="mapping"
          :mapping-options="mappingOptions"
          :editable="false"
          @update-mapping="handleMappingUpdate"
        />
      </UFormField>

      <div class="flex flex-col gap-2 mt-6">
        <UButton
          type="button"
          size="lg"
          color="neutral"
          block
          :disabled="!isEveryMappingItemSelected"
          @click="handleSubmit(false)"
        >
          <p>Next</p>
          <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
        </UButton>

        <UButton
          type="button"
          size="lg"
          color="neutral"
          variant="outline"
          block
          :disabled="!isEveryMappingItemSelected"
          @click="handleSubmit(true)"
        >
          <p>Save & Go Back to Overview</p>
        </UButton>

        <UButton
          type="button"
          size="lg"
          color="neutral"
          variant="link"
          block
          @click="$emit('previousStep')"
        >
          <p class="underline">
            Back to previous step
          </p>
        </UButton>
      </div>
    </UForm>
  </template>

  <template v-else>
    <USkeleton class="h-4 w-40 mb-4" />
    <TableSkeleton :rows="10" :columns="columnsMeta.length || 4" />
    <USkeleton class="h-10 w-full mt-4 mb-2" />
    <USkeleton class="h-10 w-full" />
  </template>
</template>
