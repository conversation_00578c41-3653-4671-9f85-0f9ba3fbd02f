import type { AssetTypeData, CreateAssetTypeData, UpdateAssetTypeData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useAssetTypeStore = defineStore('asset-type', () => {
  const assetTypes = ref<AssetTypeData[]>([])

  // Local state functions

  function getByTableName(tableName: string): AssetTypeData {
    const assetType = assetTypes.value.find(assetType => assetType.table_name === tableName)
    if (!assetType)
      throw new Error(`Master data not found for table name: ${tableName}`)
    return assetType
  }

  function getById(id: string): AssetTypeData {
    const assetType = assetTypes.value.find(assetType => assetType.id === id)
    if (!assetType)
      throw new Error(`Master data not found for id: ${id}`)
    return assetType
  }

  function getSupportingDataAssetTypes(): AssetTypeData[] {
    return assetTypes.value.filter(assetType => assetType.supporting_data)
  }

  async function hydrate() {
    const response = await useApiClient()<ApiResponse<AssetTypeData[]>>(
      `api/dynamic-assets/asset-types`,
    )

    assetTypes.value = response.data
  }

  async function create(createAssetTypeData: CreateAssetTypeData) {
    return await useApiClient()<ApiResponse<AssetTypeData>>(
      `api/dynamic-assets/asset-types`,
      {
        method: 'POST',
        body: JSON.stringify(createAssetTypeData),
      },
    )
  }

  async function update(assetTypeId: string, updateAssetTypeData: UpdateAssetTypeData) {
    return await useApiClient()<ApiResponse<AssetTypeData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}`,
      {
        method: 'PUT',
        body: JSON.stringify(updateAssetTypeData),
      },
    )
  }

  async function destroy(assetTypeId: string) {
    await useApiClient()<ApiResponse<void>>(
      `api/dynamic-assets/asset-types/${assetTypeId}`,
      {
        method: 'DELETE',
      },
    )

    assetTypes.value = assetTypes.value.filter(assetType => assetType.id !== assetTypeId)
  }

  async function emptyEntry(id: string) {
    return await useApiClient()<ApiResponse<any>>(
      `api/dynamic-assets/asset-types/${id}/empty-entry`,
    )
  }

  return {
    assetTypes,

    getById,
    getByTableName,
    getSupportingDataAssetTypes,

    hydrate,
    create,
    update,
    destroy,

    emptyEntry,
  }
})
