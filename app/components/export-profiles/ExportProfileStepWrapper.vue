<script setup lang="ts">
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { EXPORT_PROFILE_STATUSES } from '~/utils/export-profiles/export-profile-statusses'
import ExportProfileConfigurationForm from './forms/ExportProfileConfigurationForm.vue'
import ExportProfileDirectoriesForm from './forms/ExportProfileDirectoriesForm.vue'
import ExportProfileTemplateForm from './forms/ExportProfileTemplateForm.vue'

defineProps<{
  step: string
  profile: ExportProfileFormData
  id: string | null
  isCreateMode: boolean
}>()
const emit = defineEmits<{
  submit: [data: ExportProfileFormData, isSaveAndBackToOverview?: boolean]
  nextStep: []
  previousStep: []
}>()

function handleSubmit(data: ExportProfileFormData, isSaveAndBackToOverview: boolean = false) {
  emit('submit', data, isSaveAndBackToOverview)
}
</script>

<template>
  <div>
    <ExportProfileConfigurationForm
      v-if="step === EXPORT_PROFILE_STATUSES.BASIC_INFORMATION"
      :id="id"
      :profile="profile"
      :is-create-mode="isCreateMode"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ExportProfileDirectoriesForm
      v-else-if="id && step === EXPORT_PROFILE_STATUSES.INCOMPLETE"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ExportProfileTemplateForm
      v-else-if="step === EXPORT_PROFILE_STATUSES.TEMPLATING"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
  </div>
</template>
