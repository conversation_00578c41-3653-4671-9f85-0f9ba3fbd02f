import type {
  AssetTypeFieldData,
  EditAssetTypeFieldData,
  UpsertAssetTypeFieldData,
} from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useAssetTypeFieldStore = defineStore('asset-type-field', () => {
  const assetTypeFields = ref<AssetTypeFieldData[]>([])

  // Local state functions

  function getAll(): AssetTypeFieldData[] {
    return assetTypeFields.value
  }

  function getById(assetTypeFieldId: string): AssetTypeFieldData {
    const assetTypeField = assetTypeFields.value.find(field => field.id === assetTypeFieldId)
    if (!assetTypeField)
      throw new Error(`Master data field not found for id: ${assetTypeFieldId}`)
    return assetTypeField
  }

  function getByAssetTypeId(assetTypeId: string): AssetTypeFieldData[] {
    return assetTypeFields.value.filter(field => field.asset_type_id === assetTypeId)
  }

  // API functions

  async function hydrate(
    assetTypeId?: string,
  ) {
    const url = assetTypeId
      ? `api/dynamic-assets/asset-types/${assetTypeId}/fields`
      : `api/dynamic-assets/asset-type-fields`

    const response = await useApiClient()<ApiResponse<AssetTypeFieldData[]>>(url)

    if (assetTypeId) {
      assetTypeFields.value = [
        ...assetTypeFields.value.filter(field => field.asset_type_id !== assetTypeId),
        ...response.data,
      ]
    }
    else {
      assetTypeFields.value = response.data
    }
  }

  async function create(
    assetTypeId: string,
    upsertAssetTypeFieldData: UpsertAssetTypeFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/fields`,
      {
        method: 'POST',
        body: JSON.stringify(upsertAssetTypeFieldData),
      },
    )

    assetTypeFields.value = [...assetTypeFields.value, response.data]

    return response
  }

  async function update(
    assetTypeId: string,
    assetTypeFieldId: string,
    editAssetTypeFieldData: EditAssetTypeFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/fields/${assetTypeFieldId}`,
      {
        method: 'PUT',
        body: JSON.stringify(editAssetTypeFieldData),
      },
    )

    assetTypeFields.value = assetTypeFields.value.map(assetTypeField =>
      assetTypeField.id === assetTypeFieldId ? response.data : assetTypeField,
    )

    return response
  }

  async function destroy(
    assetTypeId: string,
    id: string,
  ) {
    await useApiClient()<ApiResponse<boolean>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/fields/${id}`,
      {
        method: 'DELETE',
      },
    )

    assetTypeFields.value = assetTypeFields.value.filter(assetTypeField => assetTypeField.id !== id)
  }

  return {
    assetTypeFields,

    getAll,
    getById,
    getByAssetTypeId,

    hydrate,
    create,
    update,
    destroy,
  }
})
