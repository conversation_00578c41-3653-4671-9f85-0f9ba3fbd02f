import type { FormField } from '~/utils/forms/FormField'
import type { AssetTypeFormFieldData } from '~/utils/types/api/api'

export class FormFieldExtra {
  constructor(
    // editor
    public readonly lang?: string,
    // assetTypeField
    public readonly parentField?: AssetTypeFormFieldData,
    // enumBasedFields
    public readonly options?: Record<string, FormField[]>,
  ) {
  }

  // AssetTypeFields
  public readonly wrapperClass?: string
}
