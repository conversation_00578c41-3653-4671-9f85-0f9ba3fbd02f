<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeTableFieldData } from '~/utils/types/api/api'
import { FormsAssetTypeUpsertAssetTypeTableFieldForm, ModalsConfirmModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()

const assetTypeTableFieldStore = useAssetTypeTableFieldStore()
const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const assetTypeRelationship = useAssetTypeRelationshipStore()

function getFieldLabelById(fieldId: string): string {
  try {
    const field = allAssetTypeFields.value?.find(f => f.id === fieldId)
    return field?.label || 'No field'
  }
  catch {
    return 'No field'
  }
}

const columns: BaseColumn<AssetTypeTableFieldData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('asset_type_relationship_id', 'Master data relationship', false, (assetTypeTableField) => {
    try {
      return assetTypeRelationship.getById(assetTypeTableField?.asset_type_relationship_id ?? '')?.label
    }
    catch {
      return 'No relationship'
    }
  }),
  new TextColumn('asset_type_field_id', 'Master data field', false, (assetTypeTableField) => {
    return getFieldLabelById(assetTypeTableField?.asset_type_field_id ?? '')
  }),
  new TextColumn('type', 'Type'),
  new TextColumn('label', 'Label'),
  new TextColumn('weight', 'Weight'),
  new CheckboxColumn('locked', 'Locked'),
  new ActionColumn('Actions', [
    { label: 'Edit', onClick: onEdit },
    { label: 'Delete', onClick: onDelete },
    { label: 'Move up', onClick: onUp },
    { label: 'Move down', onClick: onDown },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const formModal = overlay.create(FormsAssetTypeUpsertAssetTypeTableFieldForm)

async function onUp(assetTypeTableField: AssetTypeTableFieldData) {
  await assetTypeTableFieldStore.moveUp(props.assetTypeId, assetTypeTableField.id as string)
  await assetTypeTableFieldStore.hydrate(props.assetTypeId)
}

async function onDown(assetTypeTableField: AssetTypeTableFieldData) {
  await assetTypeTableFieldStore.moveDown(props.assetTypeId, assetTypeTableField.id as string)
  await assetTypeTableFieldStore.hydrate(props.assetTypeId)
}

function onEdit(assetTypeTableField: AssetTypeTableFieldData) {
  formModal.open({
    assetTypeId: props.assetTypeId,
    assetTypeTableField,
  })
}

async function onDelete(assetTypeTableField: AssetTypeTableFieldData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeTableFieldStore.destroy(props.assetTypeId, assetTypeTableField.id as string)
    toast.add({ title: 'Master data table field deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data table field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeTableFieldStore.getByAssetTypeId(assetTypeId)"
  />
</template>
