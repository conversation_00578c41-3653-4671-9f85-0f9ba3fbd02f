import type { AssetTypeFieldData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeFieldKeys } from '../keys/asset-type-field.keys'

export class AssetTypeFieldCacheService {
  private static getCachedAssetTypeFields(): AssetTypeFieldData[] | undefined {
    const { getCachedQuery } = useCachedQueries()

    return getCachedQuery<AssetTypeFieldData[]>(
      assetTypeFieldKeys.all.queryKey,
    ).value
  }

  static getById(id: string): AssetTypeFieldData | undefined {
    const fields = this.getCachedAssetTypeFields()
    return fields?.find((f: AssetTypeFieldData) => f.id === id)
  }

  static getByIdOrFail(id: string): AssetTypeFieldData {
    const field = this.getById(id)

    if (!field) {
      throw new Error(`AssetTypeField with id ${id} not found`)
    }

    return field
  }

  static getByAssetTypeId(assetTypeId: string): AssetTypeFieldData[] {
    const fields = this.getCachedAssetTypeFields()
    return fields?.filter((f: AssetTypeFieldData) => f.asset_type_id === assetTypeId) || []
  }
}
