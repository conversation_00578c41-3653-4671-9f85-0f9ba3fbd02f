import type { AssetTypeFieldData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeFieldKeys } from '../keys/asset-type-field.keys'

export function useAssetTypeFieldsQuery() {
  return useQuery({
    ...assetTypeFieldKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeFieldsByAssetTypeQuery(assetTypeId: string) {
  return useQuery({
    ...assetTypeFieldKeys.byAssetTypeId(assetTypeId),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeFieldByIdQuery(id: string) {
  return useQuery({
    ...assetTypeFieldKeys.byId(id),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeFieldByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<AssetTypeFieldData, typeof assetTypeFieldKeys.byIdOrFail>,
) {
  return useQuery({
    ...assetTypeFieldKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.INFITE,
    ...options,
  })
}
