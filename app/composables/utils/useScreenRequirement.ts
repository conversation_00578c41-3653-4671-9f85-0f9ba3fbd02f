import { useWindowSize } from '@vueuse/core'

const MIN_WIDTH = 1150
const MIN_HEIGHT = 650

export function useScreenRequirement() {
  const { width, height } = useWindowSize()
  const hasMinimumResolution = computed(() => width.value >= MIN_WIDTH && height.value >= MIN_HEIGHT)

  const resolutionWarningDismissed = useCookie<boolean>('resolution-warning-dismissed', {
    maxAge: 60 * 60 * 24 * 7, // 7 days
    default: () => false,
  })

  const showResolutionWarning = computed(() => {
    return !hasMinimumResolution.value && !resolutionWarningDismissed.value
  })

  function dismissResolutionWarning() {
    resolutionWarningDismissed.value = true
  }

  return {
    hasMinimumResolution,
    showResolutionWarning,
    dismissResolutionWarning,
    MIN_WIDTH,
    MIN_HEIGHT,
  }
}
