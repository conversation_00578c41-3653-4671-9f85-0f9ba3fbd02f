<script setup lang="ts">
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { cloneDeep } from 'lodash-es'
import { useIntegrationsQuery } from '~/api/integrations/queries/useIntegrationQueries'
import { createExportProfileUiSchema } from '~/schemas/export-profiles/create-export-profile.schema'

const props = defineProps<{
  profile: ExportProfileFormData
  isCreateMode: boolean
}>()

const emit = defineEmits<{
  submit: [data: ExportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const formState: Ref<ExportProfileFormData> = ref(cloneDeep(props.profile))

watch(() => props.profile, (newProfile) => {
  formState.value = cloneDeep(newProfile)
}, { immediate: true })

const {
  data: integrations,
  isFetching: isIntegrationsFetching,
} = useIntegrationsQuery()

const mappedIntegrations = computed(() =>
  (integrations.value ?? []).map(integration => ({
    label: integration.label,
    value: integration.id,
  })),
)

async function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  await formRef.value.submit()

  emit('submit', formState.value, isSaveAndBackToOverview)
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="createExportProfileUiSchema"
    class="space-y-4"
  >
    <h2 class="text-lg font-bold">
      Profile Configuration
    </h2>

    <UFormField label="Profile name" name="name" required>
      <UInput v-model="formState.name" placeholder="Enter profile name" />
    </UFormField>

    <UFormField
      label="Select an integration to use"
      name="integration_id"
      required
    >
      <USelect
        v-model="formState.integration_id"
        placeholder="Select an item..."
        :loading="isIntegrationsFetching"
        :items="mappedIntegrations"
        :disabled="!props.isCreateMode"
        class="w-full"
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Next</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="outline"
        block
        @click="handleSubmit(true)"
      >
        <p>Save & Go Back to Overview</p>
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
