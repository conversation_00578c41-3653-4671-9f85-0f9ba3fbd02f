<script setup lang="ts">
import { ref, watch } from 'vue'
import NumericField from '~/components/forms/inputs/NumericField.vue'

const props = defineProps<{
  value: string | number | null | undefined
}>()

const emit = defineEmits<{
  (e: 'update', value: number | string | null | undefined): void
}>()

const inputValue = ref(props.value)

watch(() => props.value, (val) => {
  inputValue.value = val
})

watch(inputValue, (val) => {
  emit('update', val)
})
</script>

<template>
  <NumericField
    v-model="inputValue"
    class="w-full"
  />
</template>
