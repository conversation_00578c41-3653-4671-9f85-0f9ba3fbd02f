<script setup lang="ts">
import type { Row } from '@tanstack/table-core'
import type { RowSelectionState } from '@tanstack/vue-table'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { EmailOrderData, OrderInquiryData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import QueryData from '~/utils/query/QueryData'
import { DateTimeColumn } from '~/utils/table/DateTimeColumn'
import { SelectionColumn } from '~/utils/table/SelectionColumn'
import { TextColumn } from '~/utils/table/TextColumn'

// Component setup
const toast = useToast()

// Store imports
const orderInquiryStore = useOrderInquiryStore()
const overlay = useOverlay()

// Component initialize
const loading = ref(true)
const data = ref()
const selection = ref<RowSelectionState>({})
const selectedItemId = ref<string>()
const orderInquiry = shallowRef<OrderInquiryData>()
const queryData = new QueryData().setFilters({
  state: ['Unprocessed'],
})
const columns: BaseColumn<OrderInquiryData>[] = [
  new SelectionColumn('#'),
  new DateTimeColumn('created_at', 'Received at'),
  new TextColumn<OrderInquiryData>('payload', 'From', false, row =>
    typeof row.payload === 'object' && row.payload !== null && 'from' in row.payload
      ? (row.payload as EmailOrderData).from
      : '---'),
  new TextColumn<OrderInquiryData>('payload', 'Subject', false, row =>
    typeof row.payload === 'object' && row.payload !== null && 'subject' in row.payload
      ? (row.payload as EmailOrderData).subject ?? '---'
      : '---'),
]
const columnConfig = columns.map(column => column.getConfig())
const pageNumber = ref(1)
const confirmModal = overlay.create(ModalsConfirmModal)

// Lifecycle hooks
onMounted(async () => {
  await fetchData()
})

// Functions
async function fetchData() {
  try {
    loading.value = true
    data.value = await orderInquiryStore.fetchAll(queryData)
  }
  catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}

async function onPageChanged() {
  queryData.setPage(pageNumber.value)
  await fetchData()
}

async function fetchOrderInquiry(id: string) {
  try {
    const response = await orderInquiryStore.fetchById(id)
    orderInquiry.value = response.data
  }
  catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
}

function rowId(originalRow: OrderInquiryData): string {
  return originalRow.id
}

function onUnselect(): void {
  selectedItemId.value = undefined
  orderInquiry.value = undefined
}

async function onSelect(originalRow: Row<OrderInquiryData>): Promise<void> {
  selectedItemId.value = originalRow.id
  await fetchOrderInquiry(originalRow.id)
}

async function onProcessOrder() {
  if (!selectedItemId.value) {
    return
  }

  const ids = [selectedItemId.value]
  await processOrders(ids)
  await fetchData()
  onUnselect()
}

async function onRejectOrder() {
  if (!selectedItemId.value) {
    return
  }

  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  const ids = [selectedItemId.value]
  await rejectOrders(ids)
  await fetchData()
  onUnselect()
}

async function onProcessOrderBatch() {
  const ids = Object.keys(selection.value)
  if (ids.length === 0) {
    return
  }

  await processOrders(ids)
  await fetchData()
  onUnselect()
}

async function onRejectOrderBatch() {
  const ids = Object.keys(selection.value)
  if (ids.length === 0) {
    return
  }

  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  await rejectOrders(ids)
  await fetchData()
  onUnselect()
}

async function processOrders(ids: string[]) {
  try {
    loading.value = true
    await orderInquiryStore.process(ids)
    toast.add({
      title: ids.length === 1 ? 'Order processing' : 'Orders processing',
      description: ids.length === 1 ? 'The selected order is being processed' : 'The selected orders are being processed',
      color: 'success',
    })
  }
  catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}

async function rejectOrders(ids: string[]) {
  try {
    loading.value = true
    await orderInquiryStore.reject(ids)
    toast.add({
      title: ids.length === 1 ? 'Order rejected' : 'Orders rejected',
      description: ids.length === 1 ? 'The selected order has been rejected' : 'The selected orders have been rejected',
      color: 'success',
    })
  }
  catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div v-if="!loading" class="grid grid-cols-2 h-full">
    <div class="grid-cols-1 flex flex-col gap-4 sm:gap-6 flex-1 overflow-y-auto p-4 sm:p-6" @click="onUnselect">
      <UTable
        v-model:row-selection="selection"
        :data="data.data"
        :columns="columnConfig"
        :get-row-id="rowId"
        @select="onSelect"
      />
      <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
        <div class="text-sm text-(--ui-text-muted)">
          <GeneralEntries :meta-data="data.meta" />
        </div>
        <div class="flex items-center gap-1.5">
          <UPagination
            v-model:page="pageNumber"
            class="justify-center"
            :items-per-page="data.meta.per_page"
            :total="data.meta.total"
            @click="onPageChanged"
          />
        </div>
      </div>
    </div>
    <div class="grid-cols-1 border-l border-(--ui-border) overflow-y-auto">
      <div v-if="selectedItemId && orderInquiry" class="h-full">
        <div class="flex justify-between items-center p-4 border-b border-(--ui-border)">
          <h2 class="text-lg font-medium">
            Order Inquiry Details
          </h2>
          <div class="flex gap-2">
            <UButton
              icon="i-ph-arrows-clockwise"
              color="primary"
              @click="onProcessOrder"
            >
              Process
            </UButton>
            <UButton
              icon="i-ph-trash"
              color="neutral"
              @click="onRejectOrder"
            >
              Reject
            </UButton>
          </div>
        </div>
        <OrdersOrderInquiry :key="orderInquiry.id" :order-inquiry="orderInquiry" />
      </div>
      <div v-else-if="Object.entries(selection).length > 0" class="h-full p-6">
        <div class="flex flex-col items-center justify-center h-full gap-4">
          <UIcon name="i-ph-selection" class="text-4xl text-neutral-600" />
          <p class="text-lg text-center text-neutral-600">
            One or multiple items selected
          </p>
          <p class="text-sm text-center text-neutral-500">
            Click on a single item to view its details or use the buttons below to process all selected items
          </p>
          <div class="flex gap-2">
            <UButton
              icon="i-ph-arrows-clockwise"
              color="primary"
              @click="onProcessOrderBatch"
            >
              Process
            </UButton>
            <UButton
              icon="i-ph-trash"
              color="neutral"
              @click="onRejectOrderBatch"
            >
              Reject
            </UButton>
          </div>
        </div>
      </div>
      <div v-else-if="data.meta.total > 0" class="h-full">
        <div class="flex flex-col items-center justify-center h-full gap-4">
          <UIcon name="i-ph-selection-plus" class="text-4xl text-neutral-600" />
          <p class="text-lg text-center text-neutral-600">
            No item selected
          </p>
          <p class="text-sm text-center text-neutral-500">
            Select an item from the table to view details
          </p>
        </div>
      </div>
      <div v-else class="h-full">
        <div class="flex flex-col items-center justify-center h-full gap-4">
          <UIcon name="i-ph-selection-plus" class="text-4xl text-text-neutral-600" />
          <p class="text-lg text-center text-neutral-600">
            No items left
          </p>
          <p class="text-sm text-center text-neutral-500">
            You are done for the day
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
