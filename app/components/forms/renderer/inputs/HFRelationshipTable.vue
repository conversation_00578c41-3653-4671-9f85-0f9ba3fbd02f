<script setup lang="ts">
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import type { FormField } from '~/utils/forms/FormField'
import { ModalsConfirmModal, ModalsDynamicFormModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useAssetTypeFormFieldsByAssetTypeQuery } from '~/api/asset-type-form-fields'
import { FormGenerator } from '~/utils/forms/FormGenerator'
import { generateActionsColumn, generateColumnsMetaFromFields } from '~/utils/table/columns-meta.generator'

const props = defineProps({
  name: { type: String, required: true },
  label: { type: String, required: true },
  field: { type: Object as PropType<FormField>, required: true },
  errors: { type: Object as PropType<Record<string, string[]>>, default: () => ({}) },
  isReadOnly: { type: Boolean, default: false },
})

const model = defineModel({
  type: Array as PropType<any[]>,
  default: () => [],
})

const overlay = useOverlay()

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const assetTypeTableFieldStore = useAssetTypeTableFieldStore()

const isMounted = ref(false)

const relationship = assetTypeRelationshipStore.getById(props.field.extra?.parentField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')
const tableFields = assetTypeTableFieldStore.getByAssetTypeId(assetType?.id ?? '')

const { data: formFields } = useAssetTypeFormFieldsByAssetTypeQuery(assetType.id)
const { data: assetTypeFields } = useAssetTypeFieldsQuery()

const formDefinition = computed<FormDefinition>(() => {
  return new FormGenerator(assetTypeFields.value ?? [])
    .generateFormDefinition(formFields.value ?? [])
})

const createModal = overlay.create(ModalsDynamicFormModal, {
  props: {
    formDefinition: formDefinition.value,
    title: relationship?.label,
    data: {},
  },
})
const confirmModal = overlay.create(ModalsConfirmModal)

const columnsMeta = computed(() => {
  const meta = generateColumnsMetaFromFields(tableFields)

  const actionsColumn = generateActionsColumn(
    props.isReadOnly
      ? [{ label: 'View', onClick: onView }]
      : [
          { label: 'Edit', onClick: onEdit },
          { label: 'Delete', onClick: onDelete },
        ],
  )

  return [
    ...meta,
    actionsColumn,
  ]
})

function onCellUpdate({ rowIndex, columnKey, value }: { rowIndex: number, columnKey: string, value: unknown }) {
  const row = model.value[rowIndex]
  if (row && row[columnKey] !== value) {
    Object.assign(row, { [columnKey]: value })
  }
}

onMounted(async () => {
  isMounted.value = true
})

async function onAdd() {
  const emptyEntry = await assetTypeStore.emptyEntry(assetType?.id ?? '')

  model.value = [...model.value, emptyEntry.data]
}

async function onEdit(item: any) {
  const index = model.value.findIndex(entry => entry.id === item.id)
  if (index === -1)
    return

  const overlay = createModal.open({
    formDefinition: formDefinition.value,
    data: { ...model.value[index] },
    errors: fieldErrors(index),
  })

  const result = await overlay.result

  if (!result)
    return

  Object.assign(model.value[index], result)
}

async function onView(item: any) {
  createModal.open({
    formDefinition: formDefinition.value,
    data: item,
    isReadOnly: props.isReadOnly,
  })
}

async function onDelete(item: any) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  model.value = model.value.filter(entry => entry.id !== item.id)
}

function fieldErrors(zeroBasedIndex: number) {
  const startWith = `${props.field.name}.${zeroBasedIndex}.`
  return Object.fromEntries(
    Object.entries(props.errors)
      .filter(([key]) => key.startsWith(startWith))
      .map(([key, value]) => [key.substring(startWith.length), value]),
  )
}

const editTable = ref(true)
const isEditButtonDisabled = ref(false)

function toggleEditTable() {
  editTable.value = !editTable.value
  isEditButtonDisabled.value = true
  setTimeout(() => {
    isEditButtonDisabled.value = false
  }, 500)
}

const rowErrors = computed(() => {
  const result: Record<number, string[]> = {}

  for (const [key, messages] of Object.entries(props.errors)) {
    const match = key.match(/\.(\d+)\./)
    if (match) {
      const index = Number(match[1])
      if (!result[index])
        result[index] = []

      result[index].push(...messages)
    }
  }

  return result
})
</script>

<template>
  <div v-if="isMounted">
    <div class="flex justify-between border-b border-solid text-center my-2">
      <h2 class="font-bold text-lg pb-2">
        {{ relationship?.label }}
      </h2>
      <div class="flex flex-row gap-2">
        <UButton
          :disabled="isEditButtonDisabled"
          :label="editTable ? 'Done' : 'Edit'"
          :icon="editTable ? 'i-ph-check' : 'i-ph-pencil-simple-line'"
          :icon-position="editTable ? 'right' : 'left'"
          variant="link"
          @click="toggleEditTable"
        />
        <ULink v-if="!props.isReadOnly" class="text-sm" @click="onAdd">
          Add {{ relationship?.label }}
        </ULink>
      </div>
    </div>
    <TablesDataTable
      :columns-meta="columnsMeta"
      :data="model"
      :editable="!props.isReadOnly && editTable"
      :row-errors="rowErrors"
      @cell-update="onCellUpdate"
    />
  </div>
</template>
