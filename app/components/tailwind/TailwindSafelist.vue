<template>
  <!--
    This component is used to generate a Tailwind CSS safelist.
    Tailwind classes that are generated dynamically or used conditionally
    need to be included in the safelist to ensure they are not purged
  -->
  <div class="hidden">
    <div class="col-span-1" />
    <div class="col-span-2" />
    <div class="col-span-3" />
    <div class="col-span-4" />
    <div class="col-span-5" />
    <div class="col-span-6" />
    <div class="col-span-7" />
    <div class="col-span-8" />
    <div class="col-span-9" />
    <div class="col-span-10" />
    <div class="col-span-11" />
    <div class="col-span-12" />
  </div>
</template>
