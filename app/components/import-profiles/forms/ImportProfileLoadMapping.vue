<script setup lang="ts">
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { useImportProfileByIdOrFailQuery } from '~/api/import-profiles/queries/useImportProfileQueries'

const props = defineProps<{
  id: string | null
  profile: ImportProfileFormData
}>()

defineEmits<{
  submit: [data: ImportProfileFormData]
  previousStep: []
}>()

useImportProfileByIdOrFailQuery(props.id!, {
  refetchInterval: 5000,
  enabled: !!props.id,
})
</script>

<template>
  <div class="space-y-4">
    <h2 class="text-lg font-bold">
      Loading the mapping
    </h2>
    <UProgress animation="swing" />

    <p>We are currently loading the mapping for the import profile. This may take a moment.</p>
    <p>The status of the import profile will be updated once the mapping is loaded.</p>
  </div>
</template>
