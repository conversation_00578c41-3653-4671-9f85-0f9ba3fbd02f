import type { DirectoryData, ExportProfileData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { createExportProfileApiSchema } from '~/schemas/export-profiles/create-export-profile.schema'
import { updateExportProfileGeneralInfoSchema } from '~/schemas/export-profiles/update-export-profile-general-info.schema'
import { updateExportProfilePathSchema } from '~/schemas/export-profiles/update-export-profile-path.schema'
import { updateExportProfileTemplateSchema } from '~/schemas/export-profiles/update-export-profile-template.schema'
import { useApiClient } from '~/utils/api/api-client'
import { transformOrThrow } from '~/utils/zod/zod-transformer'
import { EXPORT_PROFILE_API_ROUTES } from './export-profile-service.routes'

export class ExportProfileService {
  public static async getAll(): Promise<ExportProfileData[]> {
    const response = await useApiClient()<ApiResponse<ExportProfileData[]>>(
      EXPORT_PROFILE_API_ROUTES.ALL,
    )

    return response.data
  }

  public static async getById(id: string): Promise<ExportProfileData> {
    const response = await useApiClient()<ApiResponse<ExportProfileData>>(
      EXPORT_PROFILE_API_ROUTES.BY_ID(id),
    )

    return response.data
  }

  public static async getByIdOrFail(id: string): Promise<ExportProfileData> {
    const profile = await this.getById(id)

    if (!profile) {
      throw new Error(`Export profile with ID ${id} not found`)
    }

    return profile
  }

  public static async create(data: ExportProfileFormData): Promise<ExportProfileData> {
    const parsedData = transformOrThrow(createExportProfileApiSchema, data)

    const response = await useApiClient()<ApiResponse<ExportProfileData>>(
      EXPORT_PROFILE_API_ROUTES.ALL,
      {
        method: 'POST',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateGeneralInfo(
    id: string,
    data: ExportProfileFormData,
  ): Promise<ExportProfileData> {
    const parsedData = transformOrThrow(updateExportProfileGeneralInfoSchema, data)

    const response = await useApiClient()<ApiResponse<ExportProfileData>>(
      EXPORT_PROFILE_API_ROUTES.UPDATE_GENERAL_INFO(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updatePath(
    id: string,
    data: ExportProfileFormData,
  ): Promise<ExportProfileData> {
    const parsedData = transformOrThrow(updateExportProfilePathSchema, data)

    const response = await useApiClient()<ApiResponse<ExportProfileData>>(
      EXPORT_PROFILE_API_ROUTES.UPDATE_PATH(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateTemplate(
    id: string,
    data: ExportProfileFormData,
  ): Promise<ExportProfileData> {
    const parsedData = transformOrThrow(updateExportProfileTemplateSchema, data)

    const response = await useApiClient()<ApiResponse<ExportProfileData>>(
      EXPORT_PROFILE_API_ROUTES.UPDATE_TEMPLATE(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async delete(id: string): Promise<void> {
    await useApiClient()(
      EXPORT_PROFILE_API_ROUTES.BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }

  public static async directories(
    id: string,
    path: string,
  ): Promise<DirectoryData> {
    const response = await useApiClient()<ApiResponse<DirectoryData>>(
      EXPORT_PROFILE_API_ROUTES.DIRECTORIES(id),
      {
        params: { path },
      },
    )

    return response.data
  }
}
