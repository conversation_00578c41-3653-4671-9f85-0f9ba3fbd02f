<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeRelationshipData, UpdateAssetTypeRelationshipData } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeRelationship: {
    type: Object as PropType<AssetTypeRelationshipData>,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const isPending = ref(false)
const data = ref<UpdateAssetTypeRelationshipData>({
  nullable: props.assetTypeRelationship.nullable ?? false,
  include: props.assetTypeRelationship.include ?? false,
  locked: props.assetTypeRelationship.locked ?? false,
  cascade_delete: props.assetTypeRelationship.cascade_delete ?? false,
  required: props.assetTypeRelationship.required ?? false,
})

async function onSubmit() {
  if (!props.assetTypeRelationship.id || !props.assetTypeRelationship.asset_type_id) {
    throw new Error('Asset type relationship ID and asset type ID are required')
  }

  try {
    isPending.value = true
    await assetTypeRelationshipStore.update(
      props.assetTypeRelationship.asset_type_id,
      props.assetTypeRelationship.id,
      data.value,
    )
    emit('close', false)
    toast.add({ title: 'Master data relationship updated', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error updating master data relationship',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover :title="`Update relationship: ${assetTypeRelationship.label}`" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <USeparator class="col-span-12" />
          <UFormField label="Nullable" class="col-span-6">
            <USwitch v-model="data.nullable" />
          </UFormField>
          <UFormField label="Include" class="col-span-6">
            <USwitch v-model="data.include" />
          </UFormField>
          <UFormField label="Locked" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField label="Cascade delete" class="col-span-6">
            <USwitch v-model="data.cascade_delete" />
          </UFormField>
          <UFormField label="Required" class="col-span-6">
            <USwitch v-model="data.required" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Update
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
