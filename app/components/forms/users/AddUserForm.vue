<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { CreateUserData } from '~/utils/types/api/api'
import { useCreateUserMutation } from '~/api/user/mutations/useUserMutations'

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const isPending = ref(false)
const data = ref<CreateUserData>({
  email: '',
  first_name: '',
  last_name: '',
  password: '',
})

const createUserMutation = useCreateUserMutation()
async function onSubmit() {
  try {
    isPending.value = true
    await createUserMutation.mutateAsync(data.value)
    toast.add({ title: 'User created', color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: 'Error creating user',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover
    title="Add user"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Email" class="col-span-12">
            <UInput v-model="data.email" type="email" placeholder="<EMAIL>" />
          </UFormField>
          <UFormField label="First name" class="col-span-6">
            <UInput v-model="data.first_name" placeholder="john" />
          </UFormField>
          <UFormField label="Last name" class="col-span-6">
            <UInput v-model="data.last_name" placeholder="doe" />
          </UFormField>
          <UFormField label="Password" class="col-span-12">
            <GeneralPasswordInput v-model="data.password" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Create
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
