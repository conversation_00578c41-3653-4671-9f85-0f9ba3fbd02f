import type { AssetTypeTableFieldData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeTableFieldKeys } from '../keys/asset-type-table-field.keys'

export class AssetTypeTableFieldCacheService {
  private static getCachedAssetTypeTableFields(): AssetTypeTableFieldData[] | undefined {
    const { getCachedQuery } = useCachedQueries()
    return getCachedQuery<AssetTypeTableFieldData[]>([assetTypeTableFieldKeys.all]).value
  }

  static getByAssetTypeId(assetTypeId: string): AssetTypeTableFieldData[] {
    const fields = this.getCachedAssetTypeTableFields()
    return fields?.filter((f: AssetTypeTableFieldData) => f.asset_type_id === assetTypeId) || []
  }
}
