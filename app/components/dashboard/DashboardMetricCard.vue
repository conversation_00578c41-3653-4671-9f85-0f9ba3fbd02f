<script lang="ts" setup>
import type { MetricData } from '~/utils/types/api/api'

defineProps<{
  metric: MetricData
}>()

function getPercentageDifference(metric: MetricData): string | undefined {
  // Hide the percentage difference for now
  return undefined

  const current = metric.value.current
  const previous = metric.value.previous

  if (!previous)
    return undefined
  const difference = current - previous

  if (difference === 0)
    return '0%'
  if (previous === 0)
    return difference > 0 ? undefined : '0%'

  const percentage = (difference / previous) * 100
  return `${Math.abs(percentage).toFixed(0)}%`
}
</script>

<template>
  <UCard variant="subtle">
    <template #default>
      <div class="flex flex-col gap-2">
        <h2 class="text-2xl font-light">
          {{ metric.value.current }}
        </h2>
        <p class="text-sm">
          {{ metric.description }}
        </p>
        <UBadge
          v-if="getPercentageDifference(metric)"
          :color="metric.value.current - metric.value.previous < 0 ? 'error' : 'success'"
          variant="soft"
          class="self-start rounded-full"
        >
          {{ metric.value.current - metric.value.previous < 0 ? '-' : '+' }} {{ getPercentageDifference(metric) }}
        </UBadge>
      </div>
    </template>
  </UCard>
</template>
