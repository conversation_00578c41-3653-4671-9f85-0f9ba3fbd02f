import type { ColumnDef } from '@tanstack/vue-table'
import type { SortingStrategy } from './sorting/SortingStrategy'
import { UButton } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'
import { MultiSortingStrategy } from './sorting/MultiSortingStrategy'

export class TextColumn<T> extends BaseColumnWithField<T> {
  private sortingStategy: SortingStrategy = new MultiSortingStrategy()
  constructor(
    field: keyof T,
    label: string,
      sortable: boolean = false,
      valueCallback?: (obj: T) => string,
  ) {
    super(field, label, sortable, ColumnType.Text, valueCallback)
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: ({ column }) => {
        if (!column.getCanSort()) {
          return this.label
        }
        return h(UButton, {
          color: 'neutral',
          variant: 'ghost',
          label: this.label,
          icon: this.sortingStategy.getSortIcon(column.getIsSorted()),
          class: '-mx-2.5',
          onClick: e => this.sortingStategy.handleClick(e, column),
        })
      },
      enableSorting: this.sortable,
      cell: ({ row }) => {
        return this.getField(row.original)
      },
    }
  }
}
