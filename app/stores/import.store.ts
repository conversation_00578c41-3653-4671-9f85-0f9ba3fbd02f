import type { ImportData, UpdateImportData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useImportStore = defineStore('import', () => {
  async function fetchAll() {
    return await useApiClient()<ApiResponse<ImportData[]>>(
      `api/dynamic-assets/imports`,
    )
  }

  async function fetchById(
    id: string,
  ) {
    return await useApiClient()<ApiResponse<ImportData>>(
      `api/dynamic-assets/imports/${id}`,
    )
  }

  async function create(
    assetTypeId: string,
    file: File,
    delimiter: string,
  ) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('asset_type_id', assetTypeId)
    formData.append('delimiter', delimiter)

    return await useApiClient()<ApiResponse<ImportData>>(
      `api/dynamic-assets/imports`,
      {
        method: 'POST',
        body: formData,
        headers: {
          Accept: 'application/json',
        },
      },
    )
  }

  async function update(
    id: string,
    updateImportData: UpdateImportData,
  ) {
    return await useApiClient()<ApiResponse<ImportData>>(
      `api/dynamic-assets/imports/${id}`,
      {
        method: 'PUT',
        body: JSON.stringify(updateImportData),
      },
    )
  }

  async function destroy(
    id: string,
  ) {
    return await useApiClient()<ApiResponse<void>>(
      `api/dynamic-assets/imports/${id}`,
      {
        method: 'DELETE',
      },
    )
  }

  return {
    fetchAll,
    fetchById,
    create,
    update,
    destroy,
  }
})
