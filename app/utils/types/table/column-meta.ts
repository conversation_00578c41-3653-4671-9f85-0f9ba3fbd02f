import type { ColumnType } from './ColumnType'

export interface ColumnMeta {
  id: string
  key: string
  label: string
  type: ColumnType
  sortable?: boolean
  editable?: boolean
  weight: number
  config?: {
    suffix?: string | null
    filterable?: boolean
    relationship?: {
      asset_type_relationship_id: string
      asset_type_field_id: string
      display_field: string
    }
    field?: {
      template?: string | null
    }
    buttons?: {
      label: string
      onClick: (row: Record<string, any>) => void
    }[]
    [key: string]: unknown
    hasError?: (row: any) => boolean
  }
}
