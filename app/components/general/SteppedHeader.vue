<script setup lang="ts">
interface Props {
  title: string
  showStepper?: boolean
  amountOfSteps?: number
  currentStep?: number
  highestReachedStep?: number
  disabledSteps?: boolean
}

withDefaults(defineProps<Props>(), {
  showStepper: false,
  amountOfSteps: 0,
  highestHitStep: 1,
})

const emit = defineEmits<{
  cancel: []
  changeStep: [step: number]
}>()
</script>

<template>
  <UHeader class="flex items-center justify-between border-b border-gray-200 px-6 py-4 bg-white">
    <template #left>
      <h1 class="text-lg font-bold">
        {{ title }}
      </h1>
    </template>

    <div v-if="showStepper && amountOfSteps > 0" class="flex items-center gap-2">
      <template v-for="stepNumber in amountOfSteps" :key="stepNumber">
        <UButton
          v-if="currentStep === stepNumber"
          class="flex h-9 w-9 items-center justify-center rounded-full text-sm font-medium"
          color="neutral"
          variant="solid"
          :disabled="disabledSteps"
          @click="emit('changeStep', stepNumber)"
        >
          {{ stepNumber }}
        </UButton>

        <UButton
          v-else-if="highestReachedStep && highestReachedStep >= stepNumber"
          class="flex h-9 w-9 items-center justify-center rounded-full text-sm font-medium"
          color="success"
          variant="subtle"
          :disabled="disabledSteps"
          @click="emit('changeStep', stepNumber)"
        >
          <UIcon name="i-ph-check-bold" class="w-4 h-4" />
        </UButton>

        <UButton
          v-else-if="highestReachedStep === stepNumber"
          class="flex h-9 w-9 items-center justify-center rounded-full text-sm font-medium bg-white border border-gray-300 text-gray-600"
          color="neutral"
          variant="subtle"
          :disabled="disabledSteps"
          @click="emit('changeStep', stepNumber)"
        >
          {{ stepNumber }}
        </UButton>

        <div
          v-else
          class="flex h-9 w-9 items-center justify-center rounded-full text-sm font-medium bg-white border border-gray-300 text-gray-600 cursor-not-allowed"
        >
          {{ stepNumber }}
        </div>
      </template>
    </div>

    <template #right>
      <UButton
        variant="outline"
        color="error"
        label="Cancel"
        @click="emit('cancel')"
      >
        Cancel
      </UButton>
    </template>
  </UHeader>
</template>
