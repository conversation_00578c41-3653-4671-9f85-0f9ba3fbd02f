<script setup lang="ts">
import type { ImportProfileScheduleFormData } from '~/schemas/import-profiles/import-profile-schedule.schema'
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { useCronSchedule } from '~/composables/cron/useCronSchedule'
import { importProfileScheduleSchema } from '~/schemas/import-profiles/import-profile-schedule.schema'
import { Day } from '~/utils/dates/day.enum'

const props = defineProps<{
  profile: ImportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const dayButtons = [
  { label: 'M', day: Day.MONDAY },
  { label: 'T', day: Day.TUESDAY },
  { label: 'W', day: Day.WEDNESDAY },
  { label: 'T', day: Day.THURSDAY },
  { label: 'F', day: Day.FRIDAY },
  { label: 'S', day: Day.SATURDAY },
  { label: 'S', day: Day.SUNDAY },
]

const { fromCron, toCron } = useCronSchedule()

const formState = ref<ImportProfileScheduleFormData>(
  fromCron(props.profile.schedule),
)

watch(() => props.profile.schedule, (newSchedule) => {
  formState.value = fromCron(newSchedule)
}, { immediate: true })

function toggleDay(day: Day) {
  const index = formState.value.days.indexOf(day)

  if (index === -1) {
    formState.value.days.push(day)
  }
  else {
    formState.value.days.splice(index, 1)
  }
}

function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  emit('submit', {
    ...props.profile,
    schedule: toCron(formState.value),
  }, isSaveAndBackToOverview)
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="importProfileScheduleSchema"
    class="space-y-4"
  >
    <h2 class="text-lg font-bold">
      Schedule
    </h2>

    <UFormField label="Select the day(s) you want to run this profile" name="days" required>
      <div class="flex gap-2">
        <UButton
          v-for="item in dayButtons"
          :key="item.day"
          :label="item.label"
          size="md"
          :variant="formState.days.includes(item.day) ? 'solid' : 'outline'"
          color="neutral"
          class="w-9 h-9 justify-center"
          @click="toggleDay(item.day)"
        />
      </div>
    </UFormField>

    <UFormField label="Time to run this profile" name="time" required>
      <UInput
        v-model="formState.time"
        type="time"
        step="60"
        placeholder="HH:MM"
        block
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Save import profile</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
