import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory'
import { AssetTypeTableFieldCacheService } from '../services/asset-type-table-field-cache.service'
import { AssetTypeTableFieldService } from '../services/asset-type-table-field.service'

export const assetTypeTableFieldKeys = createQueryKeys('assetTypeTableFields', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeTableFieldService.getAll(),
  },
  byAssetTypeId: (assetTypeId: string) => ({
    queryKey: [assetTypeId],
    queryFn: () => AssetTypeTableFieldCacheService.getByAssetTypeId(assetTypeId),
  }),
})
