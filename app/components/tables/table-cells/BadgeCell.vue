<script setup lang="ts">
import { UBadge } from '#components'
import { computed } from 'vue'

type UBadgeColor = 'neutral' | 'error' | 'primary' | 'secondary' | 'success' | 'info' | 'warning'

const props = defineProps<{
  value: string | null | undefined
  config?: {
    mode: 'threshold' | 'status'
    statuses?: Record<string, UBadgeColor>
    thresholds?: { min: number, color: UBadgeColor }[]
  }
}>()

const displayValue = computed(() => {
  if (!props.value)
    return ''
  return props.value
    .replace(/_/g, ' ')
    .replace(/\b\w/g, c => c.toUpperCase())
})

const badgeColor = computed<UBadgeColor>(() => {
  if (!props.value || !props.config)
    return 'neutral'

  const value = props.value.toLowerCase()

  if (props.config.mode === 'status' && props.config.statuses) {
    const normalizedStatuses = Object.entries(props.config.statuses).reduce(
      (map, [key, color]) => {
        map[key.toLowerCase()] = color
        return map
      },
      {} as Record<string, UBadgeColor>,
    )

    return normalizedStatuses[value] ?? 'neutral'
  }

  if (props.config.mode === 'threshold') {
    const numericValue = Number.parseFloat(value)
    if (Number.isNaN(numericValue))
      return 'neutral'

    const sortedThresholds = [...(props.config.thresholds ?? [])].sort((a, b) => b.min - a.min)
    const threshold = sortedThresholds.find(t => numericValue >= t.min)
    return threshold?.color ?? 'neutral'
  }

  return 'neutral'
})
</script>

<template>
  <UBadge :color="badgeColor" variant="subtle">
    {{ displayValue }}
  </UBadge>
</template>
