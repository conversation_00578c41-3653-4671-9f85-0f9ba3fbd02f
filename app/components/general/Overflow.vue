<script setup lang="ts">
defineProps({
  bgColor: {
    type: String,
    default: 'neutral-50',
  },
})

const parentElement = ref<HTMLElement | null>(null)
const slotElement = ref<HTMLElement | null>(null)
const parentWidth = ref(0)
const slotWidth = ref(0)
const scrollPosition = ref(0)
const scrollStep = 200

async function updateWidths() {
  await nextTick()
  if (parentElement.value) {
    parentWidth.value = parentElement.value.offsetWidth
  }
  if (slotElement.value && slotElement.value.firstElementChild) {
    slotWidth.value = slotElement.value.firstElementChild.scrollWidth
  }
}

const hasOverflow = computed(() => {
  return parentWidth.value < slotWidth.value
})

const canScrollRight = computed(() => {
  return hasOverflow.value && scrollPosition.value < (slotWidth.value - parentWidth.value)
})

const canScrollLeft = computed(() => {
  return scrollPosition.value > 0
})

onMounted(() => {
  updateWidths()
  window.addEventListener('resize', updateWidths)
})

function onScrollRight() {
  if (!parentElement.value || !canScrollRight.value)
    return

  const maxScroll = slotWidth.value - parentWidth.value
  scrollPosition.value = Math.min(maxScroll, scrollPosition.value + scrollStep)

  if (slotElement.value) {
    slotElement.value.style.transform = `translateX(-${scrollPosition.value}px)`
  }
}

function onScrollLeft() {
  if (!parentElement.value || !canScrollLeft.value)
    return

  scrollPosition.value = Math.max(0, scrollPosition.value - scrollStep)

  if (slotElement.value) {
    slotElement.value.style.transform = `translateX(-${scrollPosition.value}px)`
  }
}

// Expose rerender function to parent components
async function rerender() {
  await updateWidths()
}

defineExpose({
  rerender,
})

// via-neutral-50 to-neutral-50
// via-white to-white
</script>

<template>
  <div ref="parentElement" class="overflow-hidden relative h-12">
    <div ref="slotElement" class="absolute transition-transform duration-300">
      <slot />
    </div>
    <div
      v-if="canScrollLeft"
      class="absolute top-0 left-0 h-full flex items-center justify-start pl-4 w-24 z-10 cursor-pointer"
      :class="`bg-gradient-to-l from-transparent via-${bgColor} to-${bgColor}`"
      @click="onScrollLeft"
    >
      <UIcon name="i-ph-arrow-left" />
    </div>
    <div
      v-if="canScrollRight"
      class="absolute top-0 right-0 h-full flex items-center justify-end pr-4 w-24 z-10 cursor-pointer"
      :class="`bg-gradient-to-r from-transparent via-${bgColor} to-${bgColor}`"
      @click="onScrollRight"
    >
      <UIcon name="i-ph-arrow-right" />
    </div>
  </div>
</template>
