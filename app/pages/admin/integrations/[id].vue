<script setup lang="ts">
import SteppedHeader from '~/components/general/SteppedHeader.vue'
import IntegrationsFormContainer from '~/components/integrations/IntegrationsFormContainer.vue'

definePageMeta({
  middleware: ['auth'],
  layout: 'empty',
})

const route = useRoute()
const integrationId = route.params.id as string
const isCreateMode = integrationId === 'new'
const integrationType = route.query.provider as string || 'api'

function handleCancel() {
  navigateTo('/admin/integrations')
}
</script>

<template>
  <div class="min-h-screen bg-neutral-50 flex flex-col">
    <SteppedHeader
      title="Configure Integration"
      :show-stepper="false"
      @cancel="handleCancel"
    />

    <UContainer class="hidden md:block max-w-3xl md:py-16">
      <UCard class="px-6 py-4 shadow-md">
        <IntegrationsFormContainer
          :integration-id="integrationId"
          :integration-type="integrationType"
          :is-create-mode="isCreateMode"
        />
      </UCard>
    </UContainer>

    <UCard class="md:hidden px-6 py-4 bg-white flex-1">
      <IntegrationsFormContainer
        :integration-id="integrationId"
        :integration-type="integrationType"
        :is-create-mode="isCreateMode"
      />
    </UCard>
  </div>
</template>
