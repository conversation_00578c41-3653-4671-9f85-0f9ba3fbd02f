import type { EditAssetTypeFieldData, UpsertAssetTypeFieldData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeFieldKeys } from '../keys/asset-type-field.keys'
import { AssetTypeFieldService } from '../services/asset-type-field.service'

export function useCreateAssetTypeFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, data }: { assetTypeId: string, data: UpsertAssetTypeFieldData }) =>
      AssetTypeFieldService.create(assetTypeId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useUpdateAssetTypeFieldMutation() {
  return useMutation({
    mutationFn: ({
      assetTypeId,
      fieldId,
      data,
    }: {
      assetTypeId: string
      fieldId: string
      data: EditAssetTypeFieldData
    }) => AssetTypeFieldService.update(assetTypeId, fieldId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, fieldId }: { assetTypeId: string, fieldId: string }) =>
      AssetTypeFieldService.delete(assetTypeId, fieldId),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}
