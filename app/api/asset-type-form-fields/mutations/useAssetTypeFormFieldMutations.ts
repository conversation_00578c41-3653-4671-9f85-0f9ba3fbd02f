import type { UpsertAssetTypeFormFieldData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeFormFieldKeys } from '../keys/asset-type-form-field.keys'
import { AssetTypeFormFieldService } from '../services/asset-type-form-field.service'

export function useCreateAssetTypeFormFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, data }: { assetTypeId: string, data: UpsertAssetTypeFormFieldData }) =>
      AssetTypeFormFieldService.create(assetTypeId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useUpdateAssetTypeFormFieldMutation() {
  return useMutation({
    mutationFn: ({
      assetTypeId,
      id,
      data,
    }: {
      assetTypeId: string
      id: string
      data: UpsertAssetTypeFormFieldData
    }) => AssetTypeFormFieldService.update(assetTypeId, id, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeFormFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeFormFieldService.delete(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useGenerateAssetTypeFormFieldsMutation() {
  return useMutation({
    mutationFn: (assetTypeId: string) => AssetTypeFormFieldService.generate(assetTypeId),
    onSuccess: (_, assetTypeId) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useMoveUpAssetTypeFormFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeFormFieldService.moveUp(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useMoveDownAssetTypeFormFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeFormFieldService.moveDown(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeFormFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}
