<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { UpsertAssetTypeIndexData } from '~/utils/types/api/api'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { AssetTypeFieldType, AssetTypeIndexType } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const assetTypeIndexStore = useAssetTypeIndexStore()

const isPending = ref(false)
const data = ref<UpsertAssetTypeIndexData>({
  asset_type_field_id: '',
  type: AssetTypeIndexType.Index,
  locked: false,
})

const {
  data: allAssetTypeFields,
} = useAssetTypeFieldsQuery()

const assetTypeFields = computed(() => {
  const fields = allAssetTypeFields.value || []
  return fields.filter(field => field.asset_type_id === props.assetTypeId)
})

const filteredItems = computed(() => {
  const items = assetTypeFields.value || []

  return items
    .filter(field => field.type === AssetTypeFieldType.String)
    .map(field => ({
      id: field.id,
      label: field.label,
      type: 'item' as const,
    }))
})

async function onSubmit() {
  try {
    isPending.value = true
    await assetTypeIndexStore.create(props.assetTypeId, data.value)
    toast.add({ title: 'Master data index created', color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: 'Error creating master data index',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover
    title="Add index"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Select field" class="col-span-12">
            <USelect
              v-model="data.asset_type_field_id"
              :items="filteredItems"
              label-key="label"
              value-key="id"
              placeholder="Select field"
              class="w-full"
            />
          </UFormField>
          <UFormField label="Type" class="col-span-12">
            <USelect
              v-model="data.type"
              :items="Object.values(AssetTypeIndexType)"
              class="w-full"
            />
          </UFormField>
          <UFormField label="Locked" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Create
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
