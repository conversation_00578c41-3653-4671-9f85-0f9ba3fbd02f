<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { CreateChannelData, MynumaData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import { useCloneDeep } from '#imports'
import { ChannelProvider } from '~/utils/types/api/api'

// Supporting types
interface BaseChannel<T> {
  id?: string
  label: string
  is_active: boolean
  data: T
}

const props = defineProps({
  data: {
    type: Object as PropType<BaseChannel<MynumaData>>,
    default: () => ({
      label: '',
      is_active: false,
      data: {
        token: null,
        aiMapping: null,
      },
    }),
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const overlay = useOverlay()

const channelStore = useChannelsStore()

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)

const channelData = ref<BaseChannel<MynumaData>>(useCloneDeep(props.data))
const creating = !channelData.value.id
const isPending = ref(false)

async function create() {
  const submitData: CreateChannelData = {
    ...channelData.value,
    provider: ChannelProvider.MYNUMA,
  }

  await channelStore.create(submitData)
}

async function update() {
  await channelStore.update(props.data.id as string, channelData.value)
}

async function onDelete() {
  if (creating)
    return

  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    isPending.value = true
    await channelStore.remove(props.data.id as string)
    toast.add({ title: 'Channel deleted', color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: 'Error deleting channel',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}

async function onSubmit() {
  try {
    isPending.value = true
    if (creating) {
      await create()
    }
    else {
      await update()
    }
    toast.add({ title: `Channel ${creating ? 'created' : 'updated'}`, color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: `Error while ${creating ? 'creating' : 'updating'} channel`,
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover :title="`${creating ? 'Add' : 'Edit'} channel`" :overlay="false">
    <template #body>
      <form class="grid grid-cols-12 gap-4" @submit.prevent="onSubmit">
        <UFormField label="Label" class="col-span-6">
          <UInput v-model="channelData.label" />
        </UFormField>
        <UFormField label="Active" class="col-span-3">
          <USwitch v-model="channelData.is_active" />
        </UFormField>
        <UFormField label="Token" class="col-span-12">
          <UInput v-model="channelData.data.token" />
        </UFormField>
        <UFormField label="AI Mapping" class="col-span-12">
          <UTextarea v-model="channelData.data.aiMapping" />
        </UFormField>
        <div class="col-span-12 flex gap-2 justify-between">
          <UButton
            v-if="!creating"
            type="button"
            variant="outline"
            color="error"
            :loading="isPending"
            :disabled="isPending"
            @click="onDelete"
          >
            Delete
          </UButton>
          <UButton
            type="submit"
            :loading="isPending"
            :disabled="isPending"
          >
            {{ creating ? 'Create' : 'Update' }}
          </UButton>
        </div>
      </form>
    </template>
  </USlideover>
</template>
