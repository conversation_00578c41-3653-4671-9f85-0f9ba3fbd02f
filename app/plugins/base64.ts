async function encodeBase64(value: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(value)
  const digest = await window.crypto.subtle.digest('SHA-256', data)

  const bytes = new Uint8Array(digest)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    const byte = bytes[i]

    if (!byte) {
      continue
    }

    binary += String.fromCharCode(byte)
  }
  const base64 = btoa(binary)
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
}

export default defineNuxtPlugin(() => {
  return {
    provide: {
      encodeBase64,
    },
  }
})
