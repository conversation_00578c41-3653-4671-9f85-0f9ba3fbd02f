# Auth Tests Documentation

## Overview

Tests de autenticación para el sistema OAuth con PKCE (Proof Key for Code Exchange) implementado en el proyecto. Los tests utilizan **MSW (Mock Service Worker)** para interceptar llamadas HTTP reales, proporcionando un entorno de testing más realista.

## Estructura

```
tests/auth/
├── useAuth.spec.ts           # Tests principales del composable useAuth  
├── token-response.builder.ts # Builder para generar mocks de TokenResponse
└── auth-service.handlers.ts  # Handlers MSW para endpoints de auth

tests/setup/
└── test-setup.ts            # Configuración de MSW server
```

## Tests Implementados

### ✅ Login Success

Ubicación: `tests/auth/useAuth.spec.ts`

**Casos de prueba:**

1. **PKCE Challenge Generation and Redirect**
   - Verifica que se genere correctamente el challenge PKCE
   - Confirma que el verifier se almacene en sessionStorage
   - Valida que la redirección a la URL de OAuth sea correcta
   - Verifica parámetros de la URL: `code_challenge`, `client_id`, `redirect_uri`

2. **OAuth Parameters Validation**
   - Confirma parámetros OAuth correctos: `response_type=code`, `code_challenge_method=S256`, `prompt=login`

3. **Error Handling**
   - Missing authorization code: Lanza error cuando no hay código en query params
   - Missing PKCE verifier: Lanza error cuando no hay verifier en sessionStorage
   - **Invalid authorization code via MSW**: Maneja errores HTTP 400 de manera realista

4. **Token Exchange via MSW**
   - Intercambia código por token a través de MSW mock server
   - Verifica que se llame el callback `onTokenReceived` con el token correcto
   - Valida estructura completa del TokenResponse

## Mock Service Worker (MSW)

### Configuración

**MSW Server Setup**: `tests/setup/test-setup.ts`
```typescript
export const mockServer = setupServer(...authServiceHandlers)

beforeAll(() => mockServer.listen())
afterEach(() => mockServer.resetHandlers())
afterAll(() => mockServer.close())
```

### Endpoints Mockeados

**`POST /oauth/token`**: `tests/auth/auth-service.handlers.ts`

- ✅ **Authorization Code Flow**: 
  - Success: `grant_type=authorization_code` + valid code/verifier
  - Error 400: Invalid authorization code
  - Error 400: Invalid code verifier

- ✅ **Refresh Token Flow**:
  - Success: `grant_type=refresh_token` + valid refresh token  
  - Error 401: Invalid refresh token

- ✅ **Error Scenarios**:
  - Error 400: Missing required parameters
  - Error 400: Unsupported grant type

**`POST /auth/logout`**: 
- ✅ Success 200: Logout confirmation

### Ventajas de MSW

1. **Realismo**: Intercepta llamadas HTTP reales en lugar de mock directo
2. **Isolation**: Cada endpoint puede responder independientemente
3. **Error Testing**: Simula respuestas de error HTTP reales
4. **Type Safety**: Responses tipadas con `TokenResponse`

## Mocks y Utilidades

### Token Response Builder

```typescript
buildTokenResponse(overrides?: Partial<TokenResponse>): TokenResponse
```

Genera mocks de `TokenResponse` con valores por defecto:
- `token_type`: "Bearer"
- `expires_in`: 3600
- `access_token`: "mock-access-token-123456789"  
- `refresh_token`: "mock-refresh-token-987654321"
- `id_token`: "mock-id-token-111222333"

### Mocks Configurados

- **MSW Server**: Intercepta requests HTTP `/oauth/token` y `/auth/logout`
- **pkce-challenge**: Mock del módulo para generar challenge/verifier consistentes
- **window.location**: Mock para capturar redirecciones
- **sessionStorage**: Mock para verificar almacenamiento de verifier
- **useRoute**: Mock de Nuxt para simular query parameters

## Test Results

```bash
✓ useAuth > login success > generates PKCE challenge, stores verifier, and redirects to OAuth URL
✓ useAuth > login success > sets correct OAuth parameters in redirect URL  
✓ useAuth > error cases > throws error when authorization code is missing
✓ useAuth > error cases > throws error when PKCE verifier is missing from sessionStorage
✓ useAuth > error cases > handles invalid authorization code via MSW
✓ useAuth > token exchange > exchanges code for token via MSW and calls onTokenReceived

6 tests passed ✅
```

## Diferencias con Proyecto de Referencia

### ✅ Mejoras Implementadas

1. **MSW Integration**: ✅ Implementado como en el proyecto de referencia
2. **HTTP Interception**: ✅ Intercepta llamadas reales vs mock directo  
3. **Realistic Error Handling**: ✅ Respuestas HTTP realistas
4. **Type Safety**: ✅ Requests/responses totalmente tipados

### ❌ Elementos No Aplicables

- **usePKCE composable**: No existe en el proyecto actual, se usa la librería `pkce-challenge`
- **@pinia/testing**: No está instalado, se removió la dependencia

### 🔄 Adaptaciones Realizadas

1. **PKCE Implementation**: Uso de `pkce-challenge` librería en lugar de implementación custom
2. **Session Storage**: Uso directo de sessionStorage en lugar de wrapper custom  
3. **MSW Setup**: Configuración específica para el proyecto actual
4. **Test Structure**: Organizados por funcionalidad (login success, error cases, token exchange)

## Ejecutar Tests

```bash
# Ejecutar todos los tests de auth
npm test tests/auth/

# Ejecutar test específico  
npm test tests/auth/useAuth.spec.ts

# Ejecutar con watch mode
npm run test:watch tests/auth/
```

## Próximos Tests a Implementar

- [ ] **Invalid Credentials**: Tests para credenciales inválidas con MSW
- [ ] **Session Restore**: Tests para restauración de sesión
- [ ] **Token Refresh**: Tests para renovación automática de tokens vía MSW
- [ ] **Logout Flow**: Tests para flujo de logout con MSW
- [ ] **Network Errors**: Tests para errores de red y timeout

## Notas Técnicas

- **Vitest Environment**: Configurado para `nuxt` environment
- **DOM Environment**: `jsdom` para manipulación de window/sessionStorage  
- **Nuxt Test Utils**: Uso de `@nuxt/test-utils/runtime` para mocks de Nuxt
- **MSW Version**: `msw` para Node.js environment con `setupServer`
- **Real HTTP Calls**: Tests usan `$fetch` real que es interceptado por MSW

## Technical Implementation

### MSW Handler Structure

```typescript
// Real HTTP interception
http.post(`${API_BASE_URL}/oauth/token`, async ({ request }) => {
  const body = await request.text()
  const params = new URLSearchParams(body)
  
  // Handle different grant types realistically
  if (grantType === 'authorization_code') {
    // Validate code + verifier
    return HttpResponse.json(buildTokenResponse())
  }
  
  if (grantType === 'refresh_token') {
    // Handle refresh flow
    return HttpResponse.json(buildTokenResponse({ 
      access_token: 'new-token' 
    }))
  }
})
```

### Test Implementation

```typescript
// Real $fetch call intercepted by MSW
fetchToken: async (code, verifier): Promise<TokenResponse> => {
  const response = await $fetch<TokenResponse>('/oauth/token', {
    method: 'POST',
    baseURL: 'https://test.app',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      code_verifier: verifier,
      // ... other params
    }).toString(),
  })
  return response
}
```

Esta implementación proporciona tests mucho más realistas y robustos que verifican el comportamiento completo del flujo de autenticación OAuth con PKCE. 