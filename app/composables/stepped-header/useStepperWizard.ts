import { computed, ref } from 'vue'
import { z } from 'zod'

export function useStepperWizard<T extends string>(steps: readonly [T, ...T[]]) {
  const currentStep = ref<T>(steps[0])

  const currentStepIndex = computed(() => steps.indexOf(currentStep.value))
  const highestReachedStepIndex = ref(currentStepIndex.value)

  const totalSteps = steps.length

  function getStepAtIndex(index: number): T | undefined {
    return steps[index] ?? undefined
  }

  function getStepAtNumber(stepNumber: number): T | undefined {
    return getStepAtIndex(stepNumber - 1)
  }

  function getStepIndex(step: T): number {
    return steps.indexOf(step)
  }

  function goToStep(step: T | number) {
    const resolvedStep = typeof step === 'number' ? getStepAtNumber(step) : step

    if (!resolvedStep || !steps.includes(resolvedStep))
      return

    currentStep.value = resolvedStep
    highestReachedStepIndex.value = Math.max(highestReachedStepIndex.value, getStepIndex(resolvedStep))
  }

  function goToNextStep() {
    const next = getStepAtIndex(currentStepIndex.value + 1)
    if (next) {
      goToStep(next)
    }
  }

  function goToPreviousStep() {
    const prev = getStepAtIndex(currentStepIndex.value - 1)
    if (prev)
      goToStep(prev)
  }

  function safeParseStep(raw: unknown): T | undefined {
    return steps.includes(raw as T) ? (raw as T) : undefined
  }

  function resetSteps() {
    currentStep.value = steps[0]
    highestReachedStepIndex.value = 0
  }

  function getHighestPossibleStep(): T {
    return steps[steps.length - 1] ?? steps[0]
  }

  function setHighestReachedStep(step: T | number) {
    const resolvedStep = typeof step === 'number' ? getStepAtNumber(step) : step

    if (!resolvedStep || !steps.includes(resolvedStep))
      return

    const index = getStepIndex(resolvedStep)
    if (index > highestReachedStepIndex.value) {
      highestReachedStepIndex.value = index
    }

    if (currentStepIndex.value > index) {
      goToStep(resolvedStep)
    }
  }

  function isCurrentStep(step: string | T | number): boolean {
    const resolvedStep = typeof step === 'number'
      ? getStepAtNumber(step)
      : typeof step === 'string'
        ? safeParseStep(step)
        : step

    if (!resolvedStep) {
      return false
    }

    return currentStep.value === resolvedStep
  }

  const stepSchema = z.enum([...steps] as [T, ...T[]])

  return {
    currentStep,
    currentStepIndex,
    highestReachedStepIndex,
    getHighestPossibleStep,

    currentStepNumber: computed(() => currentStepIndex.value + 1),
    highestReachedStepNumber: computed(() => highestReachedStepIndex.value + 1),

    goToStep,
    goToNextStep,
    goToPreviousStep,

    setHighestReachedStep,
    resetSteps,

    steps,
    totalSteps,
    isFirstStep: computed(() => currentStepIndex.value === 0),
    isLastStep: computed(() => currentStepIndex.value === totalSteps - 1),
    isCurrentStep,
    stepSchema,
    parseStep: safeParseStep,
  }
}
