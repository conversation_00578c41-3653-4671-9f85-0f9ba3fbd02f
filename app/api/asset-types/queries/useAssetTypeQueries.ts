import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeKeys } from '../keys/asset-type.keys'

export function useAssetTypesQuery() {
  return useQuery({
    ...assetTypeKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeQuery(id: string) {
  return useQuery({
    ...assetTypeKeys.byId(id),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeByTableNameQuery(tableName: string) {
  return useQuery({
    ...assetTypeKeys.byTableName(tableName),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeEmptyEntryQuery(id: string) {
  return useQuery({
    ...assetTypeKeys.emptyEntry(id),
    staleTime: CACHE_CONFIG.INFITE,
  })
}
