import type { ModelContract } from '~/utils/environment/model.contract'
import { StockModel } from '~/utils/environment/stock.model'
import { TransportModel } from '~/utils/environment/transport.model'

export class ModelFactory {
  create(defaultModel: string | null): ModelContract {
    if (defaultModel === 'dyn_transport_order') {
      return new TransportModel()
    }
    return new StockModel()
  }
}
