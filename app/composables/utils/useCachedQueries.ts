import type { QueryKey } from '@tanstack/vue-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'
import { queryClient } from '~/plugins/vue-query'

export function useCachedQueries() {
  function getCachedQuery<T>(queryKey: QueryKey): ComputedRef<T | undefined> {
    return computed(() => {
      return queryClient.getQueryData<T>(queryKey)
    })
  }

  function hasCachedQuery(queryKey: QueryKey): boolean {
    return queryClient.getQueryData(queryKey) !== undefined
  }

  return {
    getCachedQuery,
    hasCachedQuery,
  }
}
