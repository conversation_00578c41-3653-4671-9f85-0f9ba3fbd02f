import type { ModelContract } from '~/utils/environment/model.contract'
import type { SettingsData, UpdateSettingsData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'
import { ModelFactory } from '~/utils/environment/model.factory'

export const useSettingsStore = defineStore('settings', () => {
  const environment = ref<ModelContract>()
  const settings = ref<SettingsData>()

  // API functions
  async function hydrate() {
    const response = await useApiClient()<ApiResponse<SettingsData>>('api/settings')

    settings.value = response.data
    environment.value = new ModelFactory().create(response.data.defaultModel)
  }

  async function update(data: UpdateSettingsData) {
    const response = await useApiClient()<ApiResponse<SettingsData>>(
      'api/settings',
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    settings.value = response.data
    environment.value = new ModelFactory().create(response.data.defaultModel)
  }

  return {
    settings,
    environment,
    hydrate,
    update,
  }
})
