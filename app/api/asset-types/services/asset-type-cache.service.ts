import type { AssetTypeData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeKeys } from '../keys/asset-type.keys'

export class AssetTypeCacheService {
  private static getCachedAssetTypes(): AssetTypeData[] | undefined {
    const { getCachedQuery } = useCachedQueries()
    return getCachedQuery<AssetTypeData[]>([assetTypeKeys.all]).value
  }

  static getById(id: string): AssetTypeData | undefined {
    const assetTypes = this.getCachedAssetTypes()
    return assetTypes?.find((at: AssetTypeData) => at.id === id)
  }

  static getByIdOrFail(id: string): AssetTypeData {
    const assetType = this.getById(id)

    if (!assetType) {
      throw new Error(`AssetType with id ${id} not found`)
    }

    return assetType
  }

  static getByTableName(tableName: string): AssetTypeData | undefined {
    const assetTypes = this.getCachedAssetTypes()
    return assetTypes?.find((at: AssetTypeData) => at.table_name === tableName)
  }
}
