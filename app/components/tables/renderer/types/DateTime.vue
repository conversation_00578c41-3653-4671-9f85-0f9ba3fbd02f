<script setup lang="ts">
import { DateFormat } from '~/utils/dates/date.format'

const props = defineProps({
  utcDateTime: {
    type: String,
    required: true,
  },
  dateOnly: {
    type: Boolean,
    default: false,
  },
})
const dayjs = useDayjs()

const formatted = computed(() => {
  if (!props.utcDateTime) {
    return 'n/a'
  }

  return dayjs(props.utcDateTime).tz().format(props.dateOnly ? DateFormat.DD_MM_YYYY : DateFormat.DD_MM_YYYY_HH_MM)
})
</script>

<template>
  <time>{{ formatted }}</time>
</template>
