import type QueryData from '~/utils/query/QueryData'
import { createQueryKeys } from '@lukemorales/query-key-factory'
import { AssetService } from '../services/asset.service'

export function createAssetKeysFactory(table: string) {
  return createQueryKeys(table, {
    all: (queryData?: QueryData) => ({
      queryKey: [queryData ? queryData.toQueryString() : ''],
      queryFn: () => AssetService.getAll(table, queryData),
    }),
    byId: (id: string, query?: object) => ({
      queryKey: [id, query],
      queryFn: () => AssetService.getById(table, id, query),
    }),
    tableData: (queryData?: QueryData) => ({
      queryKey: [queryData ? queryData.toQueryString() : ''],
      queryFn: () => AssetService.getTableData(table, queryData),
    }),
    search: (queryData?: QueryData) => ({
      queryKey: [queryData ? queryData.toQueryString() : ''],
      queryFn: () => AssetService.search(table, queryData),
    }),
  })
}
