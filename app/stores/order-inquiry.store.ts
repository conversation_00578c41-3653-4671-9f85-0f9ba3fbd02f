import type QueryData from '~/utils/query/QueryData'
import type { OrderInquiryData } from '~/utils/types/api/api'
import type { ApiResponse, PaginatedApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useOrderInquiryStore = defineStore('order-inquiry', () => {
  async function fetchById(
    orderInquiryId: string,
  ) {
    return await useApiClient()<ApiResponse<OrderInquiryData>>(
      `api/orders/order-inquiries/${orderInquiryId}`,
    )
  }

  async function fetchAll(queryData: QueryData) {
    queryData.setSort([{ id: 'updated_at', desc: true }])
    queryData.setIncludes([])

    return await useApiClient()<PaginatedApiResponse<OrderInquiryData[]>>(
      `api/orders/order-inquiries?${queryData.toQueryString()}`,
    )
  }

  async function retry(
    orderInquiryId: string,
  ) {
    return await useApiClient()<ApiResponse<OrderInquiryData>>(
      `api/orders/order-inquiries/${orderInquiryId}/retry`,
      {
        method: 'POST',
      },
    )
  }

  async function reject(
    orderInquiryIds: string[],
  ) {
    return await useApiClient()<ApiResponse<void>>(
      `api/orders/order-inquiries/reject`,
      {
        method: 'POST',
        body: JSON.stringify({
          orderInquiryIds,
        }),
      },
    )
  }

  async function process(
    orderInquiryIds: string[],
  ) {
    return await useApiClient()<ApiResponse<void>>(
      `api/orders/order-inquiries/process`,
      {
        method: 'POST',
        body: JSON.stringify({
          orderInquiryIds,
        }),
      },
    )
  }

  return {
    fetchById,
    fetchAll,
    retry,
    reject,
    process,
  }
})
