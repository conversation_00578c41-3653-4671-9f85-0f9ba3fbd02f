<script setup lang="ts">
import { useTenantStore } from '~/stores/tenant.store'
import VueQueryDevToolsWrapper from './components/devtools/VueQueryDevToolsWrapper.vue'
import { useIntercom } from './composables/intercom/useIntercom'

const tenantStore = useTenantStore()
const name = computed<string>(() => {
  return tenantStore.tenant?.name ?? ''
})

const titlePrefix = computed(() => {
  const tenantName = name.value
  if (tenantName.length === 0) {
    return 'Hyperfox'
  }
  return `Hyperfox - ${tenantName}`
})

useHead({
  titleTemplate: (titleChunk) => {
    const prefix = titlePrefix.value
    const suffix = titleChunk ?? 'Order automation platform'
    return `${prefix} - ${suffix}`
  },
})

const { setupIntercomWithUserData } = useIntercom()

onMounted(async () => {
  await setupIntercomWithUserData()
})
</script>

<template>
  <NuxtLoadingIndicator color="#ff2600" />
  <UApp>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <VueQueryDevToolsWrapper />
  </UApp>
</template>
