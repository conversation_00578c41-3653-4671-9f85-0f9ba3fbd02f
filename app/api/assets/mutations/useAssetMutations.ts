import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { createAssetKeysFactory } from '../keys/asset.keys'
import { AssetService } from '../services/asset.service'

export function createAssetMutationsFactory(table: string) {
  const assetKeys = createAssetKeysFactory(table)

  const useCreateAssetMutation = () => {
    return useMutation({
      mutationFn: (data: object) => AssetService.create(table, data),
      onSuccess: () => {
        queryClient.invalidateQueries({
          predicate: query => (query.queryKey[0] === table),
        })
      },
    })
  }

  const useUpdateAssetMutation = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string, data: object }) =>
        AssetService.update(table, id, data),
      onSuccess: (_, { id }) => {
        queryClient.invalidateQueries({
          predicate: query => (query.queryKey[0] === table),
        })
        queryClient.invalidateQueries({
          queryKey: assetKeys.byId(id).queryKey.slice(0, 3),
        })
      },
    })
  }

  const useDeleteAssetMutation = () => {
    return useMutation({
      mutationFn: (id: string) => AssetService.delete(table, id),
      onSuccess: (_, id) => {
        queryClient.invalidateQueries({
          predicate: (query) => {
            const [tableKey, assetsKey, typeKey] = query.queryKey
            return tableKey === table && assetsKey === 'assets'
              && (typeKey === 'tableData' || typeKey === 'all')
          },
        })
        queryClient.removeQueries({
          queryKey: assetKeys.byId(id).queryKey.slice(0, 3),
        })
      },
    })
  }

  const useAssetActionMutation = () => {
    return useMutation({
      mutationKey: ['assetAction', table],
      mutationFn: ({ id, action, data }: { id: string, action: string, data?: object }) =>
        AssetService.action(table, id, action, data),
      onSuccess: (_, { id }) => {
        queryClient.invalidateQueries({
          predicate: query => (query.queryKey[0] === table),
        })
        queryClient.invalidateQueries({
          queryKey: assetKeys.byId(id).queryKey.slice(0, 3),
        })
      },
    })
  }

  const useTruncateAssetMutation = () => {
    return useMutation({
      mutationFn: () => AssetService.truncate(table),
      onSuccess: (_) => {
        queryClient.invalidateQueries({
          predicate: query => (query.queryKey[0] === table),
        })
      },
    })
  }

  return {
    useCreateAssetMutation,
    useUpdateAssetMutation,
    useDeleteAssetMutation,
    useTruncateAssetMutation,
    useAssetActionMutation,
  }
}
