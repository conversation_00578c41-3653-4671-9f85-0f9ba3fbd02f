import type { CreateUserData, UpdateUserData } from '~/utils/types/api/api'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { userKeys } from '../keys/user.keys'
import { IdentityService } from '../services/identity.service'

export function useCreateUserMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateUserData) => IdentityService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const [tableKey, assetsKey] = query.queryKey
          return tableKey === 'user' && assetsKey === 'all'
        },
      })
    },
  })
}

export function useUpdateUserMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, data }: { userId: string, data: UpdateUserData }) =>
      IdentityService.update(userId, data),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const [tableKey, assetsKey] = query.queryKey
          return tableKey === 'user' && assetsKey === 'all'
        },
      })

      batchInvalidateKeys(
        queryClient,
        [
          userKeys.byId(userId),
          userKeys.me,
        ],
      )
    },
  })
}

export function useDeleteUserMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userId: string) => IdentityService.delete(userId),
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const [tableKey, assetsKey] = query.queryKey
          return tableKey === 'user' && assetsKey === 'all'
        },
      })

      batchInvalidateKeys(
        queryClient,
        [
          userKeys.byId(userId),
          userKeys.me,
        ],
      )
    },
  })
}

export function useLogout() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: () => IdentityService.logout(),
    onSuccess: () => {
      queryClient.removeQueries({
        queryKey: userKeys.me.queryKey,
      })
    },
  })
}
