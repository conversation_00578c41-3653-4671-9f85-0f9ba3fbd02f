# Order Portal

## Project Description

The goal of this application is to help businesses speed up and streamline their order management process.
It automates the flow by capturing, validating, and syncing incoming orders, reducing manual overhead and errors.

---
## Key Technologies

- **Nuxt 3**: A modern Vue.js framework offering strong defaults, scalability, and a smooth developer experience.
- **Nuxt UI (Pro)**: A flexible and accessible UI component library tailored for Nuxt.
- **Pinia**: Modular and type-safe state management library for Vue.
- **ESLint**: Code linting for maintaining code quality in Vue and TypeScript files.
- **TanStack Table (Vue)**: A lightweight wrapper for advanced table features using TanStack's core logic. (Bundled with Nuxt UI)
- **nuxt-auth-sanctum**: This module provides a simple way to use Laravel Sanctum with Nuxt by leveraging cookies-based authentication.

---
## Project Structure

The project follows the official [Nuxt 3 directory structure](https://nuxt.com/docs/guide/directory-structure), with forward compatibility for the upcoming Nuxt 4 release.
Each major feature and layout is modularized to support scalability and maintainability.

---

## Development and Build Instructions

Install required dependencies using:
```
npm install
```

Create a `.env` file with the following environment variables:
```
#Only needed for build purposes
NUXT_UI_PRO_LICENSE=
#Example {tenant}.api.hyperfox.local
DEV_HOST=
```

Run the project using the following command:
```
npm run dev
```
