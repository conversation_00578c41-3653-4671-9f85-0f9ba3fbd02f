<script setup lang="ts">
import type { IntegrationData } from '~/utils/types/api/api'
import { useIntegrationsStore } from '~/stores/integrations.store'

const integrationsStore = useIntegrationsStore()

onMounted(async () => {
  await integrationsStore.hydrateAvailable()
  await integrationsStore.hydrate()
})

const router = useRouter()

async function addIntegration(integration: string) {
  await router.push({
    path: '/admin/integrations/new',
    query: { provider: integration },
  })
}

async function configureIntegration(integration: IntegrationData) {
  await router.push({
    path: `/admin/integrations/${integration.id}`,
    query: { provider: integration.type },
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Integrations">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="mb-8">
        <h1 class="text-2xl">
          Integrations
        </h1>
        <p class="text-gray-500 mt-3">
          Supercharge your workflow and connect the tools you use every day.
        </p>
      </div>

      <IntegrationsList
        :integrations="integrationsStore.integrations"
        @configure-integration="configureIntegration"
      />

      <IntegrationsAvailable
        class="mt-8"
        :available-integrations="integrationsStore.availableIntegrations"
        @add-integration="addIntegration"
      />
    </template>
  </UDashboardPanel>
</template>
