import { createQueryKeys } from '@lukemorales/query-key-factory'
import { WebhookService } from '../services/webhook.service'

export const webhookKeys = createQueryKeys('webhook', {
  all: {
    queryKey: null,
    queryFn: () => WebhookService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => WebhookService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => WebhookService.getByIdOrFail(id),
  }),
})
