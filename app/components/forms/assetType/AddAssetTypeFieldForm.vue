<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { UpsertAssetTypeFieldData } from '~/utils/types/api/api'
import { useCreateAssetTypeFieldMutation } from '~/api/asset-type-fields'
import { AssetTypeFieldType } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const data = ref<UpsertAssetTypeFieldData>({
  label: '',
  type: AssetTypeFieldType.String,
  precision: null,
  nullable: true,
  locked: false,
  required: false,
  searchable: false,
  weight: 1,
  enum_values: [],
})

const createAssetTypeFieldMutation = useCreateAssetTypeFieldMutation()

async function onSubmit() {
  try {
    await createAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      data: data.value,
    })
    toast.add({ title: 'Master data field created', color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: 'Error creating master data field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <USlideover title="Add field" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Label" class="col-span-6">
            <UInput v-model="data.label" placeholder="Enter a label name" />
          </UFormField>
          <UFormField label="Type" class="col-span-6">
            <USelect v-model="data.type" :items="Object.values(AssetTypeFieldType)" class="w-full" />
          </UFormField>
          <UFormField label="Weight" class="col-span-6">
            <UInput v-model="data.weight" placeholder="1" type="number" />
          </UFormField>
          <div class="col-span-6" />
          <UFormField label="Nullable" class="col-span-3">
            <USwitch v-model="data.nullable" />
          </UFormField>
          <UFormField label="Locked" class="col-span-3">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField label="Required" class="col-span-3">
            <USwitch v-model="data.required" />
          </UFormField>
          <UFormField label="Searchable" class="col-span-3">
            <USwitch v-model="data.searchable" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="createAssetTypeFieldMutation.isPending.value"
          :disabled="createAssetTypeFieldMutation.isPending.value"
        >
          Create
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
