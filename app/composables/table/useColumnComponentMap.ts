import { defineAsyncComponent } from 'vue'
import { ColumnType } from '~/utils/types/table/ColumnType'

function resolve(component: string) {
  return defineAsyncComponent(() => import(`~/components/tables/table-cells/${component}.vue`))
}

export function useColumnComponentMap(): Record<ColumnType, { view: any, edit?: any }> {
  const Placeholder = resolve('TextCell')

  return {
    [ColumnType.Text]: {
      view: resolve('TextCell'),
      edit: resolve('EditableTextCell'),
    },
    [ColumnType.Numeric]: {
      view: resolve('NumericCell'),
      edit: resolve('EditableNumericCell'),
    },
    [ColumnType.Badge]: {
      view: resolve('BadgeCell'),
    },
    [ColumnType.Date]: {
      view: resolve('DateCell'),
      edit: resolve('EditableDateCell'),
    },
    [ColumnType.DateTime]: {
      view: resolve('DateTimeCell'),
      edit: resolve('EditableDateTimeCell'),
    },
    [ColumnType.Actions]: {
      view: resolve('ActionsCell'),
    },
    [ColumnType.Relationship]: {
      view: resolve('RelationshipCell'),
      edit: resolve('EditableRelationshipCell'),
    },
    [ColumnType.Error]: {
      view: resolve('ErrorCell'),
    },
    [ColumnType.Checkbox]: {
      view: Placeholder,
    },
    [ColumnType.Enum]: {
      view: Placeholder,
    },
    [ColumnType.Image]: {
      view: Placeholder,
    },
    [ColumnType.Copy]: {
      view: Placeholder,
    },
  }
}
