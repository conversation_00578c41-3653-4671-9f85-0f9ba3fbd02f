<script setup lang="ts">
defineProps<{
  row: Record<string, any>
  config?: {
    buttons?: {
      label: string
      onClick: (row: Record<string, any>) => void
    }[]
  }
}>()
</script>

<template>
  <div class="flex gap-2 justify-start">
    <ULink
      v-for="(button, index) in config?.buttons || []"
      :key="index"
      :active="true"
      class="cursor-pointer"
      @click="() => button.onClick(row)"
    >
      {{ button.label }}
    </ULink>
  </div>
</template>
