import type { AssetTypeFieldData, EditAssetTypeFieldData, UpsertAssetTypeFieldData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { ASSET_TYPE_FIELD_API_ROUTES } from './asset-type-field-service.routes'

export class AssetTypeFieldService {
  static async getAll(): Promise<AssetTypeFieldData[]> {
    const response = await useApiClient()<ApiResponse<AssetTypeFieldData[]>>(
      ASSET_TYPE_FIELD_API_ROUTES.ALL,
    )
    return response.data
  }

  static async create(
    assetTypeId: string,
    data: UpsertAssetTypeFieldData,
  ): Promise<AssetTypeFieldData> {
    const response = await useApiClient()<ApiResponse<AssetTypeFieldData>>(
      ASSET_TYPE_FIELD_API_ROUTES.BY_ASSET_TYPE_ID(assetTypeId),
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async update(
    assetTypeId: string,
    fieldId: string,
    data: EditAssetTypeFieldData,
  ): Promise<AssetTypeFieldData> {
    const response = await useApiClient()<ApiResponse<AssetTypeFieldData>>(
      ASSET_TYPE_FIELD_API_ROUTES.BY_ID(assetTypeId, fieldId),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async delete(assetTypeId: string, fieldId: string): Promise<void> {
    await useApiClient()<ApiResponse<boolean>>(
      ASSET_TYPE_FIELD_API_ROUTES.BY_ID(assetTypeId, fieldId),
      {
        method: 'DELETE',
      },
    )
  }
}
