<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeData, UpdateAssetTypeData } from '~/utils/types/api/api'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'

const props = defineProps({
  assetType: {
    type: Object as PropType<AssetTypeData>,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()

const isPending = ref(false)
const data = ref<UpdateAssetTypeData>({
  display_field_id: props.assetType.display_field_id || '',
  supporting_data: props.assetType.supporting_data || false,
  locked: props.assetType.locked || false,
})

async function onSubmit() {
  try {
    isPending.value = true
    await assetTypeStore.update(props.assetType.id, data.value)
    await assetTypeStore.hydrate()
    emit('close', false)
    toast.add({ title: 'Master data updated', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error updating master data field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}

const displayFieldOptions = computed(() =>
  (allAssetTypeFields.value || [])
    .filter(field => field.asset_type_id === props.assetType.id)
    .map(field => ({
      id: field.id,
      label: field.label,
      type: 'item' as const,
    })),
)
</script>

<template>
  <USlideover
    title="Edit master data"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Display field" class="col-span-12">
            <USelect
              v-model="data.display_field_id"
              :items="displayFieldOptions"
              label-key="label"
              value-key="id"
              class="w-full"
            />
          </UFormField>
          <UFormField label="Reference data" class="col-span-6">
            <USwitch v-model="data.supporting_data" />
          </UFormField>
          <UFormField label="Locked" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Update
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
