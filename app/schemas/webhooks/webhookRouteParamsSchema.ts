import { z } from 'zod'

export const webhookQuerySchema = z.object({
  step: z.string().optional(),
})

export const webhookRouteParamsSchema = z.object({
  id: z.union([
    z.literal('new'),
    z.string().uuid({ message: 'Invalid UUID for webhook ID' }),
  ]),
})

export type WebhookQueryParams = z.infer<typeof webhookQuerySchema>
export type WebhookRouteParams = z.infer<typeof webhookRouteParamsSchema>
