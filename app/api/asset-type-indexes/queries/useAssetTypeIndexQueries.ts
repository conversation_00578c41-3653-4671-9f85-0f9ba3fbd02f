import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeIndexKeys } from '../keys/asset-type-index.keys'

export function useAssetTypeIndexesQuery() {
  return useQuery({
    ...assetTypeIndexKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeIndexesByAssetTypeQuery(assetTypeId: string) {
  return useQuery({
    ...assetTypeIndexKeys.byAssetTypeId(assetTypeId),
    staleTime: CACHE_CONFIG.INFITE,
  })
}

export function useAssetTypeIndexesByAssetTypeFieldQuery(assetTypeFieldId: string) {
  return useQuery({
    ...assetTypeIndexKeys.byAssetTypeFieldId(assetTypeFieldId),
    staleTime: CACHE_CONFIG.INFITE,
  })
}
