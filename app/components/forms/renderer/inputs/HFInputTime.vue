<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { computed } from 'vue'
import { useFormFieldError } from '~/composables/form/useFormFieldError'

const props = defineProps<FormInputProps>()

const model = defineModel({ type: String })

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)
</script>

<template>
  <div class="flex flex-col gap-2">
    <UFormField :label="label" :error="error">
      <UInput
        v-model="model"
        type="time"
        :name="name"
        :disabled="isReadOnly"
        step="60"
      />
    </UFormField>
  </div>
</template>
