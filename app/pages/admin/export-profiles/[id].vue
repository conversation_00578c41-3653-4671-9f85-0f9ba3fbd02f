<script setup lang="ts">
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { ref } from 'vue'
import { ZodError } from 'zod'
import { useCreateExportProfileMutation, useExportProfileByIdOrFailQuery, useUpdateExportProfileGeneralInfoMutation, useUpdateExportProfilePathMutation, useUpdateExportProfileTemplateMutation } from '~/api/export-profiles'
import ExportProfileStepWrapper from '~/components/export-profiles/ExportProfileStepWrapper.vue'
import SteppedHeader from '~/components/general/SteppedHeader.vue'
import { useStepperWizard } from '~/composables/stepped-header/useStepperWizard'
import { exportProfileDataToFormSchema } from '~/schemas/export-profiles/export-profile-api-to-form.schema'
import { exportProfileQuerySchema, exportProfileRouteParamsSchema } from '~/schemas/export-profiles/export-profile-id-route.schema'
import { isExportProfileReady } from '~/utils/export-profiles/export-profile-status.checker'
import { EXPORT_PROFILE_STATUSES } from '~/utils/export-profiles/export-profile-statusses'
import { isValidExportProfileStep } from '~/utils/export-profiles/export-profile-step.checker'
import { EXPORT_PROFILE_STEPS } from '~/utils/export-profiles/export-profile-steps'

definePageMeta({ layout: 'empty' })

const profile = ref<ExportProfileFormData | null>(null)

const exportProfileId = ref<string | null>(null)
const isCreateMode = ref<boolean>(false)
const routeError = ref<string | null>(null)

const {
  currentStep,
  currentStepIndex,
  currentStepNumber,
  getHighestPossibleStep,
  highestReachedStepNumber,
  setHighestReachedStep,
  resetSteps,
  goToStep,
  goToNextStep,
  goToPreviousStep,
  totalSteps,
  isLastStep,
} = useStepperWizard(EXPORT_PROFILE_STEPS)
const hasInitializedStepper = ref(false)

const route = useRoute()
const router = useRouter()

try {
  const parsedParams = exportProfileRouteParamsSchema.parse(route.params)
  const parsedQuery = exportProfileQuerySchema.parse(route.query)

  isCreateMode.value = parsedParams.id === 'new'
  if (!isCreateMode.value) {
    exportProfileId.value = parsedParams.id
  }
  else {
    profile.value = {
      name: '',
      status: '',
      integration_id: '',
      path: '/',
      template: null,
      crlf: false,
    }

    hasInitializedStepper.value = true
  }

  if (isValidExportProfileStep(parsedQuery.step)) {
    goToStep(parsedQuery.step)
  }
}
catch (error) {
  if (error instanceof ZodError) {
    routeError.value = error.issues.map(issue => issue.message).join(', ')
  }
  else {
    routeError.value = 'Unexpected route error'
  }
}

const {
  data: fetchedProfile,
  isError: isProfileError,
  error: profileError,
} = useExportProfileByIdOrFailQuery(exportProfileId.value!, {
  enabled: !isCreateMode.value && !!exportProfileId.value,
})

watchEffect(() => {
  if (fetchedProfile.value) {
    profile.value = exportProfileDataToFormSchema.parse(fetchedProfile.value)

    if (hasInitializedStepper.value) {
      return
    }

    if (isExportProfileReady(fetchedProfile.value)) {
      setHighestReachedStep(getHighestPossibleStep())
    }
    else if (isValidExportProfileStep(fetchedProfile.value.status)) {
      setHighestReachedStep(fetchedProfile.value.status)
      hasInitializedStepper.value = true
    }
    else {
      resetSteps()
    }
  }
})

function handleCancel() {
  router.push('/admin/export-profiles')
}

const createExportPorfileMutation = useCreateExportProfileMutation()

const updateExportProfileGeneralInfoMutation = useUpdateExportProfileGeneralInfoMutation()
const updateExportProfilePathMutation = useUpdateExportProfilePathMutation()
const updateExportProfileTemplateMutation = useUpdateExportProfileTemplateMutation()

async function updateExportProfile(data: ExportProfileFormData) {
  switch (currentStep.value) {
    case EXPORT_PROFILE_STATUSES.BASIC_INFORMATION:
      return updateExportProfileGeneralInfoMutation.mutateAsync({
        exportProfileId: exportProfileId.value!,
        data,
      })
    case EXPORT_PROFILE_STATUSES.INCOMPLETE:
      return updateExportProfilePathMutation.mutateAsync({
        exportProfileId: exportProfileId.value!,
        data,
      })
    case EXPORT_PROFILE_STATUSES.TEMPLATING:
      return updateExportProfileTemplateMutation.mutateAsync({
        exportProfileId: exportProfileId.value!,
        data,
      })
    default:
      throw new Error('Invalid step for update')
  }
}

async function submitForm(data: ExportProfileFormData, isSaveAndBackToOverview: boolean = false) {
  try {
    if (isCreateMode.value) {
      const createdProfile = await createExportPorfileMutation.mutateAsync(data)

      if (isSaveAndBackToOverview) {
        useToast().add({
          title: 'Export profile created successfully',
          description: 'Your export profile has been created.',
          color: 'success',
        })
        return router.push('/admin/export-profiles')
      }

      return router.push({
        path: `/admin/export-profiles/${createdProfile.id}`,
        query: { step: createdProfile.status },
      })
    }
    else {
      if (!exportProfileId.value) {
        throw new Error('Export profile ID is required for update')
      }

      const updatedProfile = await updateExportProfile(data)

      profile.value = exportProfileDataToFormSchema.parse(updatedProfile)

      if (isValidExportProfileStep(updatedProfile.status)) {
        router.replace({
          path: route.path,
          query: {
            ...route.query,
            step: updatedProfile.status,
          },
        })
      }
    }

    if (isLastStep.value || isSaveAndBackToOverview) {
      useToast().add({
        title: 'Export profile saved successfully',
        description: 'Your export profile has been saved.',
        color: 'success',
      })

      return router.push('/admin/export-profiles')
    }

    goToNextStep()
  }
  catch (error: any) {
    const toast = useToast()
    const message
    = error?.data?.message
      || error?.message
      || 'An unexpected error occurred'

    toast.add({
      title: 'Error saving export profile',
      description: message,
      color: 'error',
    })
  }
}

function handlePreviousStep() {
  if (currentStepIndex.value > 0) {
    goToPreviousStep()
  }
  else {
    handleCancel()
  }
}
</script>

<template>
  <div class="min-h-screen bg-neutral-50 flex flex-col">
    <SteppedHeader
      title="Configure export profile"
      :show-stepper="true"
      :current-step="currentStepNumber"
      :highest-reached-step="highestReachedStepNumber"
      :amount-of-steps="totalSteps"
      @change-step="goToStep"
      @cancel="handleCancel"
    />

    <div v-if="routeError || isProfileError" class="p-8 text-red-500 text-center">
      {{ routeError || profileError?.message }}
      <div class="mt-4">
        <UButton color="error" variant="solid" @click="handleCancel">
          Back to Profiles
        </UButton>
      </div>
    </div>

    <template v-else-if="profile">
      <UContainer class="hidden md:block md:py-16 max-w-3xl">
        <UCard class="px-6 py-4 shadow-md">
          <ExportProfileStepWrapper
            :id="exportProfileId"
            :step="currentStep"
            :profile="profile"
            :is-create-mode="isCreateMode"
            @previous-step="handlePreviousStep"
            @submit="submitForm"
          />
        </UCard>
      </UContainer>

      <UCard class="md:hidden px-6 py-4 bg-white flex-1">
        <ExportProfileStepWrapper
          :id="exportProfileId"
          :step="currentStep"
          :profile="profile"
          :is-create-mode="isCreateMode"
          @previous-step="handlePreviousStep"
          @submit="submitForm"
        />
      </UCard>
    </template>
  </div>
</template>
