<script setup lang="ts">
import type { FormField } from '~/utils/forms/FormField'
import { FormComponent } from '~/utils/forms/FormComponent'

const props = defineProps({
  field: {
    type: Object as PropType<FormField>,
    required: true,
  },
  errors: {
    type: Object as PropType<Record<string, string[]>>,
    required: true,
  },
  isReadOnly: {
    type: Boolean,
    default: false,
  },
})
const model = defineModel({
  type: [Object, String, Number, Boolean] as PropType<Record<string, any> | string | number | boolean>,
})
const fieldErrors = computed(() => {
  return Object.fromEntries(
    Object.entries(props.errors)
      .filter(([key]) => key.startsWith(props.field.name)),
  )
})

const formComponent = computed(() => {
  return FormComponent.create(props.field.type)
})
</script>

<template>
  <component
    :is="formComponent.component"
    v-if="formComponent"
    v-model="model"
    :name="field.name"
    :label="field.label"
    :field="field"
    :errors="fieldErrors"
    :is-read-only="isReadOnly"
    :crud-operations="field.crud"
  />
</template>
