<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { computed } from 'vue'
import { useFormFieldError } from '~/composables/form/useFormFieldError'

const props = defineProps<FormInputProps>()

const model = defineModel<boolean>({
  default: false,
})

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)
</script>

<template>
  <div class="flex flex-col gap-2">
    <UFormField :error="error">
      <UCheckbox
        v-model="model"
        :name="name"
        :label="label"
        :disabled="isReadOnly"
      />
    </UFormField>
  </div>
</template>
