<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeRelationshipData } from '~/utils/types/api/api'
import { FormsAssetTypeEditAssetTypeRelationshipForm, ModalsConfirmModal } from '#components'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const columns: BaseColumn<AssetTypeRelationshipData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('label', 'Label'),
  new TextColumn('relationship_function_name', 'Name'),
  new TextColumn('relationship_method', 'Method'),
  new TextColumn('relationship_asset_type_id', 'Relationship Asset', false, (data) => {
    try {
      return assetTypeStore.getById(data.relationship_asset_type_id ?? '')?.label
    }
    catch {
      return 'No relationship'
    }
  }),
  new CheckboxColumn('include', 'Include'),
  new CheckboxColumn('nullable', 'Nullable'),
  new CheckboxColumn('locked', 'Locked'),
  new CheckboxColumn('cascade_delete', 'Cascade'),
  new CheckboxColumn('required', 'Required'),
  new ActionColumn('Actions', [
    { label: 'Edit', onClick: onEdit },
    { label: 'Delete', onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const editSlideover = overlay.create(FormsAssetTypeEditAssetTypeRelationshipForm)

function onEdit(assetTypeRelationship: AssetTypeRelationshipData) {
  editSlideover.open({
    assetTypeRelationship,
  })
}

async function onDelete(assetTypeRelationship: AssetTypeRelationshipData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeRelationshipStore.destroy(props.assetTypeId, assetTypeRelationship.id as string)
    toast.add({ title: 'Master data relationship deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data relationship',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeRelationshipStore.getByAssetTypeId(assetTypeId)"
  />
</template>
