<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { computed } from 'vue'
import { useAssetTypeFieldByIdOrFailQuery } from '~/api/asset-type-fields'
import { AssetQueryFactory } from '~/api/assets'
import { useFormFieldError } from '~/composables/form/useFormFieldError'

const props = defineProps<FormInputProps>()

const model = defineModel({
  type: String,
})

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const relationship = assetTypeRelationshipStore.getById(props.field.extra?.parentField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')
const { data: assetTypeField } = useAssetTypeFieldByIdOrFailQuery(
  assetType.display_field_id!,
  {
    enabled: !!assetType?.display_field_id,
  },
)

const displayField = computed(() => {
  return assetTypeField.value?.field ?? ''
})

const { useAssetsQuery } = AssetQueryFactory
  .getQueryInstance(assetType?.table_name)
  .getQueries()

const { data: assetData, isFetching: isFetchingAssets } = useAssetsQuery()
</script>

<template>
  <UFormField :label="label" :error="error">
    <USelect
      :key="field.id"
      v-model="model"
      class="w-full"
      :items="assetData?.data || []"
      :label-key="displayField"
      value-key="id"
      :loading="isFetchingAssets"
      :disabled="isReadOnly"
    />
  </UFormField>
</template>
