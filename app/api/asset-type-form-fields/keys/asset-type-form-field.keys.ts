import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory'
import { AssetTypeFormFieldCacheService } from '../services/asset-type-form-field-cache.service'
import { AssetTypeFormFieldService } from '../services/asset-type-form-field.service'

export const assetTypeFormFieldKeys = createQueryKeys('assetTypeFormFields', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeFormFieldService.getAll(),
  },
  byAssetTypeId: (assetTypeId: string) => ({
    queryKey: [assetTypeId],
    queryFn: () => AssetTypeFormFieldCacheService.getByAssetTypeId(assetTypeId),
  }),
})
