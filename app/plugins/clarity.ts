export default defineNuxtPlugin(() => {
  const hostname = window.location.hostname
  let clarityProjectId = ''

  if (hostname.includes('staging.testerfox.eu')) {
    clarityProjectId = 'r4h6uwskpu'
  }
  else if (hostname.includes('acceptance.testerfox.eu')) {
    clarityProjectId = 'r4h8he5o2a'
  }
  else if (hostname.includes('hyperfox.cloud')) {
    clarityProjectId = 'r4h90wdpid'
  }

  // Only add the Clarity script if we have a project ID
  if (clarityProjectId) {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.innerHTML = `
        (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "${clarityProjectId}");
      `
    document.head.appendChild(script)
  }
})
