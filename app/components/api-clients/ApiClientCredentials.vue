<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import type { ApiClientCredentialsFormData } from '~/schemas/api-client/api-client-form.schema'
import type { ApiClientCredentialsData, ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import { cloneDeep } from 'lodash-es'
import { apiClientCredentialsSchema } from '~/schemas/api-client/api-client-form.schema'

const props = defineProps<{
  apiClient: ApiClientFormData
  credentials: ApiClientCredentialsData | null
}>()

const emit = defineEmits<{
  submit: [data: ApiClientFormData]
  toggleSecretVisibility: []
  copyToClipboard: [text: string, label: string]
  revokeAndGenerate: []
  testConnection: []
}>()

const defaultCredentials: ApiClientCredentialsData = {
  key: '',
  secret: '',
  secretVisible: false,
  actualSecret: '',
}

const credentialsState = ref<ApiClientCredentialsFormData>({
  key: props.credentials?.key || '',
  secret: props.credentials?.secret || '',
  secretVisible: props.credentials?.secretVisible || false,
  actualSecret: props.credentials?.actualSecret || '',
})

watch(() => props.credentials, (newCredentials) => {
  if (newCredentials) {
    credentialsState.value = cloneDeep(newCredentials)
  }
  else {
    credentialsState.value = cloneDeep(defaultCredentials)
  }
}, { immediate: true })

const safeCredentials = computed(() => props.credentials || defaultCredentials)

function onSubmit(_event: FormSubmitEvent<ApiClientCredentialsFormData>) {
  emit('submit', props.apiClient)
}

function submitForm() {
  emit('submit', props.apiClient)
}
</script>

<template>
  <UForm
    :state="credentialsState"
    :schema="apiClientCredentialsSchema"
    class="space-y-6"
    @submit="onSubmit"
  >
    <h2 class="text-lg font-bold">
      API Credentials
    </h2>

    <UFormField label="Key" name="key">
      <div class="relative">
        <div
          class="w-full px-3 py-2 text-sm font-mono bg-gray-50 text-gray-900 border border-gray-300 rounded-md select-text"
          style="min-height: 2.5rem; display: flex; align-items: center;"
        >
          {{ safeCredentials.key }}
        </div>
        <UButton
          icon="i-heroicons-clipboard-document"
          variant="ghost"
          color="neutral"
          size="sm"
          class="absolute right-2 top-1/2 transform -translate-y-1/2"
          @click="$emit('copyToClipboard', safeCredentials.key, 'API Key')"
        />
      </div>
    </UFormField>

    <UFormField label="Secret" name="secret">
      <div class="relative">
        <div
          class="w-full px-3 py-2 text-sm font-mono bg-gray-50 text-gray-900 border border-gray-300 rounded-md select-text"
          style="min-height: 2.5rem; display: flex; align-items: center;"
        >
          {{ safeCredentials.secret }}
        </div>
        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
          <UButton
            :icon="safeCredentials.secretVisible ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'"
            variant="ghost"
            color="neutral"
            size="sm"
            :disabled="!safeCredentials.actualSecret"
            @click="$emit('toggleSecretVisibility')"
          />
          <UButton
            icon="i-heroicons-clipboard-document"
            variant="ghost"
            color="neutral"
            size="sm"
            :disabled="!safeCredentials.actualSecret"
            @click="$emit('copyToClipboard', safeCredentials.actualSecret, 'API Secret')"
          />
        </div>
      </div>

      <div v-if="!safeCredentials.actualSecret" class="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
        <div class="flex items-start">
          <UIcon name="i-heroicons-exclamation-triangle" class="h-5 w-5 text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div class="text-sm">
            <p class="text-amber-800 font-medium">
              Secret not available
            </p>
            <p class="text-amber-700 mt-1">
              The secret for this API client could not be retrieved. This is normal for existing clients as secrets are only shown once for security reasons.
            </p>
            <p class="text-amber-700 mt-1">
              <strong>To get a new secret:</strong> Click "Revoke & Generate" below to create new credentials.
            </p>
          </div>
        </div>
      </div>
    </UFormField>

    <div class="space-y-2">
      <p class="text-sm font-medium text-gray-700">
        Notes:
      </p>
      <ul class="text-sm text-gray-600 space-y-1">
        <li>• You can only display your secret once. Keep it safe.</li>
        <li>• Afterwards you have to revoke the ID and secret.</li>
      </ul>
    </div>
    <div class="space-y-4">
      <div class="flex items-center gap-4">
        <UButton
          size="lg"
          color="neutral"
          variant="outline"
          block
          @click="$emit('revokeAndGenerate')"
        >
          Revoke & Generate
        </UButton>
      </div>

      <UButton type="button" size="lg" color="neutral" block @click="submitForm">
        Done
      </UButton>
    </div>
  </UForm>
</template>
