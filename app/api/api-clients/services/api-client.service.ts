import type { ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import type { ApiClientData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { apiClientFormToApiSchema } from '~/schemas/api-client/api-client-api-transform.schema'
import { useApiClient } from '~/utils/api/api-client'
import { transformOrThrow } from '~/utils/zod/zod-transformer'
import { API_CLIENT_API_ROUTES } from './api-client-service.routes'

export class ApiClientService {
  public static async getAll(): Promise<ApiClientData[]> {
    const response = await useApiClient()<ApiResponse<ApiClientData[]>>(
      API_CLIENT_API_ROUTES.ALL,
    )

    return response.data
  }

  public static async getById(id: string): Promise<ApiClientData | null> {
    const response = await useApiClient()<ApiResponse<ApiClientData>>(
      API_CLIENT_API_ROUTES.BY_ID(id),
    )

    return response.data
  }

  public static async getByIdOrFail(id: string): Promise<ApiClientData> {
    const apiClient = await this.getById(id)

    if (!apiClient) {
      throw new Error(`API client with ID ${id} not found`)
    }

    return apiClient
  }

  public static async create(data: ApiClientFormData): Promise<ApiClientData> {
    const parsedData = transformOrThrow(apiClientFormToApiSchema, data)

    const response = await useApiClient()<ApiResponse<ApiClientData>>(
      API_CLIENT_API_ROUTES.ALL,
      {
        method: 'POST',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async update(
    id: string,
    data: ApiClientFormData,
  ): Promise<ApiClientData> {
    const parsedData = transformOrThrow(apiClientFormToApiSchema, data)

    const response = await useApiClient()<ApiResponse<ApiClientData>>(
      API_CLIENT_API_ROUTES.BY_ID(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async delete(id: string): Promise<void> {
    await useApiClient()(
      API_CLIENT_API_ROUTES.BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }

  public static async revokeAndGenerate(id: string): Promise<ApiClientData> {
    const response = await useApiClient()<ApiResponse<ApiClientData>>(
      API_CLIENT_API_ROUTES.REVOKE_BY_ID(id),
      {
        method: 'POST',
      },
    )

    return response.data
  }
}
