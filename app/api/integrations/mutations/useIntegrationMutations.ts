import type { CreateIntegrationData, UpdateIntegrationData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { exportProfileKeys } from '~/api/export-profiles'
import { importProfileKeys } from '~/api/import-profiles/keys/import-profile.keys'
import { queryClient } from '~/plugins/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { integrationKeys } from '../keys/integration.keys'
import { IntegrationService } from '../services/integration.service'

export function useCreateIntegrationMutation() {
  return useMutation({
    mutationFn: (data: CreateIntegrationData) => IntegrationService.create(data),
    onSuccess: () => {
      batchInvalidateKeys(queryClient, [
        integrationKeys.all,
      ])
    },
  })
}

export function useUpdateIntegrationMutation() {
  return useMutation({
    mutationFn: ({ integrationId, data }: { integrationId: string, data: UpdateIntegrationData }) =>
      IntegrationService.update(integrationId, data),
    onSuccess: (_, { integrationId }) => {
      batchInvalidateKeys(queryClient, [
        integrationKeys.all,
        integrationKeys.byId(integrationId),
        integrationKeys.byIdOrFail(integrationId),
      ])
    },
  })
}

export function useDeleteIntegrationMutation() {
  return useMutation({
    mutationFn: (integrationId: string) => IntegrationService.delete(integrationId),
    onSuccess: (_, integrationId) => {
      batchInvalidateKeys(queryClient, [
        integrationKeys.all,
        integrationKeys.byId(integrationId),
        integrationKeys.byIdOrFail(integrationId),

        importProfileKeys.all,
        exportProfileKeys.all,
      ])
    },
  })
}

export function useTestIntegrationMutation() {
  return useMutation({
    mutationFn: (data: CreateIntegrationData) => IntegrationService.test(data),
  })
}
