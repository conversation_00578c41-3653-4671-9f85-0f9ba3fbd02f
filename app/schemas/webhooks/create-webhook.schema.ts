import { z } from 'zod'
import { enumToTuple } from '~/utils/enum/enum-to-tuple.converter'
import { WebhookEvent } from '~/utils/types/api/api'

export const createWebhookUiSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  event: z.enum(enumToTuple(WebhookEvent)),
  target_url: z.string().url('Target URL must be a valid URL'),
  secret: z.string().optional().nullable(),
})

// una vez validado que los datos sean lo de esperar los convierte en datos y esos son los datos que se mandan al backend

export const createWebhookApiSchema = createWebhookUiSchema.transform(data => ({
  name: data.name,
  event: data.event,
  target_url: data.target_url,
  secret: data.secret,
}))

export type CreateWebhookApiData = z.infer<typeof createWebhookApiSchema>
