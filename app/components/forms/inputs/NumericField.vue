<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: string | number | null | undefined
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number | null | undefined): void
}>()

const hasFocus = ref(false)
const localValue = ref(props.modelValue)

watch(() => props.modelValue, (val) => {
  localValue.value = val
})

watch(localValue, (val) => {
  emit('update:modelValue', val)
})

function ignoreWheelEvent(e: WheelEvent) {
  if (hasFocus.value) {
    e.preventDefault()
  }
}
</script>

<template>
  <UInput
    v-model="localValue"
    type="number"
    class="w-full"
    :disabled="disabled"
    @wheel="ignoreWheelEvent"
    @focus="hasFocus = true"
    @blur="hasFocus = false"
  />
</template>
