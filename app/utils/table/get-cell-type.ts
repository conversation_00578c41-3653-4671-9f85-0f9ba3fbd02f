import type { AssetTypeTableFieldData } from '../types/api/api'
import { ColumnType } from '../types/table/ColumnType'

const columnTypeMap: Record<string, ColumnType> = {
  text: ColumnType.Text,
  numeric: ColumnType.Numeric,
  badge: ColumnType.Badge,
  checkbox: ColumnType.Checkbox,
  enum: ColumnType.Enum,
  image: ColumnType.Image,
  copy: ColumnType.Copy,
  relationship: ColumnType.Relationship,
  datetime: ColumnType.DateTime,
  date: ColumnType.Date,
}

export function getCellType(field: AssetTypeTableFieldData): ColumnType {
  if (field.asset_type_relationship_id) {
    return ColumnType.Relationship
  }

  return columnTypeMap[field.type] || ColumnType.Text
}
