<script setup lang="ts">
import type { WebhookFormData } from '~/utils/types/webhook/webhook-form.type'
import { computed, ref, watchEffect } from 'vue'
import { ZodError } from 'zod'
import { useCreateWebhookMutation, useTestWebhookMutation, useUpdateWebhookMutation, useWebhookByIdOrFailQuery } from '~/api/webhooks'
import SteppedHeader from '~/components/general/SteppedHeader.vue'
import WebhookConfigurationForm from '~/components/webhooks/forms/WebhookConfigurationForm.vue'
import { webhookApiDataToFormSchema } from '~/schemas/webhooks/webhook-api-to-form.schema'
import { webhookRouteParamsSchema } from '~/schemas/webhooks/webhookRouteParamsSchema'

definePageMeta({ layout: 'empty' })

const webhook = ref<WebhookFormData | null>(null)

const webhookId = ref<string | null>(null)
const isCreateMode = ref<boolean>(false)
const routeError = ref<string | null>(null)

const route = useRoute()
const router = useRouter()

try {
  const parsedParams = webhookRouteParamsSchema.parse(route.params)

  isCreateMode.value = parsedParams.id === 'new'
  if (!isCreateMode.value) {
    webhookId.value = parsedParams.id
  }
  else {
    webhook.value = {
      name: '',
      event: 'order.validated',
      target_url: '',
    }
  }
}
catch (error) {
  if (error instanceof ZodError) {
    routeError.value = error.issues.map(issue => issue.message).join(', ')
  }
  else {
    routeError.value = 'Unexpected route error'
  }
}

const {
  data: fetchedWebhook,
  isError: isProfileError,
  error: profileError,
} = useWebhookByIdOrFailQuery(webhookId.value!, {
  enabled: !isCreateMode.value && !!webhookId.value,
})

watchEffect(() => {
  if (fetchedWebhook.value) {
    webhook.value = webhookApiDataToFormSchema.parse(fetchedWebhook.value)
  }
})

// test webhook functionality
const testWebhookMutation = useTestWebhookMutation()
const testResult = computed(() => testWebhookMutation.data.value)

function testWebhook(data: WebhookFormData) {
  useToast().add({
    title: 'Testing Webhook',
    description: `Sending test payload to ${data.target_url}`,
    color: 'info',
  })
}

function handleCancel() {
  router.push('/admin/webhooks')
}

const createWebhookMutation = useCreateWebhookMutation()
const updateWebhookMutation = useUpdateWebhookMutation()

async function submitForm(data: WebhookFormData) {
  try {
    webhook.value = data

    if (isCreateMode.value) {
      await createWebhookMutation.mutateAsync(data)
      useToast().add({
        title: 'Webhook created successfully',
        description: `${data.name} has been configured and is ready to receive events.`,
        color: 'success',
      })

      return router.push('/admin/webhooks')
    }

    else {
      if (!webhookId.value) {
        throw new Error('Export profile ID is required for update')
      }

      await updateWebhookMutation.mutateAsync({
        webhookId: webhookId.value,
        data,
      })

      useToast().add({
        title: 'Webhook updated successfully',
        description: `${data.name} has been updated.`,
        color: 'success',
      })
      return router.push('/admin/webhooks')
    }
  }
  catch (error: any) {
    const toast = useToast()
    const message
        = error?.data?.message
          || error?.message
          || 'An unexpected error occurred'

    toast.add({
      title: 'Error saving export profile',
      description: message,
      color: 'error',
    })
  }
}

function handlePreviousStep() {
  handleCancel()
}
</script>

<template>
  <div class="min-h-screen bg-neutral-50 flex flex-col">
    <SteppedHeader
      title="Create webhook"
      :show-stepper="false"
      @cancel="handleCancel"
    />

    <div v-if="routeError || isProfileError" class="p-8 text-red-500 text-center">
      {{ routeError || profileError?.message }}
      <div class="mt-4">
        <UButton color="error" variant="solid" @click="handleCancel">
          Back to Profiles
        </UButton>
      </div>
    </div>

    <template v-else-if="webhook">
      <UContainer class="hidden md:block md:py-16 max-w-3xl">
        <UCard class="px-6 py-4 shadow-md">
          <WebhookConfigurationForm
            :id="webhookId"
            :webhook="webhook"
            :test-result="testResult"
            :test-mutation="testWebhookMutation"
            @previous-step="handlePreviousStep"
            @submit="submitForm"
            @test-webhook="testWebhook"
            @cancel-creation="handleCancel"
          />
        </UCard>
      </UContainer>

      <UCard class="md:hidden px-6 py-4 bg-white flex-1">
        <WebhookConfigurationForm
          :id="webhookId"
          :webhook="webhook"
          :test-result="testResult"
          :test-mutation="testWebhookMutation"
          @previous-step="handlePreviousStep"
          @submit="submitForm"
          @test-webhook="testWebhook"
          @cancel-creation="handleCancel"
        />
      </UCard>
    </template>
  </div>
</template>
