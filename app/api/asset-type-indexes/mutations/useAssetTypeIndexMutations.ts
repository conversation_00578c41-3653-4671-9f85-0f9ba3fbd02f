import type { UpsertAssetTypeIndexData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeIndexKeys } from '../keys/asset-type-index.keys'
import { AssetTypeIndexService } from '../services/asset-type-index.service'

export function useCreateAssetTypeIndexMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, data }: { assetTypeId: string, data: UpsertAssetTypeIndexData }) =>
      AssetTypeIndexService.create(assetTypeId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeIndexKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeIndexKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeIndexMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeIndexService.delete(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeIndexKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeIndexKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}
