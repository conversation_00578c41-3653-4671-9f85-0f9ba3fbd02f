import type { AssetTypeIndexData, UpsertAssetTypeIndexData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { ASSET_TYPE_INDEX_API_ROUTES } from './asset-type-index-service.routes'

export class AssetTypeIndexService {
  static async getAll(): Promise<AssetTypeIndexData[]> {
    const response = await useApiClient()<ApiResponse<AssetTypeIndexData[]>>(
      ASSET_TYPE_INDEX_API_ROUTES.ALL,
    )
    return response.data
  }

  static async create(
    assetTypeId: string,
    data: UpsertAssetTypeIndexData,
  ): Promise<AssetTypeIndexData> {
    const response = await useApiClient()<ApiResponse<AssetTypeIndexData>>(
      ASSET_TYPE_INDEX_API_ROUTES.BY_ASSET_TYPE_ID(assetTypeId),
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async delete(assetTypeId: string, id: string): Promise<void> {
    await useApiClient()<ApiResponse<boolean>>(
      ASSET_TYPE_INDEX_API_ROUTES.BY_ID(assetTypeId, id),
      {
        method: 'DELETE',
      },
    )
  }
}
