import type {
  MappingItemData,
  UpdateImportProfileConfigurationData,
  UpdateImportProfileGeneralInfoData,
  UpdateImportProfileMappingData,
  UpdateImportProfileScheduleData,
} from '../api/api'

type CleanMappingData = Omit<UpdateImportProfileMappingData, 'mapping'>
type CleanScheduleData = Omit<UpdateImportProfileScheduleData, 'schedule'>

export interface ImportProfileFormData
  extends Partial<UpdateImportProfileGeneralInfoData>,
  Partial<UpdateImportProfileConfigurationData>,
  Partial<CleanMappingData>,
  Partial<CleanScheduleData> {
  integration_id: string
  asset_type_id: string
  status: string

  mapping?: Array<MappingItemData> | null
  schedule?: string | null
}
