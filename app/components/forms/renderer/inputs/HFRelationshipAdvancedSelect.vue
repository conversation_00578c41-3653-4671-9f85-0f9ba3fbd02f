<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { refDebounced } from '@vueuse/shared'
import { computed } from 'vue'
import { useAssetTypeFieldByIdOrFailQuery } from '~/api/asset-type-fields'
import { AssetQueryFactory } from '~/api/assets'
import { useFormFieldError } from '~/composables/form/useFormFieldError'
import QueryData from '~/utils/query/QueryData'

const props = defineProps<FormInputProps>()

const model = defineModel({
  type: String,
})

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const items = ref<any[]>([])
const selectedItem = ref<any>()
const searchTerm = ref('')
const searchTermDebounced = refDebounced(searchTerm, 300)
const query: Ref<QueryData> = shallowRef(new QueryData())

const relationship = assetTypeRelationshipStore.getById(props.field.extra?.parentField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')
const { data: assetTypeField } = useAssetTypeFieldByIdOrFailQuery(
  assetType.display_field_id!,
  {
    enabled: !!assetType?.display_field_id,
  },
)

const displayField = computed(() => {
  return assetTypeField.value?.field ?? ''
})
const assetQuery = AssetQueryFactory.getQueryInstance(assetType?.table_name)

function updateSelectedItem() {
  if (items.value && model.value) {
    selectedItem.value = items.value.find(i => i.id === model.value)
  }
  else {
    selectedItem.value = undefined
  }
}

const { useAssetSearchQuery, useAssetQuery } = assetQuery.getQueries()

const { data: individualAsset } = useAssetQuery(
  computed(() => model.value || ''),
  {
    enabled: computed(() => {
      return !!(model.value && !items.value?.find(i => i.id === model.value))
    }),
  },
)

const assetSearchQuery = useAssetSearchQuery(query)

watch(assetSearchQuery.data, (newValue) => {
  if (!newValue)
    return

  items.value = newValue.data || []
  updateSelectedItem()
}, { immediate: true })

watch(individualAsset, (newAsset) => {
  if (newAsset && !items.value.find(i => i.id === newAsset.id)) {
    items.value = [newAsset, ...items.value]
    updateSelectedItem()
  }
}, { immediate: true })

watch(model, () => {
  updateSelectedItem()
}, { immediate: true })

watch(searchTermDebounced, async () => {
  const newQuery = new QueryData()
  newQuery.setParams({ q: searchTermDebounced.value })
  query.value = newQuery
})
</script>

<template>
  <UFormField :label="label" :error="error">
    <USelectMenu
      :key="field.id"
      v-model="model"
      v-model:search-term="searchTerm"
      :loading="assetSearchQuery.isFetching.value"
      :items="items"
      value-key="id"
      :label-key="displayField"
      ignore-filter
      class="w-full"
      :disabled="isReadOnly"
    />
  </UFormField>
</template>
