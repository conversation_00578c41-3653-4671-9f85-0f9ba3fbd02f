<script setup lang="ts">
import type { SelectItem } from '@nuxt/ui'
import type { BadgeConfigData } from '~/utils/types/api/api'
import NumericField from '~/components/forms/inputs/NumericField.vue'
import { BadgeConfigMode } from '~/utils/types/api/api'

const model = defineModel<BadgeConfigData | null>()

const badgeItems = ref([
  { label: 'Primary', value: 'primary', chip: { color: 'primary' } },
  { label: 'Secondary', value: 'secondary', chip: { color: 'secondary' } },
  { label: 'Success', value: 'success', chip: { color: 'success' } },
  { label: 'Info', value: 'info', chip: { color: 'info' } },
  { label: 'Warning', value: 'warning', chip: { color: 'warning' } },
  { label: 'Error', value: 'error', chip: { color: 'error' } },
  { label: 'Neutral', value: 'neutral', chip: { color: 'neutral' } },
] satisfies SelectItem[])

const badgeMode = computed({
  get: () => model.value?.mode ?? BadgeConfigMode.Status,
  set: (value: BadgeConfigMode) => {
    if (model.value) {
      model.value.mode = value
    }
    resetBadgeValues()
  },
})

onMounted(() => {
  initializeBadgeConfig()
})

function addBadge() {
  switch (model.value?.mode) {
    case BadgeConfigMode.Status:
      (model.value.statuses ??= []).push({
        name: '',
        color: 'neutral',
      })
      break
    case BadgeConfigMode.Threshold:
      (model.value.thresholds ??= []).push({
        min: 0,
        color: 'neutral',
      })
      break
  }
}

function initializeBadgeConfig() {
  if (model.value) {
    return
  }
  model.value = {
    thresholds: [],
    mode: BadgeConfigMode.Status,
    statuses: [],
  }
}

function resetBadgeValues() {
  switch (model.value?.mode) {
    case BadgeConfigMode.Status:
      model.value.thresholds = []
      break
    case BadgeConfigMode.Threshold:
      model.value.statuses = []
      break
  }
}

function removeBadge(index: number) {
  if (!model.value)
    return
  switch (model.value?.mode) {
    case BadgeConfigMode.Status:
      model.value?.statuses?.splice(index, 1)
      break
    case BadgeConfigMode.Threshold:
      model.value?.thresholds?.splice(index, 1)
      break
  }
}
</script>

<template>
  <UFormField label="Badge config mode" class="w-full">
    <USelect v-model="badgeMode" :items="Object.values(BadgeConfigMode)" class="w-full" />
  </UFormField>
  <div class="flex justify-between">
    <span>Set badges</span>
    <UButton leading-icon="i-ph-plus" @click="addBadge" />
  </div>
  <div v-if="badgeMode === BadgeConfigMode.Status">
    <div v-for="(config, index) in model?.statuses" :key="index" class="grid grid-cols-12 gap-4">
      <UFormField label="Name" class="col-span-5">
        <UInput v-model="config.name" />
      </UFormField>
      <UFormField label="Color" class="col-span-5">
        <USelect v-model="config.color" :items="badgeItems" class="w-full" />
      </UFormField>
      <div class="col-span-2 flex items-end justify-end">
        <UButton color="error" leading-icon="i-ph-trash" @click="removeBadge(index)" />
      </div>
    </div>
  </div>
  <div v-else-if="badgeMode === BadgeConfigMode.Threshold">
    <div v-for="(config, index) in model?.thresholds" :key="index" class="grid grid-cols-12 gap-4">
      <UFormField label="Minimum" class="col-span-5">
        <NumericField v-model="config.min" />
      </UFormField>
      <UFormField label="Color" class="col-span-5">
        <USelect v-model="config.color" :items="badgeItems" class="w-full" />
      </UFormField>
      <div class="col-span-2 flex items-end justify-end">
        <UButton color="error" leading-icon="i-ph-trash" @click="removeBadge(index)" />
      </div>
    </div>
  </div>
</template>
