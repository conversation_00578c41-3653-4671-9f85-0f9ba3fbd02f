import type {
  CreateAssetTypeRelationshipData,
  UpdateAssetTypeRelationshipData,
} from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeRelationshipKeys } from '../keys/asset-type-relationship.keys'
import { AssetTypeRelationshipService } from '../services/asset-type-relationship.service'

export function useCreateAssetTypeRelationshipMutation() {
  return useMutation({
    mutationFn: ({
      assetTypeId,
      data,
    }: {
      assetTypeId: string
      data: CreateAssetTypeRelationshipData
    }) => AssetTypeRelationshipService.create(assetTypeId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useUpdateAssetTypeRelationshipMutation() {
  return useMutation({
    mutationFn: ({
      assetTypeId,
      id,
      data,
    }: {
      assetTypeId: string
      id: string
      data: UpdateAssetTypeRelationshipData
    }) => AssetTypeRelationshipService.update(assetTypeId, id, data),
    onSuccess: (_, { assetTypeId, id }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.byAssetTypeId(assetTypeId).queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.byId(id).queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.byIdOrFail(id).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeRelationshipMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeRelationshipService.delete(assetTypeId, id),
    onSuccess: (_, { assetTypeId, id }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeRelationshipKeys.byAssetTypeId(assetTypeId).queryKey,
      })
      queryClient.removeQueries({
        queryKey: assetTypeRelationshipKeys.byId(id).queryKey,
      })
      queryClient.removeQueries({
        queryKey: assetTypeRelationshipKeys.byIdOrFail(id).queryKey,
      })
    },
  })
}
