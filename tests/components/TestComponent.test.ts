import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import TestComponent from '~/components/TestComponent.vue'

describe('testComponent', () => {
  it('should mount and render correctly', () => {
    const wrapper = mount(TestComponent)

    expect(wrapper.html()).toContain('Hello Test')
    expect(wrapper.html()).toContain('This is a test component')
    expect(wrapper.find('.test-component').exists()).toBe(true)
  })
})
