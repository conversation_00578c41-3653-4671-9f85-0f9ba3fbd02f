<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeFieldData, AssetTypeRelationshipData, UpdateImportData } from '~/utils/types/api/api'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import {

  AssetTypeRelationshipMethod,

} from '~/utils/types/api/api'

const props = defineProps({
  importId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const importStore = useImportStore()
const assetTypeIndexStore = useAssetTypeIndexStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const isPending = ref(false)
const isInitialized = ref(false)
const assetTypeFields = ref<AssetTypeFieldData[]>()
const assetTypeRelationships = ref<AssetTypeRelationshipData[]>()
const filteredAssetTypeFields = ref<AssetTypeFieldData[]>()
const importData = ref<any>(null)
const data = ref<UpdateImportData>({
  mapping: {
    mappings: [],
  },
  ready_for_processing: false,
  unique_field_id: '',
})

function getFieldsByAssetTypeId(assetTypeId: string): AssetTypeFieldData[] {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}

async function initializeData() {
  if (!importData.value || !allAssetTypeFields.value || isInitialized.value) {
    return
  }

  try {
    assetTypeFields.value = getFieldsByAssetTypeId(importData.value.asset_type_id)
    assetTypeRelationships.value = assetTypeRelationshipStore.getByAssetTypeId(importData.value.asset_type_id)

    filteredAssetTypeFields.value = assetTypeFields.value.filter((field) => {
      return assetTypeIndexStore.getByAssetTypeFieldId(field.id).length > 0
    })

    isInitialized.value = true
  }
  catch (error) {
    console.error('Error initializing data:', error)
    toast.add({
      title: 'Error loading field data',
      description: 'Failed to load asset type fields',
      color: 'error',
    })
  }
}

onMounted(async () => {
  try {
    const response = await importStore.fetchById(props.importId)
    importData.value = response.data
    data.value.mapping = importData.value.mapping || data.value.mapping
    data.value.unique_field_id = importData.value.unique_field_id || data.value.unique_field_id

    await initializeData()
  }
  catch (error) {
    console.error('Error loading import data:', error)
    toast.add({
      title: 'Error loading import',
      description: 'Failed to load import data',
      color: 'error',
    })
  }
})

watch([allAssetTypeFields, importData], async () => {
  await initializeData()
}, { immediate: true })

async function onSubmit() {
  try {
    isPending.value = true
    await importStore.update(props.importId, data.value)
    emit('close', false)
    toast.add({ title: 'Import updated', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error updating import',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}

function clearMapping(mapping: any) {
  mapping.to = null
}

const selectAssetTypeFields = computed(() => {
  if (!assetTypeRelationships.value || !assetTypeFields.value) {
    return []
  }

  const mappedRelationshipFields = assetTypeRelationships.value
    .filter(
      relationship => relationship.relationship_method === AssetTypeRelationshipMethod.BelongsTo
        && relationship.relationship_asset_type_id,
    )
    .map(relationship => ({
      label: `${relationship.label} ID`,
      value: relationship.relationship_key,
    }))

  const mappedFields = assetTypeFields.value
    .map(field => ({
      label: field.label,
      value: field.field,
    }))

  return mappedFields.concat(mappedRelationshipFields)
})

const selectFilteredAssetTypeFields = computed(() =>
  (filteredAssetTypeFields.value || []).map(field => ({
    label: field.label,
    value: field.id,
  })),
)
</script>

<template>
  <USlideover title="Edit import" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between gap-4" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <template v-for="mapping in data.mapping.mappings" :key="mapping.from">
            <UFormField label="From" class="col-span-5">
              <span class="flex items-center">{{ mapping.from }}</span>
            </UFormField>
            <span class="col-span-2 flex items-center">
              <UIcon name="i-ph-arrow-right" />
            </span>
            <UFormField label="To" class="col-span-5">
              <USelect
                :model-value="mapping.to ?? undefined"
                :items="selectAssetTypeFields"
                placeholder="Select a field"
                class="w-full"
                @update:model-value="mapping.to = $event"
              >
                <template v-if="mapping.to" #trailing>
                  <UIcon
                    name="i-ph-x"
                    class="!pointer-events-auto"
                    @click="clearMapping(mapping)"
                  />
                </template>
              </USelect>
            </UFormField>
          </template>
          <UFormField label="Unique identifier" class="col-span-12">
            <USelect
              v-model="data.unique_field_id"
              :items="selectFilteredAssetTypeFields"
              placeholder="Select a field"
            />
          </UFormField>
          <UFormField label="Ready for processing" class="col-span-12">
            <USwitch v-model="data.ready_for_processing" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Update
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
