import type { AssetTypeData, CreateAssetTypeData, UpdateAssetTypeData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { ASSET_TYPE_API_ROUTES } from './asset-type-service.routes'

export class AssetTypeService {
  static async getAll(): Promise<AssetTypeData[]> {
    const response = await useApiClient()<ApiResponse<AssetTypeData[]>>(
      ASSET_TYPE_API_ROUTES.ALL,
    )
    return response.data
  }

  static async create(data: CreateAssetTypeData): Promise<AssetTypeData> {
    const response = await useApiClient()<ApiResponse<AssetTypeData>>(
      ASSET_TYPE_API_ROUTES.ALL,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async update(id: string, data: UpdateAssetTypeData): Promise<AssetTypeData> {
    const response = await useApiClient()<ApiResponse<AssetTypeData>>(
      ASSET_TYPE_API_ROUTES.BY_ID(id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async delete(id: string): Promise<void> {
    await useApiClient()<ApiResponse<void>>(
      ASSET_TYPE_API_ROUTES.BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }

  static async emptyEntry(id: string): Promise<any> {
    const response = await useApiClient()<ApiResponse<any>>(
      ASSET_TYPE_API_ROUTES.EMPTY_ENTRY(id),
    )
    return response.data
  }
}
