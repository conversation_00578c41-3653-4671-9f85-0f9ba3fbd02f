<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { UserData } from '~/utils/types/api/api'
import { FormsUsersAddUserForm, FormsUsersEditUserForm, ModalsConfirmModal } from '#components'
import { useDeleteUserMutation } from '~/api/user/mutations/useUserMutations'
import { useUsersQuery } from '~/api/user/queries/useUserQueries'
import QueryData from '~/utils/query/QueryData'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { BadgeColumn } from '~/utils/table/BadgeColumn'
import { TextColumn } from '~/utils/table/TextColumn'
import { BadgeConfigMode } from '~/utils/types/api/api'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const overlay = useOverlay()
const toast = useToast()

const badgeConfig = {
  thresholds: [],
  mode: BadgeConfigMode.Status,
  statuses: [
    { name: 'Active', color: 'info' },
  ],
}
const columns: BaseColumn<UserData>[] = [
  new TextColumn<UserData>('email', 'Email'),
  new TextColumn<UserData>('first_name', 'First name'),
  new TextColumn<UserData>('last_name', 'Last name'),
  new BadgeColumn<UserData>('status', 'Status', badgeConfig),
  new ActionColumn<UserData>('Actions', [
    { label: 'Edit', onClick: row => onEdit(row) },
    { label: 'Delete', onClick: row => onDelete(row.id) },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

const queryData: Ref<QueryData> = shallowRef(new QueryData())

const pageNumber = ref(1)

const { data: usersData, isFetching: isLoadingUsers } = useUsersQuery(queryData)

watch(pageNumber, async (newPage) => {
  const newQueryData = new QueryData()

  newQueryData.setPage(newPage)

  queryData.value = newQueryData
}, { immediate: false })

// Modals
const addModal = overlay.create(FormsUsersAddUserForm)
const editModal = overlay.create(FormsUsersEditUserForm)
const confirmModal = overlay.create(ModalsConfirmModal)

async function onAdd() {
  await addModal.open()
}

async function onEdit(user: UserData) {
  await editModal.open({
    user,
  })
}

const deleteUserMutation = useDeleteUserMutation()

async function onDelete(id: string) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await deleteUserMutation.mutateAsync(id)
    toast.add({ title: 'User deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Could not delete user',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Users">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            label="Add user"
            leading-icon="i-ph-plus"
            @click="onAdd"
          />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="!isLoadingUsers && usersData" #body>
      <UTable :columns="columnConfig" :data="usersData?.data" />
      <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
        <div class="text-sm text-(--ui-text-muted)">
          <GeneralEntries :meta-data="usersData?.meta" />
        </div>
        <div class="flex items-center gap-1.5">
          <UPagination
            v-model:page="pageNumber"
            :page-count="usersData.meta.last_page"
            :items-per-page="usersData.meta.per_page"
            :total="usersData.meta.total"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
