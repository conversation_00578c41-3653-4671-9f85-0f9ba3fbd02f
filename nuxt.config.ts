export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',

  devtools: {
    enabled: true,

    timeline: {
      enabled: true,
    },
  },

  colorMode: {
    preference: 'light',
  },

  future: {
    compatibilityVersion: 4,
  },

  plugins: [
    { src: '~/plugins/base64.ts' },
    { src: '~/plugins/api-base-url.ts' },
    { src: '~/plugins/vue-query.ts' },
  ],

  ssr: false,

  modules: [
    '@pinia/nuxt',
    '@nuxt/ui-pro',
    '@nuxtjs/i18n',
    'nuxt-lodash',
    '@nuxt/image',
    '@nuxt/eslint',
    'dayjs-nuxt',
    '@nuxt/scripts',
  ],

  css: [
    '@/assets/css/main.css',
  ],

  i18n: {
    locales: [
      { code: 'en', file: 'en.json' },
      { code: 'nl', file: 'nl.json' },
    ],
    lazy: true,
    defaultLocale: 'en',
  },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      htmlAttrs: {
        lang: 'en',
      },
      link: [
        { rel: 'icon', type: 'image/png', href: '/favicon-96x96.png', sizes: '96x96' },
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      ],
    },
  },

  devServer: {
    host: import.meta.env.DEV_HOST || 'localhost',
    https: {
      key: './certs/localhost-key.pem',
      cert: './certs/localhost-cert.pem',
    },
  },

  dayjs: {
    plugins: ['timezone'],
    defaultTimezone: 'Europe/Brussels',
  },

  runtimeConfig: {
    public: {
      nodeEnv: '',
    },
  },
})
