<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import type { WebhookConfigurationFormData } from '~/schemas/webhooks/webhook-form-schema'
import type { StatusMessage } from '~/utils/types/api/api'
import { cloneDeep } from 'lodash-es'
import { webhookConfigurationSchema } from '~/schemas/webhooks/webhook-form-schema'

const props = defineProps<{
  webhook: WebhookConfigurationFormData
  testResult?: StatusMessage | null
  testMutation?: any
}>()

const emit = defineEmits<{
  submit: [data: WebhookConfigurationFormData]
  testWebhook: [data: WebhookConfigurationFormData]
  cancelCreation: []
}>()

const state = ref<WebhookConfigurationFormData>(cloneDeep(props.webhook))

if (!state.value.event) {
  state.value.event = 'order.validated'
}

watch(() => props.webhook, (newWebhook) => {
  const clonedWebhook = cloneDeep(newWebhook)
  if (!clonedWebhook.event) {
    clonedWebhook.event = 'order.validated'
  }
  state.value = clonedWebhook
}, { immediate: true })

const eventOptions = [
  { label: 'Validated Orders', value: 'order.validated' },
]
const examplePayload = computed(() => {
  const payloads: Record<string, object> = {
    validated_orders: {
      validated_order: {
        id: 'aaa-bbb',
      },
    },
  }

  return payloads[state.value.event] || payloads.validated_orders
})

function copyPayload() {
  navigator.clipboard.writeText(JSON.stringify(examplePayload.value, null, 2))
  useToast().add({
    title: 'Copied!',
    description: 'Example payload copied to clipboard',
    color: 'success',
  })
}

function onSubmit(event: FormSubmitEvent<WebhookConfigurationFormData>) {
  emit('submit', event.data)
}

function cancelCreation() {
  emit('cancelCreation')
}
</script>

<template>
  <UForm
    :state="state"
    :schema="webhookConfigurationSchema"
    class="space-y-6"
    @submit="onSubmit"
  >
    <UFormField label="Webhook Name" name="name" required>
      <UInput v-model="state.name" placeholder="Enter webhook name" size="lg" />
    </UFormField>

    <UFormField label="Event" name="event" required>
      <USelect
        v-model="state.event"
        :options="eventOptions"
        placeholder="Validated Orders"
        value-attribute="value"
        option-attribute="label"
        size="lg"
        class="w-full"
        :disabled="true"
      />
    </UFormField>

    <UFormField label="Target URL" name="target_url" required>
      <UInput
        v-model="state.target_url"
        placeholder="https://your-domain.com/webhook/endpoint"
        size="lg"
        type="url"
      />
    </UFormField>

    <div>
      <div class="flex items-center justify-between mb-2">
        <label class="block text-sm font-medium text-gray-700">
          Example Payload
        </label>
        <UButton
          icon="i-heroicons-clipboard-document"
          variant="ghost"
          color="neutral"
          size="sm"
          @click="copyPayload"
        />
      </div>
      <div class="relative">
        <pre class="bg-gray-700 text-white p-4 rounded-lg text-sm font-mono overflow-auto max-h-64 leading-relaxed">{{ JSON.stringify(examplePayload, null, 2) }}</pre>
      </div>
    </div>

    <div class="space-y-4">
      <UButton type="submit" size="lg" color="neutral" block>
        <p>Save</p>
      </UButton>

      <div class="w-full flex justify-center">
        <UButton type="button" size="lg" color="neutral" variant="link" @click="cancelCreation">
          <p class="underline">
            Cancel
          </p>
        </UButton>
      </div>
    </div>
  </UForm>
</template>
