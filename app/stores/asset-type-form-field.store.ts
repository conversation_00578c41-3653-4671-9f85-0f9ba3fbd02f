import type { AssetTypeFormFieldData, UpsertAssetTypeFormFieldData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useAssetTypeFormFieldStore = defineStore('asset-type-form-field', () => {
  const assetTypeFormFields = ref<AssetTypeFormFieldData[]>([])

  // Local state functions

  function getAll(): AssetTypeFormFieldData[] {
    return assetTypeFormFields.value
  }

  function getByAssetTypeId(assetTypeId: string): AssetTypeFormFieldData[] {
    return assetTypeFormFields.value.filter(formField => formField.asset_type_id === assetTypeId)
  }

  // API functions

  async function hydrate(
    assetTypeId?: string,
  ) {
    const url = assetTypeId
      ? `api/dynamic-assets/asset-types/${assetTypeId}/form-fields`
      : `api/dynamic-assets/asset-type-form-fields`

    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData[]>>(url)

    if (assetTypeId) {
      assetTypeFormFields.value = [
        ...assetTypeFormFields.value.filter(field => field.asset_type_id !== assetTypeId),
        ...response.data,
      ]
    }
    else {
      assetTypeFormFields.value = response.data
    }
  }

  async function create(
    assetTypeId: string,
    upsertAssetTypeFormFieldData: UpsertAssetTypeFormFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields`,
      {
        method: 'POST',
        body: JSON.stringify(upsertAssetTypeFormFieldData),
      },
    )

    assetTypeFormFields.value = [...assetTypeFormFields.value, response.data]

    return response
  }

  async function update(
    assetTypeId: string,
    assetTypeFormFieldId: string,
    upsertAssetTypeFormFieldData: UpsertAssetTypeFormFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields/${assetTypeFormFieldId}`,
      {
        method: 'PUT',
        body: JSON.stringify(upsertAssetTypeFormFieldData),
      },
    )

    assetTypeFormFields.value = assetTypeFormFields.value.map(field =>
      field.id === assetTypeFormFieldId ? response.data : field,
    )

    return response
  }

  async function destroy(
    assetTypeId: string,
    assetTypeFormFieldId: string,
  ) {
    await useApiClient()<ApiResponse<boolean>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields/${assetTypeFormFieldId}`,
      {
        method: 'DELETE',
      },
    )

    assetTypeFormFields.value = assetTypeFormFields.value.filter(field => field.id !== assetTypeFormFieldId)
  }

  async function generate(
    assetTypeId: string,
  ) {
    await useApiClient()<ApiResponse<AssetTypeFormFieldData[]>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields/generate`,
    )
  }

  async function moveUp(
    assetTypeId: string,
    id: string,
  ) {
    await useApiClient()<ApiResponse<AssetTypeFormFieldData[]>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields/${id}/up`,
    )
  }

  async function moveDown(
    assetTypeId: string,
    id: string,
  ) {
    await useApiClient()<ApiResponse<AssetTypeFormFieldData[]>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/form-fields/${id}/down`,
    )
  }

  return {
    assetTypeFormFields,

    getAll,
    getByAssetTypeId,

    hydrate,
    create,
    update,
    destroy,

    generate,
    moveUp,
    moveDown,
  }
})
