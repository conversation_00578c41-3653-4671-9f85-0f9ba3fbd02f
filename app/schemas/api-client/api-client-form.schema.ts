import { z } from 'zod'

export const apiClientScopeSchema = z.object({
  tokenName: z.string().min(1, 'Token name is required').max(100, 'Token name must be less than 100 characters'),
  hasExpiration: z.boolean(),
  expiration_date: z.string().nullable(),
  permissions: z.object({
    readReferenceData: z.boolean(),
    writeReferenceData: z.boolean(),
    readOrderData: z.boolean(),
    writeOrderData: z.boolean(),
  }),
}).refine((data) => {
  const hasAnyPermission = data.permissions.readReferenceData
    || data.permissions.writeReferenceData
    || data.permissions.readOrderData
    || data.permissions.writeOrderData
  if (!hasAnyPermission) {
    return false
  }
  return true
}, {
  message: 'At least one permission must be selected',
  path: ['permissions'],
}).refine((data) => {
  // If expiration is enabled, date must be provided
  if (data.hasExpiration && !data.expiration_date) {
    return false
  }
  return true
}, {
  message: 'Expiration date is required when enabled',
  path: ['expiration_date'],
}).refine((data) => {
  if (data.hasExpiration && data.expiration_date) {
    const selectedDate = new Date(data.expiration_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return selectedDate >= today
  }
  return true
}, {
  message: 'Expiration date is required and must be today or in the future',
  path: ['expiration_date'],
})

export const apiClientCredentialsSchema = z.object({
  key: z.string(),
  secret: z.string(),
  secretVisible: z.boolean(),
  actualSecret: z.string(),
})

export type ApiClientScopeFormData = z.infer<typeof apiClientScopeSchema>
export type ApiClientCredentialsFormData = z.infer<typeof apiClientCredentialsSchema>
