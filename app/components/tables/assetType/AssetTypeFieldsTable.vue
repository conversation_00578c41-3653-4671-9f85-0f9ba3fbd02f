<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeFieldData } from '~/utils/types/api/api'
import {
  FormsAssetTypeEditAssetTypeFieldForm,
  ModalsConfirmModal,
} from '#components'
import { useAssetTypeFieldsQuery, useDeleteAssetTypeFieldMutation } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const deleteAssetTypeFieldMutation = useDeleteAssetTypeFieldMutation()

function getFieldsByAssetTypeId(assetTypeId: string) {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}

const columns: BaseColumn<AssetTypeFieldData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('label', 'Label'),
  new TextColumn('field', 'Field'),
  new TextColumn('type', 'Type'),
  new TextColumn('weight', 'Weight'),
  new CheckboxColumn('nullable', 'Nullable'),
  new CheckboxColumn('locked', 'Locked'),
  new CheckboxColumn('searchable', 'Searchable'),
  new CheckboxColumn('required', 'Required'),
  new ActionColumn('Actions', [
    { label: 'Edit', onClick: onEdit },
    { label: 'Delete', onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const editModal = overlay.create(FormsAssetTypeEditAssetTypeFieldForm)

function onEdit(assetTypeField: AssetTypeFieldData) {
  editModal.open({
    assetTypeField,
  })
}

async function onDelete(assetTypeField: AssetTypeFieldData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await deleteAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      fieldId: assetTypeField.id as string,
    })
    toast.add({ title: 'Master data field deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="getFieldsByAssetTypeId(assetTypeId)"
  />
</template>
