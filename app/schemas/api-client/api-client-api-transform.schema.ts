import { z } from 'zod'
import { ApiClientScope } from '~/utils/types/api/api'

export const apiClientFormToApiSchema = z.object({
  tokenName: z.string(),
  hasExpiration: z.boolean(),
  expiration_date: z.string().nullable(),
  permissions: z.object({
    readOrderData: z.boolean(),
    writeOrderData: z.boolean(),
    readReferenceData: z.boolean(),
    writeReferenceData: z.boolean(),
  }),
}).transform((data) => {
  const scopes: ApiClientScope[] = []

  if (data.permissions.readReferenceData) {
    scopes.push(ApiClientScope.READ_REFERENCE_DATA)
  }
  if (data.permissions.writeReferenceData) {
    scopes.push(ApiClientScope.WRITE_REFERENCE_DATA)
  }
  if (data.permissions.readOrderData) {
    scopes.push(ApiClientScope.READ_ORDER_DATA)
  }
  if (data.permissions.writeOrderData) {
    scopes.push(ApiClientScope.WRITE_ORDER_DATA)
  }

  if (scopes.length === 0) {
    throw new Error('At least one permission must be selected')
  }

  // Convert YYYY-MM-DD to format: 2025-12-21T23:59:59+00:00
  let expiration_date: string | null = null
  if (data.hasExpiration && data.expiration_date) {
    const date = new Date(data.expiration_date)
    date.setUTCHours(23, 59, 59, 0)
    const year = date.getUTCFullYear()
    const month = String(date.getUTCMonth() + 1).padStart(2, '0')
    const day = String(date.getUTCDate()).padStart(2, '0')
    const hours = String(date.getUTCHours()).padStart(2, '0')
    const minutes = String(date.getUTCMinutes()).padStart(2, '0')
    const seconds = String(date.getUTCSeconds()).padStart(2, '0')
    expiration_date = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}+00:00`
  }

  return {
    name: data.tokenName,
    expiration_date,
    scopes,
  }
})

export const apiClientToFormSchema = z.object({
  id: z.string(),
  name: z.string(),
  expiration_date: z.string().nullable(),
  scopes: z.array(z.nativeEnum(ApiClientScope)),
  key: z.string(),
  secret: z.string().nullable(),
  created_at: z.string().nullable().optional(),
  updated_at: z.string().nullable().optional(),
}).transform((data) => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  const defaultDate = tomorrow.toISOString().split('T')[0]

  let formexpiration_date = defaultDate
  if (data.expiration_date) {
    const date = new Date(data.expiration_date)
    formexpiration_date = date.toISOString().split('T')[0]
  }

  return {
    tokenName: data.name,
    hasExpiration: data.expiration_date !== null,
    expiration_date: data.expiration_date !== null ? formexpiration_date : null,
    permissions: {
      readReferenceData: data.scopes.includes(ApiClientScope.READ_REFERENCE_DATA),
      writeReferenceData: data.scopes.includes(ApiClientScope.WRITE_REFERENCE_DATA),
      readOrderData: data.scopes.includes(ApiClientScope.READ_ORDER_DATA),
      writeOrderData: data.scopes.includes(ApiClientScope.WRITE_ORDER_DATA),
    },
  }
})
