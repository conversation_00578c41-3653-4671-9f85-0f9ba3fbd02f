import type { ImportProfileData } from '../types/api/api'
import { IMPORT_PROFILE_STATUSES } from './import-profile-statusses'

export function isImportProfileReady(
  profile: ImportProfileData,
): boolean {
  const status = profile.status

  if (!status) {
    return false
  }

  return status === IMPORT_PROFILE_STATUSES.READY
}

export function isValidImportProfileStatus(
  status: string | IMPORT_PROFILE_STATUSES | undefined | null,
): boolean {
  if (!status) {
    return false
  }

  return Object.values(IMPORT_PROFILE_STATUSES).includes(status as IMPORT_PROFILE_STATUSES)
}
