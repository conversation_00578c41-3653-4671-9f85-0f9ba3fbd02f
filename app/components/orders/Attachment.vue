<script setup lang="ts">
import type { AttachmentData } from '~/utils/types/api/api'

const props = defineProps({
  attachment: {
    type: Object as PropType<AttachmentData>,
    required: true,
  },
})

const storageStore = useStorageStore()
const settingsStore = useSettingsStore()

const loading = ref(true)
const url = ref<string>()
const isPdf = computed(() => {
  if (props.attachment.mime_type === 'application/pdf') {
    return true
  }

  return props.attachment.mime_type === 'application/octet-stream'
    && props.attachment.filename.toLowerCase().endsWith('.pdf')
})
const isOffice = computed(() => {
  const officeMimeTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
    'application/msword', // doc
    'application/vnd.ms-excel', // xls
    'text/csv', // csv
    'text/plain', // txt
  ]

  if (officeMimeTypes.includes(props.attachment.mime_type)) {
    return true
  }

  // Fallback for application/octet-stream
  if (props.attachment.mime_type === 'application/octet-stream') {
    const filename = props.attachment.filename.toLowerCase()
    const officeExtensions = ['.docx', '.xlsx', '.doc', '.xls', '.csv', '.txt']
    return officeExtensions.some(ext => filename.endsWith(ext))
  }

  return false
})
const shouldRenderOffice = computed(() => isOffice.value && settingsStore.settings?.renderOfficeDocuments)

onMounted(async () => {
  url.value = await storageStore.getAttachmentUrl(props.attachment.id)
  loading.value = false
})

async function downloadAttachment() {
  if (url.value) {
    try {
      const response = await fetch(url.value)
      const blob = await response.blob()
      const objectUrl = URL.createObjectURL(blob)
      const a = document.createElement('a')

      a.href = objectUrl
      a.download = props.attachment.filename
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()

      setTimeout(() => {
        document.body.removeChild(a)
        URL.revokeObjectURL(objectUrl)
      }, 100)
    }
    catch (error) {
      console.error('Error downloading file:', error)
    }
  }
}
</script>

<template>
  <div v-if="loading" class="flex justify-center items-center h-full">
    <UIcon name="i-ph-arrow-clockwise" class="animate-spin h-8 w-8" />
  </div>
  <template v-else>
    <embed
      v-if="isPdf"
      :src="url"
      type="application/pdf"
      frameBorder="0"
      scrolling="auto"
      height="100%"
      width="100%"
    >
    <iframe
      v-else-if="shouldRenderOffice"
      :src="`https://view.officeapps.live.com/op/embed.aspx?src=${url}`"
      height="100%"
      width="100%"
    />
    <div v-else class="flex flex-col justify-center items-center h-full">
      <p class="mb-4">
        {{ attachment.filename }}
      </p>
      <UButton
        icon="i-ph-download-simple"
        color="primary"
        @click="downloadAttachment"
      >
        Download File
      </UButton>
    </div>
  </template>
</template>
