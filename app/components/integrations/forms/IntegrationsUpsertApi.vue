<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import type { ApiIntegrationForm } from '~/schemas/integrations/api-integration-form.schema'
import type { ApiConfiguration } from '~/utils/types/api/api'
import type { BaseIntegrationFormProps } from '~/utils/types/integrations'
import { apiIntegrationFormSchema } from '~/schemas/integrations/api-integration-form.schema'
import { WebserviceAuthType } from '~/utils/types/api/api'

interface IntegrationsUpsertApiProps extends BaseIntegrationFormProps {}

const props = defineProps<IntegrationsUpsertApiProps>()

const emit = defineEmits<{
  submit: [formData: ApiIntegrationForm]
}>()

const defaultConfig: ApiConfiguration = {
  webservice_url: '',
  webservice_auth_method: WebserviceAuthType.NONE,
  webservice_username: '',
  webservice_password: '',
  webservice_ntlm: false,
}

const state = ref<ApiIntegrationForm>({
  label: props.integration?.label || '',
  data: {
    ...defaultConfig,
    ...props.integration?.data,
  },
})

const authMethodOptions = [
  { label: 'None', value: WebserviceAuthType.NONE },
  { label: 'Basic Authentication', value: WebserviceAuthType.BASIC },
  { label: 'Bearer Token', value: WebserviceAuthType.BEARER },
]

watch(() => props.integration, (newValue) => {
  if (!newValue) {
    return
  }

  state.value = {
    label: newValue.label,
    data: {
      ...state.value.data,
      ...newValue.data,
    },
  }
})

function setWebserviceAuthData() {
  state.value.data.webservice_username = ''
  state.value.data.webservice_password = ''
}

function onSubmit(event: FormSubmitEvent<ApiIntegrationForm>) {
  const formData = {
    label: event.data.label,
    data: event.data.data,
  }
  emit('submit', formData)
}
</script>

<template>
  <UForm
    :schema="apiIntegrationFormSchema"
    :state="state"
    @submit="onSubmit"
  >
    <div class="space-y-4">
      <h2 class="text-lg font-bold">
        API
      </h2>

      <UFormField label="Label" name="label" required>
        <UInput v-model="state.label" placeholder="Enter a name for this integration" />
      </UFormField>

      <UFormField label="Webservice URL" name="data.webservice_url" required>
        <UInput
          v-model="state.data.webservice_url"
          placeholder="https://example.com/api"
        />
      </UFormField>

      <UFormField label="Auth method" name="data.webservice_auth_method" required>
        <USelect
          v-model="state.data.webservice_auth_method"
          :items="authMethodOptions"
          option-attribute="label"
          value-attribute="value"
          class="w-full"
          @change="setWebserviceAuthData"
        />
      </UFormField>

      <UFormField
        v-if="state.data.webservice_auth_method === WebserviceAuthType.BASIC"
        label="Username"
        name="data.webservice_username"
        required
      >
        <UInput
          v-model="state.data.webservice_username"
          placeholder="Username"
        />
      </UFormField>

      <UFormField
        v-if="state.data.webservice_auth_method === WebserviceAuthType.BASIC"
        label="Password"
        name="data.webservice_password"
      >
        <UInput
          v-model="state.data.webservice_password"
          type="password"
          placeholder="••••••••••••"
          autocomplete="new-password"
        />
      </UFormField>

      <USwitch
        v-if="state.data.webservice_auth_method === WebserviceAuthType.BASIC"
        v-model="state.data.webservice_ntlm"
        label="NTLM authentication"
        name="data.webservice_ntlm"
      />

      <UFormField
        v-if="state.data.webservice_auth_method === WebserviceAuthType.BEARER"
        label="Token"
        name="data.webservice_password"
        required
      >
        <UInput
          v-model="state.data.webservice_password"
          placeholder="Bearer token"
          autocomplete="new-password"
        />
      </UFormField>
    </div>

    <div class="flex flex-col gap-2 mt-8">
      <UButton
        label="Save"
        type="submit"
        color="neutral"
        size="lg"
        block
        :loading="props.isSubmitting"
        :disabled="props.isSubmitting"
      />
    </div>
  </UForm>
</template>
