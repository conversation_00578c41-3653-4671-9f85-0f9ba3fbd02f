import type { CreateAssetTypeData, UpdateAssetTypeData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeKeys } from '../keys/asset-type.keys'
import { AssetTypeService } from '../services/asset-type.service'

export function useCreateAssetTypeMutation() {
  return useMutation({
    mutationFn: (data: CreateAssetTypeData) => AssetTypeService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: assetTypeKeys.all.queryKey,
      })
    },
  })
}

export function useUpdateAssetTypeMutation() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateAssetTypeData }) =>
      AssetTypeService.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeKeys.byId(id).queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeKeys.byIdOrFail(id).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeMutation() {
  return useMutation({
    mutationFn: (id: string) => AssetTypeService.delete(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeKeys.all.queryKey,
      })
      queryClient.removeQueries({
        queryKey: assetTypeKeys.byId(id).queryKey,
      })
      queryClient.removeQueries({
        queryKey: assetTypeKeys.byIdOrFail(id).queryKey,
      })
    },
  })
}
