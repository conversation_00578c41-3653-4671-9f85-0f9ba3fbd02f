import { http, HttpResponse } from 'msw'
import { buildTokenResponse } from './token-response.builder'

const API_BASE_URL = 'https://test.app'

export const authServiceHandlers = [
  // POST /oauth/token - Token exchange
  http.post(`${API_BASE_URL}/oauth/token`, async ({ request }) => {
    const body = await request.text()
    const params = new URLSearchParams(body)

    const grantType = params.get('grant_type')
    const code = params.get('code')
    const verifier = params.get('code_verifier')
    const refreshToken = params.get('refresh_token')

    // Handle authorization code flow
    if (grantType === 'authorization_code') {
      if (!code || !verifier) {
        return HttpResponse.json(
          { error: 'invalid_request', error_description: 'Missing required parameters' },
          { status: 400 },
        )
      }

      if (code === 'invalid-auth-code') {
        return HttpResponse.json(
          { error: 'invalid_grant', error_description: 'Invalid authorization code' },
          { status: 400 },
        )
      }

      if (verifier !== 'test_verifier') {
        return HttpResponse.json(
          { error: 'invalid_grant', error_description: 'Invalid code verifier' },
          { status: 400 },
        )
      }

      return HttpResponse.json(buildTokenResponse())
    }

    // Handle refresh token flow
    if (grantType === 'refresh_token') {
      if (!refreshToken) {
        return HttpResponse.json(
          { error: 'invalid_request', error_description: 'Missing refresh token' },
          { status: 400 },
        )
      }

      if (refreshToken === 'invalid-refresh-token') {
        return HttpResponse.json(
          { error: 'invalid_grant', error_description: 'Invalid refresh token' },
          { status: 401 },
        )
      }

      return HttpResponse.json(buildTokenResponse({
        access_token: 'new-access-token-123456789',
        refresh_token: 'new-refresh-token-987654321',
      }))
    }

    return HttpResponse.json(
      { error: 'unsupported_grant_type', error_description: 'Grant type not supported' },
      { status: 400 },
    )
  }),

  // POST /auth/logout - Logout
  http.post(`${API_BASE_URL}/auth/logout`, () => {
    return HttpResponse.json({ message: 'Successfully logged out' }, { status: 200 })
  }),
] 