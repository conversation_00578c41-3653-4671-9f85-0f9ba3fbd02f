import type { UpsertAssetTypeTableFieldData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { assetTypeTableFieldKeys } from '../keys/asset-type-table-field.keys'
import { AssetTypeTableFieldService } from '../services/asset-type-table-field.service'

export function useCreateAssetTypeTableFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, data }: { assetTypeId: string, data: UpsertAssetTypeTableFieldData }) =>
      AssetTypeTableFieldService.create(assetTypeId, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useUpdateAssetTypeTableFieldMutation() {
  return useMutation({
    mutationFn: ({
      assetTypeId,
      id,
      data,
    }: {
      assetTypeId: string
      id: string
      data: UpsertAssetTypeTableFieldData
    }) => AssetTypeTableFieldService.update(assetTypeId, id, data),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useDeleteAssetTypeTableFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeTableFieldService.delete(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useMoveUpAssetTypeTableFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeTableFieldService.moveUp(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}

export function useMoveDownAssetTypeTableFieldMutation() {
  return useMutation({
    mutationFn: ({ assetTypeId, id }: { assetTypeId: string, id: string }) =>
      AssetTypeTableFieldService.moveDown(assetTypeId, id),
    onSuccess: (_, { assetTypeId }) => {
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.all.queryKey,
      })
      queryClient.invalidateQueries({
        queryKey: assetTypeTableFieldKeys.byAssetTypeId(assetTypeId).queryKey,
      })
    },
  })
}
