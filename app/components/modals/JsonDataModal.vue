<script setup lang="ts">
import { useClipboard } from '@vueuse/core'

interface Props {
  title?: string
  json: string
}

const props = defineProps<Props>()
const tooltipText = ref()
const open = ref(false)
const clipboard = useClipboard({
  legacy: true,
})

const formatted = computed(() => {
  try {
    return JSON.stringify(JSON.parse(props.json ?? '{}'), null, 2)
  }
  catch {
    return props.json
  }
})

async function copy() {
  await clipboard.copy(formatted.value)
  tooltipText.value = 'Copied!'
  open.value = true
  setTimeout(() => {
    tooltipText.value = undefined
  }, 3000)
}
</script>

<template>
  <USlideover :title="props.title" :overlay="false">
    <template #body>
      <UTextarea v-model:model-value="formatted" autoresize class="w-full" :readonly="true" />
    </template>
    <template #footer>
      <div class="flex gap-2 justify-end w-full">
        <UTooltip v-model:open="open" :text="tooltipText">
          <UButton variant="outline" @click="copy">
            Copy
          </UButton>
        </UTooltip>
      </div>
    </template>
  </USlideover>
</template>
