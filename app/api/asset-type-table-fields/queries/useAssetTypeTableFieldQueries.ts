import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { assetTypeTableFieldKeys } from '../keys/asset-type-table-field.keys'

export function useAssetTypeTableFieldsQuery() {
  return useQuery({
    ...assetTypeTableFieldKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useAssetTypeTableFieldsByAssetTypeQuery(assetTypeId: string) {
  return useQuery({
    ...assetTypeTableFieldKeys.byAssetTypeId(assetTypeId),
    staleTime: CACHE_CONFIG.INFITE,
  })
}
