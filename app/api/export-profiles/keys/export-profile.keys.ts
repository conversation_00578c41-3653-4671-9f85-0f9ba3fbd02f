import { createQueryKeys } from '@lukemorales/query-key-factory'
import { ExportProfileService } from '../services/export-profile.service'

export const exportProfileKeys = createQueryKeys('exportProfile', {
  all: {
    queryKey: null,
    queryFn: () => ExportProfileService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => ExportProfileService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => ExportProfileService.getByIdOrFail(id),
  }),
  directories: (id: string, path: string) => ({
    queryKey: [id, path],
    queryFn: () => ExportProfileService.directories(id, path),
  }),
})
