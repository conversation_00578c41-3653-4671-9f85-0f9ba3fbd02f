import type { DirectoryData, ExportProfileData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { exportProfileKeys } from '../keys/export-profile.keys'
import { ExportProfileService } from '../services/export-profile.service'

export function useExportProfilesQuery() {
  return useQuery({
    ...exportProfileKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useExportProfileByIdQuery(
  id: string,
  options?: ComposableQueryOptions<ExportProfileData, typeof exportProfileKeys.byId>,
) {
  return useQuery({
    ...exportProfileKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useExportProfileByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<ExportProfileData, typeof exportProfileKeys.byIdOrFail>,
) {
  return useQuery({
    ...exportProfileKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useExportProfileDirectoriesQuery(
  id: string,
  path: MaybeRefOrGetter<string>,
) {
  return useQuery<DirectoryData>({
    queryKey: ['exportProfile', 'byId', id, 'directories', path],
    queryFn: () => ExportProfileService.directories(id, toValue(path)),
    staleTime: CACHE_CONFIG.IMMEDIATE,
  })
}
