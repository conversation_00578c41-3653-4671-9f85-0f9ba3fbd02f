<script setup lang="ts">
import UnsupportedResolution from '~/components/general/UnsupportedResolution.vue'
import { useScreenRequirement } from '~/composables/utils/useScreenRequirement'

const open = ref(false)

const links = [[{
  id: 'master-data',
  label: 'Master data',
  icon: 'i-lucide-file',
  to: '/admin/master-data',
}, {
  id: 'channels',
  label: 'Channels',
  icon: 'i-lucide-send',
  to: '/admin/channels',
}, {
  id: 'integrations',
  label: 'Integrations',
  icon: 'i-lucide-link',
  to: '/admin/integrations',
}, {
  label: 'Webhooks',
  icon: 'i-lucide-webhook',
  to: '/admin/webhooks',
}, {
  label: 'API Clients',
  icon: 'i-lucide-code',
  to: '/admin/api-clients',
}, {
  id: 'import-profiles',
  label: 'Import Profiles',
  icon: 'i-lucide-file-input',
  to: '/admin/import-profiles',
}, {
  id: 'export-profiles',
  label: 'Export Profiles',
  icon: 'i-lucide-file-output',
  to: '/admin/export-profiles',
}, {
  id: 'users',
  label: 'Users',
  icon: 'i-lucide-users',
  to: '/admin/users',
}, {
  id: 'logs',
  label: 'System Logs',
  icon: 'i-lucide-clipboard',
  to: '/admin/logs',
}, {
  id: 'settings',
  label: 'Settings',
  icon: 'i-lucide-settings',
  to: '/admin/settings',
}], [
  {
    label: 'Back to portal',
    icon: 'i-ph-sign-out',
    to: '/',
  },
]]

const { showResolutionWarning } = useScreenRequirement()
</script>

<template>
  <UDashboardGroup>
    <UDashboardSidebar
      v-model:open="open"
      collapsible
      resizable
      class="bg-neutral-50"
    >
      <template #header="{ collapsed }">
        <div class="flex justify-center w-full my-4">
          <img v-if="!collapsed" src="~/assets/img/hyperfox.svg" alt="Hyperfox" class="w-36">
          <img v-if="collapsed" src="~/assets/img/hyperfox-logo.svg" alt="Hyperfox">
        </div>
      </template>

      <template #default="{ collapsed }">
        <UNavigationMenu
          :collapsed="collapsed"
          :items="links[0]"
          orientation="vertical"
        />

        <UNavigationMenu
          :collapsed="collapsed"
          :items="links[1]"
          orientation="vertical"
          class="mt-auto"
        />
      </template>
    </UDashboardSidebar>
    <slot />
    <UnsupportedResolution v-if="showResolutionWarning" />
  </UDashboardGroup>
</template>
