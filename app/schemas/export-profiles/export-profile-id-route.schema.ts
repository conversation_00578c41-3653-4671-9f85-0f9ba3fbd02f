import { z } from 'zod'
import { enumToTuple } from '~/utils/enum/enum-to-tuple.converter'
import { EXPORT_PROFILE_STATUSES } from '~/utils/export-profiles/export-profile-statusses'

export const exportProfileQuerySchema = z.object({
  step: z
    .enum(enumToTuple(EXPORT_PROFILE_STATUSES))
    .optional(),
})

export const exportProfileRouteParamsSchema = z.object({
  id: z.union([
    z.literal('new'),
    z.string().uuid({ message: 'Invalid UUID for export profile ID' }),
  ]),
})

export type ExportProfileQueryParams = z.infer<typeof exportProfileQuerySchema>
export type ExportProfileRouteParams = z.infer<typeof exportProfileRouteParamsSchema>
