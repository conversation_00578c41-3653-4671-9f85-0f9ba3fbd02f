<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { OrderInquiryData } from '~/utils/types/api/api'
import QueryData from '~/utils/query/QueryData'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { BadgeColumn } from '~/utils/table/BadgeColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { DateTimeColumn } from '~/utils/table/DateTimeColumn'
import { TextColumn } from '~/utils/table/TextColumn'
import { BadgeConfigMode } from '~/utils/types/api/api'

definePageMeta({
  middleware: ['auth'],
})

// Component setup
const toast = useToast()
const orderInquiryStore = useOrderInquiryStore()

const badgeConfig = {
  thresholds: [],
  mode: BadgeConfigMode.Status,
  statuses: [
    { name: 'Unprocessed', color: 'info' },
    { name: 'Processed', color: 'success' },
    { name: 'Error', color: 'error' },
  ],
}
const loading = ref(true)
const data = ref()
const queryData = new QueryData()
const columns: BaseColumn<OrderInquiryData>[] = [
  new CopyColumn<OrderInquiryData>('id', 'Id'),
  new BadgeColumn<OrderInquiryData>('state', 'Status', badgeConfig),
  new TextColumn<OrderInquiryData>('channel', 'Channel', false, row => row.channel?.label || '---'),
  new DateTimeColumn<OrderInquiryData>('created_at', 'Received at'),
  new ActionColumn<OrderInquiryData>('Actions', [
    {
      label: 'View',
      onClick: async (row: OrderInquiryData) => {
        await navigateTo(`/order-inquiries/${row.id}`)
      },
    },
    {
      label: 'Retry',
      onClick: async (row: OrderInquiryData) => {
        try {
          await orderInquiryStore.retry(row.id)
          data.value = await orderInquiryStore.fetchAll(queryData)
        }
        catch (e) {
          toast.add({
            title: 'Error retrying order inquiry',
            description: (e as FetchError)?.data?.message || 'An error occurred',
            color: 'error',
          })
        }
      },
    },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())
const pageNumber = ref(1)

watch(pageNumber, async (newPage) => {
  queryData.setPage(newPage)

  data.value = await orderInquiryStore.fetchAll(queryData)
}, { immediate: false })

onMounted(async () => {
  data.value = await orderInquiryStore.fetchAll(queryData)
  loading.value = false
})
</script>

<template>
  <div v-if="!loading" class="h-full flex flex-col gap-4 sm:gap-6 flex-1 overflow-y-auto p-4 sm:p-6">
    <UTable
      :data="data.data"
      :columns="columnConfig"
    />
    <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
      <div class="text-sm text-(--ui-text-muted)">
        <GeneralEntries :meta-data="data.meta" />
      </div>
      <div class="flex items-center gap-1.5">
        <UPagination
          v-model:page="pageNumber"
          class="justify-center"
          :items-per-page="data.meta.per_page"
          :total="data.meta.total"
        />
      </div>
    </div>
  </div>
</template>
