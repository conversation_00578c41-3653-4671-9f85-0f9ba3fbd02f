{"name": "designer", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "lint": "eslint", "lint:fix": "eslint --fix", "prepare": "husky install", "postinstall": "husky install", "typecheck": "nuxi typecheck", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@iconify-json/ph": "^1.2.2", "@lukemorales/query-key-factory": "^1.3.4", "@nuxt/eslint": "^1.3.0", "@nuxt/image": "^1.10.0", "@nuxt/scripts": "^0.11.8", "@nuxt/ui-pro": "^3.0.2", "@nuxtjs/i18n": "^9.4.0", "@pinia/nuxt": "^0.10.1", "@tanstack/vue-query": "^5.81.2", "@tanstack/vue-query-devtools": "^5.83.0", "@tanstack/vue-table": "^8.21.2", "@unhead/vue": "^2.0.10", "@vueuse/core": "^13.1.0", "dayjs-nuxt": "^2.1.11", "eslint": "^9.23.0", "lodash": "^4.17.21", "nuxt": "^3.17.5", "nuxt-auth-sanctum": "^0.6.1", "pinia": "^3.0.3", "pkce-challenge": "^5.0.0", "qs": "^6.14.0"}, "devDependencies": {"@antfu/eslint-config": "^4.13.0", "@nuxt/test-utils": "^3.19.1", "@types/node": "^22.15.3", "@types/qs": "^6.9.18", "@types/youtube": "^0.1.2", "@vue/test-utils": "^2.4.6", "eslint": "^9.23.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "nuxt-lodash": "^2.5.3", "typescript": "^5.8.2", "vitest": "^3.2.4"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "eslint"]}}