<script setup lang="ts">
import type { TableColumn } from '#ui/types'
import type { SortingState } from '@tanstack/vue-table'
import { useClone } from '#imports'
import { AssetQueryFactory } from '~/api/assets'
import QueryData from '~/utils/query/QueryData'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { ColumnGenerator } from '~/utils/table/ColumnGenerator'
import { DynProcessingStatus } from '~/utils/types/api/api'
import { ValidateActionReject } from '~/utils/validate/validate-action'
import { ValidateActionExecutor } from '~/utils/validate/validate-action.executor'

// Store imports
const settingsStore = useSettingsStore()
const assetTypeStore = useAssetTypeStore()
const assetTypeTableFieldStore = useAssetTypeTableFieldStore()

// Component initialize
const pageNumber = ref(1)
const assetType = assetTypeStore.getByTableName(settingsStore.settings?.defaultModel ?? '')
const tableFields = assetTypeTableFieldStore.getByAssetTypeId(assetType.id)

const defaultModel = settingsStore.settings?.defaultModel ?? ''

const { useAssetActionMutation } = AssetQueryFactory
  .getQueryInstance(defaultModel)
  .getMutations()
const assetActionMutation = useAssetActionMutation()

const executor = new ValidateActionExecutor(assetActionMutation.mutateAsync)

const query: Ref<QueryData> = shallowRef(new QueryData())
query.value.setFilters({
  processing_status: [
    DynProcessingStatus.PENDING_VALIDATION,
    DynProcessingStatus.SAVED,
    DynProcessingStatus.SUBMIT_FAILED,
  ],
})

if (settingsStore.settings?.defaultModel === 'dyn_transport_order') {
  query.value.setSort([{ desc: true, id: 'loading_date' }])
}

const { useAssetTableDataQuery } = AssetQueryFactory
  .getQueryInstance(assetType?.table_name)
  .getQueries()

const {
  data: assetData,
  isFetching: isFetchingAssets,
} = useAssetTableDataQuery(query)

async function pageChanged() {
  const newQuery = useClone(query.value)
  newQuery.setPage(pageNumber.value)

  query.value = newQuery
}

// Functions
async function onSortingChange(sorting: SortingState) {
  const newQuery = useClone(query.value)
  newQuery.setSort(sorting)

  query.value = newQuery
}

const generatedColumns = [...new ColumnGenerator().generateColumns(tableFields, false, true), new ActionColumn<any>('Actions', [
  {
    label: 'Validate',
    onClick: async (row) => {
      await navigateTo(`/validate/${row.id}`)
    },
  },
  {
    label: 'Reject',
    onClick: async (row) => {
      await executor.execute(row.id, new ValidateActionReject())
    },
  },
])]
const columnConfig: TableColumn<any>[] = generatedColumns.map(column => column.getConfig())
</script>

<template>
  <div v-if="!isFetchingAssets && assetData" class="h-full flex flex-col gap-4 sm:gap-6 flex-1 overflow-y-auto p-4 sm:p-6">
    <UTable
      :sorting="query.getSort()"
      :data="assetData.data"
      :columns="columnConfig"
      :sorting-options="{ manualSorting: true }"
      @update:sorting="onSortingChange"
    />
    <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
      <div class="text-sm text-(--ui-text-muted)">
        <GeneralEntries :meta-data="assetData.meta" />
      </div>
      <div class="flex items-center gap-1.5">
        <UPagination
          v-model:page="pageNumber"
          class="justify-center"
          :total="assetData.meta.total"
          @click="pageChanged"
        />
      </div>
    </div>
  </div>
</template>
