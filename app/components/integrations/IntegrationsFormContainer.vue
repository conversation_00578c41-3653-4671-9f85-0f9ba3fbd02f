<script setup lang="ts">
import { useAvailableIntegrationsQuery } from '~/api/integrations/queries/useIntegrationQueries'
import IntegrationsUpsertForm from './forms/IntegrationsUpsertForm.vue'

const props = defineProps<{
  integrationId: string
  integrationType: string
  isCreateMode: boolean
}>()

const isValidIntegrationType = ref(true)

const {
  data: availableIntegrations,
} = useAvailableIntegrationsQuery()

watch(availableIntegrations, (newValue) => {
  if (!newValue) {
    return
  }

  isValidIntegrationType.value = newValue[props.integrationType] !== undefined
})
</script>

<template>
  <div v-if="!isValidIntegrationType" class="m-6">
    <div class="text-center py-8">
      <UIcon name="i-ph-warning-circle" class="text-red-500 text-4xl mb-4" />
      <h3 class="text-lg font-semibold mb-2">
        Invalid Integration Type
      </h3>
      <p class="text-gray-600 mb-4">
        The integration type "{{ integrationType }}" is not available or supported.
      </p>
      <UButton
        to="/admin/integrations"
        label="Back to Integrations"
        color="primary"
      />
    </div>
  </div>

  <IntegrationsUpsertForm
    v-else
    :integration-id="isCreateMode ? undefined : integrationId"
    :integration-type="integrationType"
    :is-create-mode="isCreateMode"
  />
</template>
