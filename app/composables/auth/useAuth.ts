import type { TokenResponse } from '~/schemas/auth/token-response.schema'
import pkceChallenge from 'pkce-challenge'

interface UseAuthOptions {
  getRedirectUri: () => string
  getClientId: () => string
  getApiBaseUrl: () => string
  onTokenReceived: (token: TokenResponse) => void
  fetchToken: (code: string, verifier: string) => Promise<TokenResponse>
}

const PKCE_VERIFIER_KEY = 'pkce_verifier'

export function useAuth({
  getRedirectUri,
  getClientId,
  getApiBaseUrl,
  onTokenReceived,
  fetchToken,
}: UseAuthOptions) {
  const generateRedirectUrl = (challenge: string) => {
    const url = new URL(`${getApiBaseUrl()}/oauth/authorize`)
    url.searchParams.set('response_type', 'code')
    url.searchParams.set('client_id', getClientId())
    url.searchParams.set('redirect_uri', getRedirectUri())
    url.searchParams.set('scope', '')
    url.searchParams.set('state', '')
    url.searchParams.set('code_challenge', challenge)
    url.searchParams.set('code_challenge_method', 'S256')
    url.searchParams.set('prompt', 'login')
    return url.toString()
  }

  const login = async () => {
    const { code_challenge, code_verifier } = await pkceChallenge()

    sessionStorage.setItem(PKCE_VERIFIER_KEY, code_verifier)

    window.location.href = generateRedirectUrl(code_challenge)
  }

  const handleRedirectCallback = async () => {
    const { query } = useRoute()
    const code = query.code as string
    if (!code)
      throw new Error('Missing authorization code')

    const verifier = sessionStorage.getItem(PKCE_VERIFIER_KEY)
    if (!verifier)
      throw new Error('Missing PKCE verifier')

    const token = await fetchToken(code, verifier)
    onTokenReceived(token)
  }

  return { login, handleRedirectCallback, generateRedirectUrl }
}
