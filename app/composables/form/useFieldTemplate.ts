import type { AssetTypeFieldData } from '~/utils/types/api/api'

export function useFieldTemplate(
  data: Ref<{ template: string | null }>,
  fields: Ref<AssetTypeFieldData[] | undefined>,
) {
  const selectedTemplateField = ref<string | null>(null)

  const definedFields = computed(() => {
    return fields.value?.filter(x => x.field && x.id) || []
  })

  const getTemplate = () =>
    (data.value.template ?? '').replace(/\{\{(.*?)\}\}/g, (_, id) => {
      if (!id)
        return ''
      try {
        const field = definedFields.value.find(x => x.id === id.trim())
        return `{{${field?.field ?? id}}}`
      }
      catch {
        return `{{${id}}}`
      }
    })

  function setTemplate(raw: string | null) {
    if (raw == null)
      return (data.value.template = '')
    data.value.template = raw.replace(/\{\{(.*?)\}\}/g, (_, name) => {
      if (!name)
        return ''
      const f = definedFields.value.find(x => x.field === name)
      return `{{${f ? f.id : name}}}`
    })
  }

  function addFieldToTemplate() {
    if (!selectedTemplateField.value)
      return
    data.value.template = `${data.value.template ?? ''} {{${selectedTemplateField.value}}}`
    selectedTemplateField.value = null
  }

  return { selectedTemplateField, getTemplate, setTemplate, addFieldToTemplate }
}
