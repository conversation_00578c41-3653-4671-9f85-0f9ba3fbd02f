import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { importProfileKeys } from '../keys/import-profile.keys'
import { ImportProfileService } from '../services/import-profile.service'

export function useCreateImportProfileMutation() {
  return useMutation({
    mutationFn: (data: ImportProfileFormData) => ImportProfileService.create(data),
    onSuccess: () => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
      ])
    },
  })
}

export function useUpdateImportProfileGeneralInfoMutation() {
  return useMutation({
    mutationFn: ({ importProfileId, data }: { importProfileId: string, data: ImportProfileFormData }) =>
      ImportProfileService.updateGeneralInfo(importProfileId, data),
    onSuccess: (_, { importProfileId }) => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
        importProfileKeys.byId(importProfileId),
        importProfileKeys.byIdOrFail(importProfileId),
      ])
    },
  })
}

export function useUpdateImportProfileConfigurationMutation() {
  return useMutation({
    mutationFn: ({ importProfileId, data }: { importProfileId: string, data: ImportProfileFormData }) =>
      ImportProfileService.updateConfiguration(importProfileId, data),
    onSuccess: (_, { importProfileId }) => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
        importProfileKeys.byId(importProfileId),
        importProfileKeys.byIdOrFail(importProfileId),
      ])
    },
  })
}

export function useUpdateImportProfileMappingMutation() {
  return useMutation({
    mutationFn: ({ importProfileId, data }: { importProfileId: string, data: ImportProfileFormData }) =>
      ImportProfileService.updateMapping(importProfileId, data),
    onSuccess: (_, { importProfileId }) => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
        importProfileKeys.byId(importProfileId),
        importProfileKeys.byIdOrFail(importProfileId),
      ])
    },
  })
}

export function useUpdateImportProfileScheduleMutation() {
  return useMutation({
    mutationFn: ({ importProfileId, data }: { importProfileId: string, data: ImportProfileFormData }) =>
      ImportProfileService.updateSchedule(importProfileId, data),
    onSuccess: (_, { importProfileId }) => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
        importProfileKeys.byId(importProfileId),
        importProfileKeys.byIdOrFail(importProfileId),
      ])
    },
  })
}

export function useDeleteImportProfileMutation() {
  return useMutation({
    mutationFn: (importProfileId: string) => ImportProfileService.delete(importProfileId),
    onSuccess: (_, importProfileId) => {
      batchInvalidateKeys(queryClient, [
        importProfileKeys.all,
        importProfileKeys.byId(importProfileId),
        importProfileKeys.byIdOrFail(importProfileId),
      ])
    },
  })
}

export function useRunImportProfileMutation() {
  return useMutation({
    mutationFn: (importProfileId: string) => ImportProfileService.run(importProfileId),
  })
}
