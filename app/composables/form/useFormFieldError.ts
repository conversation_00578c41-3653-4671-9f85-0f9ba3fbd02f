import type { ComputedRef } from 'vue'
import { computed } from 'vue'

export function useFormFieldError(
  errors: ComputedRef<Record<string, string[]>> | Record<string, string[]>,
  errorDisplay: ComputedRef<'full' | 'border' | 'none'> | 'full' | 'border' | 'none' = 'full',
) {
  const firstError = computed(() => {
    const errorsValue = typeof errors === 'object' && 'value' in errors ? errors.value : errors
    return getFirstError(errorsValue as Record<string, string[]>)
  })

  const showErrorBorderOnly = computed(() => {
    const displayValue = typeof errorDisplay === 'string' ? errorDisplay : errorDisplay.value
    return displayValue === 'border'
  })

  const showError = computed(() => {
    const displayValue = typeof errorDisplay === 'string' ? errorDisplay : errorDisplay.value
    return displayValue !== 'none'
  })

  const error = computed(() => {
    if (!showError.value || !firstError.value) {
      return false
    }
    if (showErrorBorderOnly.value) {
      return true
    }
    return firstError.value
  })

  return {
    firstError,
    error,
    showError,
    showErrorBorderOnly,
  }
}
