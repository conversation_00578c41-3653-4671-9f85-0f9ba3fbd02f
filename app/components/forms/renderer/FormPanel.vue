<script setup lang="ts">
import type { FormElement } from '~/utils/forms/FormElement'
import { errorDisplayProp } from '~/utils/forms/errorDisplayProp'
import { FormField } from '~/utils/forms/FormField'
import { FormPanel } from '~/utils/forms/FormPanel'

defineProps({
  elements: {
    type: Object as PropType<FormElement[]>,
    required: true,
  },
  errors: {
    type: Object as PropType<Record<string, string[]>>,
    required: true,
  },
  isReadOnly: {
    type: Boolean,
    default: false,
  },
  ...errorDisplayProp,
})

const model = defineModel({
  type: Object as PropType<Record<string, any>>,
  required: true,
})
function isFormPanel(element: FormElement): element is FormPanel {
  return element instanceof FormPanel
}

function isFormField(element: FormElement): element is FormField {
  return element instanceof FormField
}
</script>

<template>
  <div class="grid grid-cols-12 gap-y-2 gap-x-4">
    <template v-for="(element, i) in elements">
      <div v-if="isFormPanel(element)" :key="`panel${i}`" :class="`col-span-${element.cols}`">
        <FormsRendererFormPanel
          v-model="model"
          :elements="element.elements"
          :errors="errors"
          :is-read-only="isReadOnly"
          :error-display="errorDisplay"
        />
      </div>
      <div v-else-if="isFormField(element)" :key="element.id" :class="`col-span-${element.cols}`">
        <FormsRendererFormField
          v-model="model[element.name]"
          :field="element"
          :errors="errors"
          :is-read-only="isReadOnly"
          :error-display="errorDisplay"
        />
      </div>
    </template>
  </div>
</template>
