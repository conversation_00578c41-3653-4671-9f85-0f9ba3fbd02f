import type { AssetTypeIndexData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeIndexKeys } from '../keys/asset-type-index.keys'

export class AssetTypeIndexCacheService {
  private static getCachedAssetTypeIndexes(): AssetTypeIndexData[] | undefined {
    const { getCachedQuery } = useCachedQueries()
    return getCachedQuery<AssetTypeIndexData[]>([assetTypeIndexKeys.all]).value
  }

  static getByAssetTypeId(assetTypeId: string): AssetTypeIndexData[] {
    const indexes = this.getCachedAssetTypeIndexes()
    return indexes?.filter((i: AssetTypeIndexData) => i.asset_type_id === assetTypeId) || []
  }

  static getByAssetTypeFieldId(assetTypeFieldId: string): AssetTypeIndexData[] {
    const indexes = this.getCachedAssetTypeIndexes()
    return indexes?.filter((i: AssetTypeIndexData) => i.asset_type_field_id === assetTypeFieldId) || []
  }
}
