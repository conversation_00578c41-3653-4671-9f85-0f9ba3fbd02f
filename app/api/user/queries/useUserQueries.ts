import type QueryData from '~/utils/query/QueryData'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { userKeys } from '../keys/user.keys'
import { IdentityService } from '../services/identity.service'

export function useUserInfo() {
  return useQuery({
    ...userKeys.me,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useUsersQuery(
  queryData: MaybeRef<QueryData>,
  options?: ComposableQueryOptions<{ queryData: QueryData }, typeof userKeys.all>,
) {
  return useQuery({
    queryKey: computed(() => {
      const unreffedData = unref(queryData)
      return userKeys.all(unreffedData).queryKey
    }),
    queryFn: () => {
      const unreffedData = unref(queryData)
      return IdentityService.getAll(unreffedData)
    },
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useUserQueryById(
  id: string,
  options?: ComposableQueryOptions<{ id: string }, typeof userKeys.byId>,
) {
  return useQuery({
    ...userKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}
