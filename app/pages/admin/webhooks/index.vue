<script setup lang="ts">
import { useDeleteWebhookMutation, useWebhooksQuery } from '~/api/webhooks'
import WebhooksList from '~/components/webhooks/WebhooksList.vue'

definePageMeta({
  layout: 'admin',
})

// Use real webhook API
const {
  data: webhooks,
  isFetching: isFetchingWebhooks,
} = useWebhooksQuery()

const toast = useToast()

const router = useRouter()
function configureWebhook(webhook: any) {
  router.push({
    path: `/admin/webhooks/${webhook.id}`,
  })
}
const deleteWebhookMutation = useDeleteWebhookMutation()

async function deleteWebhook(webhookId: string) {
  try {
    await deleteWebhookMutation.mutateAsync(webhookId)
    toast.add({
      title: 'Webhook Deleted',
      description: 'Webhook has been successfully deleted',
      color: 'success',
    })
  }
  catch {
    toast.add({
      title: 'Error',
      description: 'Failed to delete webhook',
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Webhooks">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            Webhooks
          </h1>
          <p class="text-gray-500 mt-3">
            Manage your webhook endpoints and monitor their performance.
          </p>
        </div>
        <UButton
          label="Create Webhook"
          size="lg"
          @click="$router.push('/admin/webhooks/new')"
        />
      </div>
      <WebhooksList
        v-if="!isFetchingWebhooks && webhooks"
        :webhooks="webhooks"
        @configure-webhook="configureWebhook"
        @delete-webhook="deleteWebhook"
      />
    </template>
  </UDashboardPanel>
</template>
