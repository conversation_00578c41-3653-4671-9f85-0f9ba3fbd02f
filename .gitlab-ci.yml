stages:
  - build
  - deploy

.build: &build
  image:
    name: node:22.16-slim
    entrypoint: ['']
  timeout: 4 hours
  before_script:
    - apt update; apt install -y autoconf automake curl
    - npm install --global nuxi
  script:
    - npm install -g @vue/cli
    - npm ci
    - nuxi generate
    - ls -lrt
    - du -sh ./
  artifacts:
    paths:
      - package-lock.json
      - dist/
      - .output/
    expire_in: 4 hours

# ######

# STAG #

build:stag:
  stage: build
  <<: *build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"'

deploy:stag:
  stage: deploy
  image:
    name: amazon/aws-cli:latest
    entrypoint: [/bin/sh, -c]
  timeout: 2 hours
  script:
    - |
      aws configure set aws_access_key_id $S3_STAG_ACCESS_KEY
      aws configure set aws_secret_access_key $S3_STAG_SECRET_KEY
      aws configure set region eu-central-1
      cd dist/
      aws s3 sync . s3://hyperfox-order-portal-stag/ --delete
      # do cloudfront cache invalidation
      export DISTRI=$(aws cloudfront list-distributions --query "DistributionList.Items[?Aliases.Items[?contains(@, 'staging.testerfox.eu')]].Id" --output text)
      aws cloudfront create-invalidation --distribution-id  $DISTRI --paths "/*"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "develop"'

# PROD #

build:prod:
  stage: build
  <<: *build
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d{1,2}\.\d{1,5}\.\d{1,5}$/'
      when: always

deploy:prod:
  stage: deploy
  image:
    name: amazon/aws-cli:latest
    entrypoint: [/bin/sh, -c]
  timeout: 2 hours
  script:
    - |
      aws configure set aws_access_key_id $S3_PROD_ACCESS_KEY
      aws configure set aws_secret_access_key $S3_PROD_SECRET_KEY
      aws configure set region eu-central-1
      cd dist/
      aws s3 sync . s3://hyperfox-order-portal-prod/ --delete
      # do cloudfront cache invalidation
      export DISTRI=$(aws cloudfront list-distributions --query "DistributionList.Items[?Aliases.Items[?contains(@, 'hyperfox.cloud')]].Id" --output text)
      aws cloudfront create-invalidation --distribution-id  $DISTRI --paths "/*"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d{1,2}\.\d{1,5}\.\d{1,5}$/'
      when: always
