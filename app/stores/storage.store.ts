import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useStorageStore = defineStore('storage', () => {
  async function getAttachmentUrl(
    attachmentId: string,
  ): Promise<string> {
    const response = await useApiClient()<ApiResponse<{ url: string }>>(
      `api/storage/attachment/${attachmentId}/url?alias=1`,
    )

    return response.data.url
  }

  return {
    getAttachmentUrl,
  }
})
