import type {
  AvailableIntegrationData,
  CreateIntegrationData,
  IntegrationData,
  StatusMessage,
  UpdateIntegrationData,
} from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useIntegrationsStore = defineStore('integrations', () => {
  const availableIntegrations = ref<Record<string, AvailableIntegrationData>>({})
  const integrations = ref<IntegrationData[]>([])

  // Local state functions
  function getAvailableIntegrationByProvider(integrationClass: string): AvailableIntegrationData {
    const availableIntegration = availableIntegrations.value[integrationClass]
    if (!availableIntegration)
      throw new Error(`Available integration not found for class: ${integrationClass}`)
    return availableIntegration
  }

  // API functions
  async function hydrateAvailable() {
    const response = await useApiClient()<ApiResponse<Record<string, AvailableIntegrationData>>>(
      `api/connectivity/available-integrations`,
    )

    availableIntegrations.value = response.data
  }

  async function hydrate() {
    const response = await useApiClient()<ApiResponse<IntegrationData[]>>(
      `api/connectivity/integrations`,
    )

    integrations.value = response.data
  }

  async function fetchById(integrationId: string) {
    return await useApiClient()<ApiResponse<IntegrationData>>(
      `api/connectivity/integrations/${integrationId}`,
    )
  }

  async function create(data: CreateIntegrationData) {
    const response = await useApiClient()<ApiResponse<IntegrationData>>(
      `api/connectivity/integrations`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    integrations.value = [...integrations.value, response.data]

    return response.data
  }

  async function update(integrationId: string, data: UpdateIntegrationData) {
    const response = await useApiClient()<ApiResponse<IntegrationData>>(
      `api/connectivity/integrations/${integrationId}`,
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    integrations.value = integrations.value.map(integration => integration.id === integrationId ? response.data : integration)

    return response
  }

  async function test(integrationId: string) {
    return await useApiClient()<ApiResponse<Record<string, StatusMessage>>>(
      `api/connectivity/integrations/${integrationId}/test`,
    )
  }

  async function run(integrationId: string) {
    return await useApiClient()<ApiResponse<Record<string, StatusMessage>>>(
      `api/connectivity/integrations/${integrationId}/run`,
    )
  }

  return {
    availableIntegrations,
    integrations,

    getAvailableIntegrationByProvider,

    hydrateAvailable,
    hydrate,
    fetchById,

    create,
    update,

    test,
    run,
  }
})
