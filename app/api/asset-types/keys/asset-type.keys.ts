import { createQueryKeys } from '@lukemorales/query-key-factory'
import { AssetTypeCacheService } from '../services/asset-type-cache.service'
import { AssetTypeService } from '../services/asset-type.service'

export const assetTypeKeys = createQueryKeys('assetTypes', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeCacheService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeCacheService.getByIdOrFail(id),
  }),
  byTableName: (tableName: string) => ({
    queryKey: [tableName],
    queryFn: () => AssetTypeCacheService.getByTableName(tableName),
  }),
  emptyEntry: (id: string) => ({
    queryKey: [id],
    queryFn: () => AssetTypeService.emptyEntry(id),
  }),
})
