<script setup lang="ts">
import type { BadgeColor } from '~/utils/table/BadgeColumn'
import type { BadgeConfigData } from '~/utils/types/api/api'
import { validBadgeColors } from '~/utils/table/BadgeColumn'
import { BadgeConfigMode } from '~/utils/types/api/api'

// Supporting types
type Props = {
  value: string | number
  suffix: null | string
} & BadgeConfigData

const props = defineProps<Props>()

const extractColor = computed<string | undefined>(() => {
  if (props.mode === BadgeConfigMode.Status) {
    // return props.statuses?.[props.value] ?? 'neutral'
    return props.statuses?.find(x => x.name === props.value)?.color
  }

  if (props.mode === BadgeConfigMode.Threshold) {
    const filteredValues = props.thresholds?.filter(({ min }) => props.value as number >= min)

    if (!filteredValues || filteredValues.length === 0) {
      return 'neutral'
    }

    return filteredValues.reduce((max, obj) =>
      obj.min > max.min ? obj : max,
    )?.color
  }

  return undefined
})

const getColor = computed<BadgeColor>(() => {
  const color = extractColor.value ?? ''
  if (validBadgeColors.has(color as BadgeColor)) {
    return color as BadgeColor
  }
  return 'neutral'
})
</script>

<template>
  <UBadge :color="getColor" variant="subtle">
    {{ value }} {{ suffix }}
  </UBadge>
</template>
