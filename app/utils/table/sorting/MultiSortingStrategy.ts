import type { Column } from '@tanstack/vue-table'
import type { SortingStrategy } from './SortingStrategy'

export class MultiSortingStrategy implements SortingStrategy {
  getSortIcon(isSorted: false | 'asc' | 'desc') {
    return isSorted ? isSorted === 'asc' ? 'i-ph-sort-ascending' : 'i-ph-sort-descending' : 'i-ph-funnel'
  }

  handleClick<T>(e: MouseEvent, column: Column<T, unknown>) {
    e.stopPropagation()

    switch (column.getIsSorted()) {
      case 'asc':
        column.toggleSorting(true, true)
        break
      case false:
        column.toggleSorting(false, true)
        break
      default:
        column.clearSorting()
        break
    }
  }
}
