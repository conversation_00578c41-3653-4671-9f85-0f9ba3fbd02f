import { z } from 'zod'

export const webhookConfigurationSchema = z.object({
  name: z.string()
    .min(1, 'Webhook name is required')
    .max(100, 'Webhook name must be less than 100 characters')
    .regex(/^[\w\s\-]+$/, 'Webhook name can only contain letters, numbers, spaces, hyphens, and underscores'),
  event: z.string()
    .min(1, 'Event is required')
    .refine(value => value === 'order.validated', {
      message: 'Only order.validated event is currently supported',
    }),
  target_url: z.string()
    .min(1, 'Target URL is required')
    .url('Please enter a valid URL')
    .refine((url) => {
      // Ensure it's https in production or allow http for development
      return url.startsWith('https://') || url.startsWith('http://localhost') || url.startsWith('http://127.0.0.1')
    }, {
      message: 'URL must use HTTPS (or HTTP for localhost)',
    }),
})

export type WebhookConfigurationFormData = z.infer<typeof webhookConfigurationSchema>
