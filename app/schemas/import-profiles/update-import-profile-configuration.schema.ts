import { z } from 'zod'

const apiImportProfileDataSchema = z.object({
  page_parameter: z.string().nullable(),
  page_start_index: z.number().nullable(),
  page_step_size: z.number().nullable(),
  page_size_parameter: z.string().nullable(),
  page_size_length: z.number().nullable(),
  data_wrapper: z.string().nullable(),
  additional_parameters: z.string().nullable(),
})

export const updateImportProfileConfigurationSchema = z.object({
  path: z.string(),
  encoding: z.string().nullable().optional(),
  config: apiImportProfileDataSchema.nullable(),
})

export type UpdateImportProfileConfigurationFormData = z.infer<typeof updateImportProfileConfigurationSchema>
