import type { ApiClientData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { apiClientKeys } from '../keys/api-client.keys'

export function useApiClientsQuery() {
  return useQuery({
    ...apiClientKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useApiClientByIdQuery(
  id: string,
  options?: ComposableQueryOptions<ApiClientData, typeof apiClientKeys.byId>,
) {
  return useQuery({
    ...apiClientKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useApiClientByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<ApiClientData, typeof apiClientKeys.byIdOrFail>,
) {
  return useQuery({
    ...apiClientKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}
