<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { CreateAssetTypeData } from '~/utils/types/api/api'

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()

const isPending = ref(false)
const data = ref<CreateAssetTypeData>({
  label: '',
  auditing_enabled: false,
  supporting_data: false,
  locked: false,
})

async function onSubmit() {
  try {
    isPending.value = true

    const assetType = await assetTypeStore.create(data.value)
    await assetTypeStore.hydrate()

    emit('close', false)
    toast.add({ title: 'Master data created', color: 'success' })
    await navigateTo(`/admin/master-data/${assetType.data.id}`)
  }
  catch (e) {
    toast.add({
      title: 'Error creating master data field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover
    title="Add master data"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Label" class="col-span-12">
            <UInput v-model="data.label" />
          </UFormField>
          <UFormField label="Auditing enabled" class="col-span-6">
            <USwitch v-model="data.auditing_enabled" />
          </UFormField>
          <UFormField label="Locked" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Create
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
