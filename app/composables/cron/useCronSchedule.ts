import type { Day } from '~/utils/dates/day.enum'

interface CronSchedule {
  time: string
  days: Day[]
}

export function useCronSchedule() {
  function toCron(schedule: CronSchedule): string {
    const [hourStr = '00', minuteStr = '00'] = schedule.time.split(':')
    const hour = Number.parseInt(hourStr, 10)
    const minute = Number.parseInt(minuteStr, 10)

    const days = schedule.days.sort().join(',')
    return `${minute} ${hour} * * ${days}`
  }

  function fromCron(cron?: string | null): CronSchedule {
    const defaultSchedule: CronSchedule = {
      days: [],
      time: '',
    }

    if (typeof cron !== 'string')
      return defaultSchedule

    const parts = cron.trim().split(' ')
    if (parts.length !== 5)
      return defaultSchedule

    const [minuteStr = '', hourStr = '', , , dayStr = ''] = parts
    const minute = Number.parseInt(minuteStr, 10)
    const hour = Number.parseInt(hourStr, 10)

    if (Number.isNaN(minute) || Number.isNaN(hour))
      return defaultSchedule

    const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`

    const days = dayStr
      .split(',')
      .map(d => Number.parseInt(d, 10))
      .filter(d => Number.isInteger(d) && d >= 0 && d <= 6) as Day[]

    return { time, days }
  }

  return {
    toCron,
    fromCron,
  }
}
