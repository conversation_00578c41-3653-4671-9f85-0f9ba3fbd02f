import { assetTypeFieldKeys } from '~/api/asset-type-fields'
import { assetTypeFormFieldKeys } from '~/api/asset-type-form-fields'
import { queryClient } from '~/plugins/vue-query'
import { useTenantStore } from '~/stores/tenant.store'

export default defineNuxtRouteMiddleware(async (to) => {
  if (to.path.includes('auth')) {
    return
  }

  if (!useUserStore().isLoggedIn) {
    return
  }

  if (useConfigStore().loaded) {
    return
  }

  await Promise.all([
    useAssetTypeStore().hydrate(),
    queryClient.prefetchQuery(assetTypeFieldKeys.all),
    useAssetTypeRelationshipStore().hydrate(),
    useAssetTypeIndexStore().hydrate(),
    queryClient.prefetchQuery(assetTypeFormFieldKeys.all),
    useAssetTypeTableFieldStore().hydrate(),
    useSettingsStore().hydrate(),
    useTenantStore().hydrate(),
  ])

  await useConfigStore().setLoaded(true)
})
