import { AuthService } from '~/api/auth/services/auth.service'
import { userKeys } from '~/api/user'
import { queryClient } from '~/plugins/vue-query'
import { getApiBaseUrl } from './api-base-url.constant'

export function useApiClient<T>() {
  const userStore = useUserStore()

  return $fetch.create<T>({
    baseURL: getApiBaseUrl(),
    onRequest({ options }) {
      options.headers.set('Accept', `application/json`)
      if (userStore.accessToken) {
        options.headers.set('Authorization', `Bearer ${userStore.accessToken}`)
      }
    },
    onResponseError: async ({ response, request, options }) => {
      if (response.status === 401 && userStore.refreshToken) {
        try {
          const tokenResponse = await AuthService.refreshToken(userStore.refreshToken)

          userStore.setAuth(tokenResponse)

          const retryOptions = {
            ...options,
            headers: {
              ...options.headers,
              Authorization: `Bearer ${tokenResponse.access_token}`,
            },
            method: options.method as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD' | undefined,
          }

          await $fetch.raw(request, retryOptions)
        }
        catch {
          queryClient.removeQueries({ queryKey: userKeys.me.queryKey })
          userStore.clearAuth()
          navigateTo('/login')
        }
      }
    },
  })
}
