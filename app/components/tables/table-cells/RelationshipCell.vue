<script setup lang="ts">
import { ref, watch } from 'vue'
import { useAssetTypeFieldByIdOrFailQuery } from '~/api/asset-type-fields'
import { AssetQueryFactory } from '~/api/assets'

const props = defineProps<{
  value: string | null | undefined
  row: Record<string, any>
  config?: {
    relationship?: {
      asset_type_relationship_id: string
      asset_type_field_id: string
    }
  }
}>()

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const assetTypeStore = useAssetTypeStore()

const relationship = computed(() =>
  assetTypeRelationshipStore.getById(props.config?.relationship?.asset_type_relationship_id ?? ''),
)

const assetType = computed(() =>
  assetTypeStore.getById(relationship.value?.relationship_asset_type_id ?? ''),
)

const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(assetType.value.table_name)
  .getQueries()

const id = props.row[relationship.value?.relationship_key ?? '']

const { data: assetData, isFetching: isFetchingAsset } = useAssetQuery(
  id!,
  {
    enabled: !!id,
  },
)

const { data: assetTypeField } = useAssetTypeFieldByIdOrFailQuery(
  assetType.value.display_field_id!,
  {
    enabled: !!assetType.value?.display_field_id,
  },
)

const displayField = computed(() => {
  return assetTypeField.value?.field ?? ''
})

function getDisplayFieldName() {
  if (!displayField.value || !assetData.value) {
    return 'n/a'
  }

  return assetData.value[displayField.value] ?? 'n/a'
}

const displayFieldName = ref(getDisplayFieldName())

watch(() => assetData.value, () => {
  displayFieldName.value = getDisplayFieldName()
})

watch(() => assetTypeField.value, () => {
  displayFieldName.value = getDisplayFieldName()
}, { immediate: true })
</script>

<template>
  <span v-if="!isFetchingAsset">{{ displayFieldName }}</span>
</template>
