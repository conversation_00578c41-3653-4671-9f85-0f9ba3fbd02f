import type {
  AssetTypeRelationshipData,
  CreateAssetTypeRelationshipData,
  UpdateAssetTypeRelationshipData,
} from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { ASSET_TYPE_RELATIONSHIP_API_ROUTES } from './asset-type-relationship-service.routes'

export class AssetTypeRelationshipService {
  static async getAll(): Promise<AssetTypeRelationshipData[]> {
    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData[]>>(
      ASSET_TYPE_RELATIONSHIP_API_ROUTES.ALL,
    )
    return response.data
  }

  static async getById(id: string): Promise<AssetTypeRelationshipData> {
    // Find the relationship in the cache or fetch it individually if needed
    const allRelationships = await this.getAll()
    const relationship = allRelationships.find(rel => rel.id === id)

    if (!relationship) {
      throw new Error(`Asset type relationship not found with id: ${id}`)
    }

    return relationship
  }

  static async create(
    assetTypeId: string,
    data: CreateAssetTypeRelationshipData,
  ): Promise<AssetTypeRelationshipData> {
    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData>>(
      ASSET_TYPE_RELATIONSHIP_API_ROUTES.BY_ASSET_TYPE_ID(assetTypeId),
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async update(
    assetTypeId: string,
    id: string,
    data: UpdateAssetTypeRelationshipData,
  ): Promise<AssetTypeRelationshipData> {
    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData>>(
      ASSET_TYPE_RELATIONSHIP_API_ROUTES.BY_ID(assetTypeId, id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async delete(assetTypeId: string, id: string): Promise<void> {
    await useApiClient()<ApiResponse<boolean>>(
      ASSET_TYPE_RELATIONSHIP_API_ROUTES.BY_ID(assetTypeId, id),
      {
        method: 'DELETE',
      },
    )
  }
}
