import type { AssetTypeFormFieldData, UpsertAssetTypeFormFieldData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { ASSET_TYPE_FORM_FIELD_API_ROUTES } from './asset-type-form-field-service.routes'

export class AssetTypeFormFieldService {
  static async getAll(): Promise<AssetTypeFormFieldData[]> {
    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData[]>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.ALL,
    )
    return response.data
  }

  static async create(
    assetTypeId: string,
    data: UpsertAssetTypeFormFieldData,
  ): Promise<AssetTypeFormFieldData> {
    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.BY_ASSET_TYPE_ID(assetTypeId),
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async update(
    assetTypeId: string,
    id: string,
    data: UpsertAssetTypeFormFieldData,
  ): Promise<AssetTypeFormFieldData> {
    const response = await useApiClient()<ApiResponse<AssetTypeFormFieldData>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.BY_ID(assetTypeId, id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )
    return response.data
  }

  static async delete(assetTypeId: string, id: string): Promise<void> {
    await useApiClient()<ApiResponse<boolean>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.BY_ID(assetTypeId, id),
      {
        method: 'DELETE',
      },
    )
  }

  static async generate(assetTypeId: string): Promise<void> {
    await useApiClient()<ApiResponse<void>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.GENERATE(assetTypeId),
    )
  }

  static async moveUp(assetTypeId: string, id: string): Promise<void> {
    await useApiClient()<ApiResponse<void>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.MOVE_UP(assetTypeId, id),
    )
  }

  static async moveDown(assetTypeId: string, id: string): Promise<void> {
    await useApiClient()<ApiResponse<void>>(
      ASSET_TYPE_FORM_FIELD_API_ROUTES.MOVE_DOWN(assetTypeId, id),
    )
  }
}
