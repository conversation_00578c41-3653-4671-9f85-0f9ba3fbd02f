<script setup lang="ts">
definePageMeta({
  middleware: ['auth'],
})

// Store imports
const assetTypeStore = useAssetTypeStore()

const supportingAssetTypes = computed(() => assetTypeStore.getSupportingDataAssetTypes())
const selectedItem = ref(supportingAssetTypes.value.length > 0 ? supportingAssetTypes.value[0]?.id : undefined)
const items = computed(() => {
  const currentSelectedId = selectedItem.value

  return supportingAssetTypes.value.map(assetType => ({
    label: assetType.label,
    value: assetType.id,
    active: assetType.id === currentSelectedId,
    onSelect: () => {
      selectedItem.value = assetType.id
    },
  }))
})
</script>

<template>
  <div class="h-full flex flex-col gap-4 sm:gap-6 flex-1 overflow-y-auto p-4 sm:p-6">
    <UDashboardToolbar>
      <template #left>
        <UNavigationMenu
          :items="items"
          highlight
        />
      </template>
      <template #right>
        <UButton
          leading-icon="i-ph-plus"
          @click="navigateTo('/reference-data/imports')"
        >
          Import
        </UButton>
      </template>
    </UDashboardToolbar>
    <TablesRendererAssetTable
      v-if="selectedItem"
      :key="selectedItem"
      :asset-type="assetTypeStore.getById(selectedItem)"
    />
  </div>
</template>
