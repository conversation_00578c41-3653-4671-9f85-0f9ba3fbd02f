import type { ColumnDef } from '@tanstack/vue-table'
import { UCheckbox } from '#components'
import { BaseColumn } from '~/utils/table/BaseColumn'
import { ColumnType } from '~/utils/types/table/ColumnType'

export class SelectionColumn<T> extends BaseColumn<T> {
  constructor(
    label: string,
  ) {
    super(label, false, ColumnType.Checkbox)
  }

  getConfig(): ColumnDef<T> {
    return {
      id: 'select',
      header: ({ table }) =>
        h(UCheckbox, {
          'modelValue': table.getIsSomePageRowsSelected()
            ? 'indeterminate'
            : table.getIsAllPageRowsSelected(),
          'onUpdate:modelValue': (value: boolean | 'indeterminate') =>
            table.toggleAllPageRowsSelected(!!value),
          'aria-label': 'Select all',
        }),
      cell: ({ row }) =>
        h(UCheckbox, {
          'modelValue': row.getIsSelected(),
          'onUpdate:modelValue': (value: boolean | 'indeterminate') => row.toggleSelected(!!value),
          'aria-label': 'Select row',
        }),
    }
  }
}
