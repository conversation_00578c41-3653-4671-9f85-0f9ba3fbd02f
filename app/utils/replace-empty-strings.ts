export function replaceEmptyStringsWithUndefined<T>(obj: T): T {
  if (Array.isArray(obj)) {
    return obj.map(item => replaceEmptyStringsWithUndefined(item)) as T
  }

  if (obj !== null && typeof obj === 'object') {
    const result: any = {}

    for (const [key, value] of Object.entries(obj)) {
      result[key]
        = value === ''
          ? undefined
          : replaceEmptyStringsWithUndefined(value)
    }

    return result as T
  }

  return obj
}
