import type { AssetTypeRelationshipData } from '../types/api/api'
import { AssetTypeRelationshipMethod } from '../types/api/api'

export function generateNameForRelationship(
  relationshipData: AssetTypeRelationshipData,
): string {
  switch (relationshipData.relationship_method) {
    case AssetTypeRelationshipMethod.HasOne:
    case AssetTypeRelationshipMethod.HasMany:
      return useSnakeCase(relationshipData.relationship_function_name)
    default:
      return relationshipData.relationship_key
  }
}
