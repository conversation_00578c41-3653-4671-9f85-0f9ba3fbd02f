import { AuthService } from '~/api/auth/services/auth.service'
import { getApiBaseUrl } from '~/utils/api/api-base-url.constant'
import { getOauthRedirectUrl } from '~/utils/auth/auth-redirect.constant'
import { getOauthClientId } from '~/utils/auth/client-id.constant'
import { useAuth } from './useAuth'

export function useMyAuth() {
  return useAuth({
    getRedirectUri: () => getOauthRedirectUrl(),
    getClientId: () => getOauthClientId(),
    getApiBaseUrl: () => getApiBaseUrl(),
    onTokenReceived: token => useUserStore().setAuth(token),
    fetchToken: (code, verifier) => AuthService.getToken(code, verifier),
  })
}
