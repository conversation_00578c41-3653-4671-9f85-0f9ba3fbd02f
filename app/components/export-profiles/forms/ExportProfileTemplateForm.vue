<script setup lang="ts">
import type { ExportProfileTemplateFormData } from '~/schemas/export-profiles/export-profile-template.schema'
import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { exportProfileTemplateSchema } from '~/schemas/export-profiles/export-profile-template.schema'

const props = defineProps<{
  profile: ExportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ExportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formState = ref<ExportProfileTemplateFormData>({
  template: props.profile.template ?? '',
  crlf: props.profile.crlf ?? false,
})

watch(() => props.profile.template, (newTemplate) => {
  formState.value.template = newTemplate ?? ''
}, { immediate: true })

function submitForm() {
  emit('submit', {
    ...props.profile,
    template: formState.value.template,
    crlf: formState.value.crlf,
  })
}
</script>

<template>
  <UForm
    :state="formState"
    :schema="exportProfileTemplateSchema"
    class="space-y-4"
    @submit="submitForm"
  >
    <h2 class="text-lg font-bold">
      XML template
    </h2>

    <p class="text-sm text-neutral-600">
      You can customize your XML template using the variables below. These will be automatically replaced with real data when the export runs.
      You can also use
      <NuxtLink to="https://twig.symfony.com/doc/3.x/templates.html" target="_blank" class="text-primary underline">
        TWIG
      </NuxtLink>
      , a templating language, to format values, add conditions, or loop through lists.
    </p>

    <div class="flex flex-wrap gap-2 text-sm">
      <code class="bg-neutral-100 px-2 py-1 rounded">${FirstName}</code>
      <code class="bg-neutral-100 px-2 py-1 rounded">${LastName}</code>
      <code class="bg-neutral-100 px-2 py-1 rounded">${DocumentID}</code>
      <code class="bg-neutral-100 px-2 py-1 rounded">${ShipmentID}</code>
      <code class="bg-neutral-100 px-2 py-1 rounded">${TrackingNumber}</code>
      <code class="bg-neutral-100 px-2 py-1 rounded">${SenderID}</code>
    </div>

    <UFormField label="XML template" name="template">
      <UTextarea
        v-model="formState.template"
        placeholder="<TAS_Document>\n  <Header>\n    <DocumentID>${DocumentID}</DocumentID>\n    ..."
        block
      />
    </UFormField>
    <USwitch v-model="formState.crlf" :label="formState.crlf ? 'Using CRLF in the export template' : 'Using LF in the export template'" />

    <UButton type="submit" size="lg" color="neutral" block class="mt-4">
      Save export profile
    </UButton>

    <div class="w-full flex justify-center">
      <UButton type="button" size="lg" color="neutral" variant="link" @click="$emit('previousStep')">
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
