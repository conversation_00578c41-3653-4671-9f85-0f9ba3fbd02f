<script setup lang="ts">
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { computed, ref } from 'vue'
import { ZodError } from 'zod'
import { useCreateImportProfileMutation, useImportProfileByIdOrFailQuery, useUpdateImportProfileConfigurationMutation, useUpdateImportProfileGeneralInfoMutation, useUpdateImportProfileMappingMutation, useUpdateImportProfileScheduleMutation } from '~/api/import-profiles'
import SteppedHeader from '~/components/general/SteppedHeader.vue'
import ImportProfileStepWrapper from '~/components/import-profiles/ImportProfileStepWrapper.vue'
import { useStepperWizard } from '~/composables/stepped-header/useStepperWizard'
import { importProfileDataToFormSchema } from '~/schemas/import-profiles/import-profile-api-to-form.schema'
import { importProfileQuerySchema, importProfileRouteParamsSchema } from '~/schemas/import-profiles/import-profile-id-route.schema'
import { isImportProfileReady } from '~/utils/import-profiles/import-profile-status.checker'
import { IMPORT_PROFILE_STATUSES } from '~/utils/import-profiles/import-profile-statusses'
import { isValidImportProfileStep } from '~/utils/import-profiles/import-profile-step.checker'
import { IMPORT_PROFILE_STEPS } from '~/utils/import-profiles/import-profile-steps'

definePageMeta({ layout: 'empty' })

const profile = ref<ImportProfileFormData | null>(null)

const importProfileId = ref<string | null>(null)
const isCreateMode = ref<boolean>(false)
const routeError = ref<string | null>(null)

const {
  currentStep,
  currentStepIndex,
  currentStepNumber,
  getHighestPossibleStep,
  highestReachedStepNumber,
  setHighestReachedStep,
  resetSteps,
  goToStep,
  goToNextStep,
  goToPreviousStep,
  totalSteps,
  isLastStep,
  isCurrentStep,
} = useStepperWizard(IMPORT_PROFILE_STEPS)
const hasInitializedStepper = ref(false)

const isDisabledButtonsStep = computed(() => {
  return isCurrentStep(IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING)
})

const route = useRoute()
const router = useRouter()

try {
  const parsedParams = importProfileRouteParamsSchema.parse(route.params)
  const parsedQuery = importProfileQuerySchema.parse(route.query)

  isCreateMode.value = parsedParams.id === 'new'
  if (!isCreateMode.value) {
    importProfileId.value = parsedParams.id
  }
  else {
    profile.value = {
      name: '',
      status: '',
      integration_id: '',
      asset_type_id: '',
      path: '/',
      encoding: null,
      config: null,
      mapping: null,
      schedule: null,
    }

    hasInitializedStepper.value = true
  }

  if (isValidImportProfileStep(parsedQuery.step)) {
    goToStep(parsedQuery.step)
  }
}
catch (error) {
  if (error instanceof ZodError) {
    routeError.value = error.issues.map(issue => issue.message).join(', ')
  }
  else {
    routeError.value = 'Unexpected route error'
  }
}

const {
  data: fetchedProfile,
  isError: isProfileError,
  error: profileError,
} = useImportProfileByIdOrFailQuery(importProfileId.value!, {
  enabled: !isCreateMode.value && !!importProfileId.value,
})

watchEffect(() => {
  if (fetchedProfile.value) {
    profile.value = importProfileDataToFormSchema.parse(fetchedProfile.value)

    if (hasInitializedStepper.value) {
      return
    }

    const step = fetchedProfile.value.status === IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING
      ? IMPORT_PROFILE_STATUSES.MAPPING
      : fetchedProfile.value.status

    if (isImportProfileReady(fetchedProfile.value)) {
      setHighestReachedStep(getHighestPossibleStep())
    }
    else if (isValidImportProfileStep(step)) {
      setHighestReachedStep(step)
      hasInitializedStepper.value = true
    }
    else {
      resetSteps()
    }
  }
})

function handleCancel() {
  router.push('/admin/import-profiles')
}

const createImportProfileMutation = useCreateImportProfileMutation()

const updateImportProfileGeneralInfoMutation = useUpdateImportProfileGeneralInfoMutation()
const updateImportProfileConfigurationMutation = useUpdateImportProfileConfigurationMutation()
const updateImportProfileMappingMutation = useUpdateImportProfileMappingMutation()
const updateImportProfileScheduleMutation = useUpdateImportProfileScheduleMutation()

async function updateImportProfile(data: ImportProfileFormData) {
  switch (currentStep.value) {
    case IMPORT_PROFILE_STATUSES.BASIC_INFORMATION:
      return updateImportProfileGeneralInfoMutation.mutateAsync({
        importProfileId: importProfileId.value!,
        data,
      })
    case IMPORT_PROFILE_STATUSES.INCOMPLETE:
      return updateImportProfileConfigurationMutation.mutateAsync({
        importProfileId: importProfileId.value!,
        data,
      })
    case IMPORT_PROFILE_STATUSES.MAPPING:
      return updateImportProfileMappingMutation.mutateAsync({
        importProfileId: importProfileId.value!,
        data,
      })
    case IMPORT_PROFILE_STATUSES.SCHEDULING:
      return updateImportProfileScheduleMutation.mutateAsync({
        importProfileId: importProfileId.value!,
        data,
      })
    default:
      throw new Error('Invalid step for updating import profile')
  }
}

async function submitForm(data: ImportProfileFormData, isSaveAndBackToOverview: boolean = false) {
  try {
    if (isCreateMode.value) {
      const createdProfile = await createImportProfileMutation.mutateAsync(data)

      if (isSaveAndBackToOverview) {
        useToast().add({
          title: 'Import profile created successfully',
          description: 'Your import profile has been created.',
          color: 'success',
        })
        return router.push('/admin/import-profiles')
      }

      return router.push({
        path: `/admin/import-profiles/${createdProfile.id}`,
        query: { step: createdProfile.status },
      })
    }
    else {
      if (!importProfileId.value) {
        throw new Error('Import profile ID is required for update')
      }

      const updatedProfile = await updateImportProfile(data)

      profile.value = importProfileDataToFormSchema.parse(updatedProfile)

      if (isValidImportProfileStep(updatedProfile.status)) {
        router.replace({
          path: route.path,
          query: {
            ...route.query,
            step: updatedProfile.status,
          },
        })
      }
    }

    if (isLastStep.value || isSaveAndBackToOverview) {
      useToast().add({
        title: 'Import profile saved successfully',
        description: 'Your import profile has been saved.',
        color: 'success',
      })

      return router.push('/admin/import-profiles')
    }

    goToNextStep()
  }
  catch (error: any) {
    const toast = useToast()
    const message
    = error?.data?.message
      || error?.message
      || 'An unexpected error occurred'

    toast.add({
      title: 'Error saving import profile',
      description: message,
      color: 'error',
    })
  }
}

function handlePreviousStep() {
  if (currentStepIndex.value > 0) {
    goToPreviousStep()
  }
  else {
    handleCancel()
  }
}

const containerMaxWidthClass = computed(() =>
  currentStep.value === IMPORT_PROFILE_STATUSES.MAPPING
    ? 'max-w-5xl'
    : 'max-w-3xl',
)
</script>

<template>
  <div class="min-h-screen bg-neutral-50 flex flex-col">
    <SteppedHeader
      title="Configure import profile"
      :show-stepper="true"
      :current-step="currentStepNumber"
      :highest-reached-step="highestReachedStepNumber"
      :amount-of-steps="totalSteps"
      :disabled-steps="isDisabledButtonsStep"
      @change-step="goToStep"
      @cancel="handleCancel"
    />

    <div v-if="routeError || isProfileError" class="p-8 text-red-500 text-center">
      {{ routeError || profileError?.message }}
      <div class="mt-4">
        <UButton color="error" variant="solid" @click="handleCancel">
          Back to Profiles
        </UButton>
      </div>
    </div>

    <template v-else-if="profile">
      <UContainer class="hidden md:block md:py-16" :class="[containerMaxWidthClass]">
        <UCard class="px-6 py-4 shadow-md">
          <ImportProfileStepWrapper
            :id="importProfileId"
            :step="currentStep"
            :profile="profile"
            :is-create-mode="isCreateMode"
            @previous-step="handlePreviousStep"
            @submit="submitForm"
          />
        </UCard>
      </UContainer>

      <UCard class="md:hidden px-6 py-4 bg-white flex-1">
        <ImportProfileStepWrapper
          :id="importProfileId"
          :step="currentStep"
          :profile="profile"
          :is-create-mode="isCreateMode"
          @previous-step="handlePreviousStep"
          @submit="submitForm"
        />
      </UCard>
    </template>
  </div>
</template>
