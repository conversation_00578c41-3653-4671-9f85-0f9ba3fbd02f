import type { TenantData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useTenantStore = defineStore('tenant', () => {
  const tenant = ref<TenantData>()

  async function hydrate() {
    const response = await useApiClient()<ApiResponse<TenantData>>('api/tenant/current')
    tenant.value = response.data
  }

  return {
    tenant,
    hydrate,
  }
})
