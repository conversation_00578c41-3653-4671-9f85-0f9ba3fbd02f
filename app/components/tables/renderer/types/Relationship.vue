<script setup lang="ts">
import { useAssetTypeFieldByIdOrFailQuery } from '~/api/asset-type-fields'

// TODO: Fix the any prop types
import { AssetQueryFactory } from '~/api/assets'

const props = defineProps({
  rowData: {
    type: Object as PropType<any>,
    required: true,
  },
  tableField: {
    type: Object as PropType<any>,
    required: true,
  },
})

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const assetTypeStore = useAssetTypeStore()

const relationship = assetTypeRelationshipStore.getById(props.tableField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')

const { data: assetTypeField } = useAssetTypeFieldByIdOrFailQuery(
  assetType.display_field_id!,
  {
    enabled: !!assetType?.display_field_id,
  },
)

const displayField = computed(() => {
  return assetTypeField.value?.field ?? ''
})

const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(assetType.table_name)
  .getQueries()

const id = props.rowData[relationship?.relationship_key ?? '']

const { data: assetData, isFetching: isFetchingAsset, refetch: refetchAsset } = useAssetQuery(
  id!,
  {
    enabled: !!id,
  },
)

watch(() => props.rowData, () => {
  refetchAsset()
})
</script>

<template>
  <span v-if="!isFetchingAsset">
    {{ assetData[displayField] ?? 'n/a' }}
  </span>
</template>
