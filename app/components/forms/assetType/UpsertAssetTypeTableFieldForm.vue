<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeRelationshipData, UpsertAssetTypeTableFieldData } from '~/utils/types/api/api'
import { useCloneDeep } from '#imports'
import { useAssetTypeFieldsByAssetTypeQuery, useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import BadgeConfigField from '~/components/forms/inputs/BadgeConfigField.vue'
import { useFieldTemplate } from '~/composables/form/useFieldTemplate'
import { AssetTypeTableFieldType } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
  assetTypeTableField: {
    type: Object as PropType<UpsertAssetTypeTableFieldData>,
    required: false,
    default: null,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const assetTypeTableFieldStore = useAssetTypeTableFieldStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()

const isPending = ref(false)
const data = ref<UpsertAssetTypeTableFieldData>(
  useCloneDeep(props.assetTypeTableField) || {
    asset_type_field_id: '',
    asset_type_relationship_id: null,
    type: AssetTypeTableFieldType.Text,
    label: '',
    locked: false,
    weight: 1,
    badge_config: null,
    filterable: false,
    suffix: '',
  },
)
const selectedFormFieldRelatable = ref<string>('Field')

const relationships = ref<AssetTypeRelationshipData[]>(
  assetTypeRelationshipStore.getByAssetTypeId(props.assetTypeId),
)

const selectedRelationship = computed(() => {
  return relationships.value.find(x => x.id === data.value.asset_type_relationship_id)
})

const allowsTemplate = computed(() => {
  return data.value.type === AssetTypeTableFieldType.AdvancedMultilineSelect
})

onMounted(() => {
  if (props.assetTypeTableField) {
    if (props.assetTypeTableField.asset_type_relationship_id) {
      selectedFormFieldRelatable.value = 'Relationship'
    }
    else {
      selectedFormFieldRelatable.value = 'Field'
    }
  }
})

const isEditing = computed(() => !!props.assetTypeTableField)

const selectedFormFieldRelatableComputable = computed({
  get: () => selectedFormFieldRelatable.value,
  set: (value: string) => {
    selectedFormFieldRelatable.value = value
    resetRelationshipFields()
  },
})

const assetTypeFieldsQuery = useAssetTypeFieldsByAssetTypeQuery(props.assetTypeId)

const { data: assetTypeFields } = useAssetTypeFieldsQuery()
const definedAssetTypeFields = computed(() => {
  return assetTypeFields.value || []
})

const fieldOptions = computed(() => {
  const fields = selectedFormFieldRelatable.value === 'Field'
    ? assetTypeFieldsQuery.data.value || []
    : (() => {
        if (!data.value.asset_type_relationship_id)
          return []
        const relationship = assetTypeRelationshipStore.getById(data.value.asset_type_relationship_id)
        if (!relationship?.relationship_asset_type_id)
          return []

        const relationshipFieldsQuery = definedAssetTypeFields.value
          .filter(field => field.asset_type_id === relationship.relationship_asset_type_id)
        return relationshipFieldsQuery || []
      })()

  return fields.map(field => ({
    id: field.id,
    label: field.label,
    type: 'item' as const,
  }))
})

watch(() => data.value?.asset_type_field_id, (newValue, oldValue) => {
  const oldFieldName = fieldOptions.value.find(field => field.id === oldValue)?.label
  if (data.value.label.length > 0 && oldFieldName !== data.value.label) {
    return
  }
  const fieldName = fieldOptions.value.find(field => field.id === newValue)?.label
  data.value.label = fieldName ?? data.value.label
})

function resetRelationshipFields() {
  if (selectedFormFieldRelatable.value === 'Field') {
    data.value.asset_type_relationship_id = null
  }
  else {
    data.value.asset_type_field_id = ''
  }
}

async function onSubmit() {
  try {
    isPending.value = true
    if (isEditing.value && data.value.id) {
      await assetTypeTableFieldStore.update(props.assetTypeId, data.value.id, data.value)
    }
    else {
      await assetTypeTableFieldStore.create(props.assetTypeId, data.value)
    }
    emit('close', false)
    toast.add({ title: `${isEditing.value ? 'Updated' : 'Created'} master data table field`, color: 'success' })
  }
  catch (e) {
    toast.add({
      title: `Error ${isEditing.value ? 'updating' : 'creating'} master data table field`,
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}

const relationshipOptions = computed(() =>
  assetTypeRelationshipStore
    .getByAssetTypeId(props.assetTypeId)
    .map(relationship => ({
      id: relationship.id,
      label: relationship.label,
      type: 'item' as const,
    })),
)

const assetTypeRelationshipIdModel = computed({
  get: () => data.value.asset_type_relationship_id ?? '',
  set: (val: string) => {
    data.value.asset_type_relationship_id = val || null
  },
})

const { selectedTemplateField, getTemplate, setTemplate, addFieldToTemplate } = useFieldTemplate(
  data,
  allAssetTypeFields,
)

function getFieldsByAssetTypeId(assetTypeId: string) {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}
</script>

<template>
  <USlideover :title="isEditing ? 'Edit table field' : 'Add table field'" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Select relatable type" class="col-span-12">
            <URadioGroup
              v-model="selectedFormFieldRelatableComputable"
              orientation="horizontal"
              :items="['Field', 'Relationship']"
            />
          </UFormField>

          <UFormField
            v-if="selectedFormFieldRelatable === 'Relationship'"
            label="Select relationship"
            class="col-span-12"
          >
            <USelect
              v-model="assetTypeRelationshipIdModel"
              :items="relationshipOptions"
              label-key="label"
              value-key="id"
              placeholder="Select relationship"
              class="w-full"
            />
          </UFormField>

          <UFormField label="Select field" class="col-span-12">
            <USelect
              v-model="data.asset_type_field_id"
              :items="fieldOptions"
              label-key="label"
              value-key="id"
              placeholder="Select field"
              class="w-full"
            />
          </UFormField>

          <UFormField label="Select type" class="col-span-12">
            <USelect
              v-model="data.type"
              :items="Object.values(AssetTypeTableFieldType)"
              placeholder="Select type"
              class="w-full"
            />
          </UFormField>

          <div v-if="allowsTemplate && selectedRelationship" class="w-full gap-4 col-span-12">
            <hr class="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700">
            <UFormField
              label="Add field to template"
              class="col-span-12"
            >
              <USelect
                v-model="selectedTemplateField"
                :items="getFieldsByAssetTypeId(selectedRelationship?.relationship_asset_type_id ?? '') as any"
                :label-key="'label' as any"
                :value-key="'field' as any"
                placeholder="Select field"
                class="w-full"
                @change="addFieldToTemplate"
              />
            </UFormField>

            <UFormField
              label="Template"
              class="w-full"
            >
              <UTextarea :model-value="getTemplate()" @update:model-value="setTemplate" />
            </UFormField>
            <hr class="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700">
          </div>

          <UFormField label="Suffix" class="col-span-12">
            <UInput v-model="data.suffix" placeholder="e.g. %, kg, m², etc." />
          </UFormField>

          <UFormField label="Label" class="col-span-12">
            <UInput v-model="data.label" />
          </UFormField>

          <UFormField label="Weight" class="col-span-6">
            <UInput v-model="data.weight" type="number" min="1" max="10" />
          </UFormField>

          <UFormField label="Locked" class="col-span-3">
            <USwitch v-model="data.locked" />
          </UFormField>

          <UFormField
            label="Filterable"
            class="col-span-3"
          >
            <USwitch v-model="data.filterable" />
          </UFormField>

          <div v-if="data.type === AssetTypeTableFieldType.Badge" class="col-span-12 flex flex-col gap-4 w-full">
            <BadgeConfigField v-model="data.badge_config" />
          </div>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          {{ isEditing ? 'Update' : 'Create' }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
