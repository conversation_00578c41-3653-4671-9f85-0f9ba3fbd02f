import { createQueryKeys } from '@lukemorales/query-key-factory'
import { ApiClientService } from '../services/api-client.service'

export const apiClientKeys = createQueryKeys('apiClient', {
  all: {
    queryKey: null,
    queryFn: () => ApiClientService.getAll(),
  },
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => ApiClientService.getById(id),
  }),
  byIdOrFail: (id: string) => ({
    queryKey: [id],
    queryFn: () => ApiClientService.getByIdOrFail(id),
  }),
})
