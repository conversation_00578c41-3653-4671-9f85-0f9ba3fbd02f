<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeIndexData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()

const assetTypeIndexStore = useAssetTypeIndexStore()

const columns: BaseColumn<AssetTypeIndexData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('name', 'Name'),
  new TextColumn('type', 'Type'),
  new ActionColumn('Actions', [
    { label: 'Delete', onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)

async function onDelete(assetTypeIndex: AssetTypeIndexData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeIndexStore.destroy(props.assetTypeId, assetTypeIndex.id as string)
    toast.add({ title: 'Master data index deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data field index',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeIndexStore.getByAssetTypeId(assetTypeId)"
  />
</template>
