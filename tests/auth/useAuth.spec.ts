import { mockNuxtImport } from '@nuxt/test-utils/runtime'
import pkceChallenge from 'pkce-challenge'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useAuth } from '~/composables/auth/useAuth'
import type { TokenResponse } from '~/schemas/auth/token-response.schema'
import { mockServer } from '../setup/test-setup'
import { buildTokenResponse } from './token-response.builder'

interface RouteMockQuery {
  code: string | undefined
}

const { mockUseRoute } = vi.hoisted(() => ({
  mockUseRoute: vi.fn(() => ({
    query: { code: 'valid-auth-code' } as RouteMockQuery,
  })),
}))
mockNuxtImport('useRoute', () => mockUseRoute)

export function setMockRoute(query: RouteMockQuery) {
  mockUseRoute.mockReturnValue({ query })
}

// Mock pkceChallenge
vi.mock('pkce-challenge', () => ({
  default: vi.fn(() => Promise.resolve({
    code_challenge: 'test_challenge',
    code_verifier: 'test_verifier',
  })),
}))

// Mock window.location
vi.stubGlobal('window', Object.create(window, {
  location: {
    value: { href: '', assign: vi.fn((url) => { window.location.href = url }) },
    writable: true,
  },
}))

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('sessionStorage', sessionStorageMock)

function useTestAuth(overrides: Partial<Parameters<typeof useAuth>[0]> = {}) {
  return useAuth({
    getRedirectUri: () => 'https://test.app/callback',
    getClientId: () => 'test-client-id',
    getApiBaseUrl: () => 'https://test.app',
    onTokenReceived: vi.fn(),
    fetchToken: async (code, verifier): Promise<TokenResponse> => {
      // Use real $fetch that will be intercepted by MSW
      const response = await $fetch<TokenResponse>('/oauth/token', {
        method: 'POST',
        baseURL: 'https://test.app',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code,
          code_verifier: verifier,
          redirect_uri: 'https://test.app/callback',
          client_id: 'test-client-id',
        }).toString(),
      })
      return response
    },
    ...overrides,
  })
}

describe('useAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    sessionStorageMock.clear()
    sessionStorageMock.getItem.mockReturnValue(null)

    setMockRoute({ code: 'valid-auth-code' })
  })

  describe('login success', () => {
    it('generates PKCE challenge, stores verifier, and redirects to OAuth URL', async () => {
      const { login } = useTestAuth()

      await login()

      expect(pkceChallenge).toHaveBeenCalled()
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('pkce_verifier', 'test_verifier')
      expect(window.location.href).toContain('https://test.app/oauth/authorize')
      expect(window.location.href).toContain('code_challenge=test_challenge')
      expect(window.location.href).toContain('client_id=test-client-id')
      expect(window.location.href).toContain('redirect_uri=https%3A%2F%2Ftest.app%2Fcallback')
    })

    it('sets correct OAuth parameters in redirect URL', async () => {
      const { login } = useTestAuth()

      await login()

      const url = new URL(window.location.href)
      expect(url.searchParams.get('response_type')).toBe('code')
      expect(url.searchParams.get('code_challenge_method')).toBe('S256')
      expect(url.searchParams.get('prompt')).toBe('login')
    })
  })

  describe('error cases', () => {
    it('throws error when authorization code is missing', async () => {
      setMockRoute({ code: undefined })

      const { handleRedirectCallback } = useTestAuth()

      await expect(handleRedirectCallback()).rejects.toThrow('Missing authorization code')
    })

    it('throws error when PKCE verifier is missing from sessionStorage', async () => {
      sessionStorageMock.getItem.mockReturnValue(null)

      const { handleRedirectCallback } = useTestAuth()

      await expect(handleRedirectCallback()).rejects.toThrow('Missing PKCE verifier')
    })

    it('handles invalid authorization code via MSW', async () => {
      setMockRoute({ code: 'invalid-auth-code' })
      sessionStorageMock.getItem.mockReturnValue('test_verifier')

      const { handleRedirectCallback } = useTestAuth()

      await expect(handleRedirectCallback()).rejects.toThrow()
    })
  })

  describe('token exchange', () => {
    it('exchanges code for token via MSW and calls onTokenReceived', async () => {
      sessionStorageMock.getItem.mockReturnValue('test_verifier')
      const onTokenReceivedSpy = vi.fn()

      const { handleRedirectCallback } = useTestAuth({
        onTokenReceived: onTokenReceivedSpy,
      })

      await handleRedirectCallback()

      expect(onTokenReceivedSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          token_type: 'Bearer',
          access_token: expect.any(String),
          refresh_token: expect.any(String),
          expires_in: expect.any(Number),
        }),
      )
    })
  })
})