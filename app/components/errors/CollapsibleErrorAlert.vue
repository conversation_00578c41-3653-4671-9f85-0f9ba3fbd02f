<script setup lang="ts">
interface Props {
  errors?: Record<string, string | string[]>
  title?: string
  color?: 'error' | 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'neutral' | undefined
  variant?: 'subtle' | 'solid' | 'outline' | 'soft' | undefined
  alertClass?: string
  collapseThreshold?: number
  expandText?: string
  collapseText?: string
  fieldNameFormatter?: (fieldName: string) => string
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  title: 'Please fix the following errors:',
  color: 'error',
  variant: 'subtle',
  alertClass: 'mb-4',
  collapseThreshold: 10,
  expandText: 'Show All',
  collapseText: 'Show Less',
})

const isExpanded = ref(false)

const hasErrors = computed(() => {
  return Object.keys(props.errors).length > 0
})

const totalErrorCount = computed(() => {
  return Object.keys(props.errors).length
})

const shouldShowToggle = computed(() => {
  return totalErrorCount.value > props.collapseThreshold
})

const displayedErrors = computed(() => {
  if (isExpanded.value || !shouldShowToggle.value) {
    return props.errors
  }

  const errorEntries = Object.entries(props.errors)
  const limitedEntries = errorEntries.slice(0, props.collapseThreshold)
  return Object.fromEntries(limitedEntries)
})

const hasHiddenErrors = computed(() => {
  return shouldShowToggle.value && !isExpanded.value
})

const hiddenErrorsText = computed(() => {
  const hiddenCount = totalErrorCount.value - props.collapseThreshold
  return `... and ${hiddenCount} more error${hiddenCount === 1 ? '' : 's'}`
})

function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

function formatFieldName(fieldName: string): string {
  return props.fieldNameFormatter
    ? props.fieldNameFormatter(fieldName)
    : fieldName
}

watch(() => props.errors, () => {
  isExpanded.value = false
}, { deep: true })
</script>

<template>
  <UAlert
    v-if="hasErrors"
    :color="color"
    :variant="variant"
    :class="alertClass"
  >
    <template #title>
      <div class="flex items-center justify-between">
        <span>{{ title }}</span>
        <UButton
          v-if="shouldShowToggle"
          variant="link"
          size="xs"
          class="ml-2"
          @click="toggleExpanded"
        >
          <UIcon
            :name="isExpanded ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
            class="ml-1 w-4 h-4"
          />
          {{ isExpanded ? collapseText : `${expandText} (${totalErrorCount})` }}
        </UButton>
      </div>
    </template>

    <template #description>
      <ul class="list-disc list-inside space-y-1 mt-2">
        <li
          v-for="(fieldErrors, fieldName) in displayedErrors"
          :key="fieldName"
        >
          <strong>{{ formatFieldName(fieldName) }}:</strong>
          <template v-if="Array.isArray(fieldErrors)">
            {{ fieldErrors.join(', ') }}
          </template>
          <template v-else>
            {{ fieldErrors }}
          </template>
        </li>
      </ul>

      <div
        v-if="!isExpanded && hasHiddenErrors"
        class="mt-2 text-sm opacity-75"
      >
        {{ hiddenErrorsText }}
      </div>
    </template>
  </UAlert>
</template>
