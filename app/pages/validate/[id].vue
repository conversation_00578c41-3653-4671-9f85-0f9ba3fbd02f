<script setup lang="ts">
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import { ModalsJsonDataModal } from '#components'
import { cloneDeep } from 'lodash'
import { FetchError } from 'ofetch'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useAssetTypeFormFieldsByAssetTypeQuery } from '~/api/asset-type-form-fields/queries/useAssetTypeFormFieldQueries'
import { AssetQueryFactory } from '~/api/assets'
import CollapsibleErrorAlert from '~/components/errors/CollapsibleErrorAlert.vue'
import OrderErrorReasonAlert from '~/components/errors/OrderErrorReasonAlert.vue'
import Badge from '~/components/tables/renderer/types/Badge.vue'
import { useIsHyperfoxUser } from '~/composables/hyperfox/useIsHyperfoxUser'
import { FormGenerator } from '~/utils/forms/FormGenerator'
import { BadgeConfigMode } from '~/utils/types/api/api'
import { ValidateActionCheck, ValidateActionReject, ValidateActionSave, ValidateActionValidate } from '~/utils/validate/validate-action'
import { ValidateActionExecutor } from '~/utils/validate/validate-action.executor'

definePageMeta({
  middleware: ['auth'],
})

// Component setup
const route = useRoute()
const overlay = useOverlay()

const settingsStore = useSettingsStore()
const assetTypeStore = useAssetTypeStore()
const orderInquiryStore = useOrderInquiryStore()

// Component initialize
const order = ref()
provide('order', order)

const executor = ref()
const orderInquiry = ref()
const loading = ref(true)
const assetId = route.params.id as string
const errors = ref<Record<string, string[]>>({})
const aiResponseModal = overlay.create(ModalsJsonDataModal)
const badgeThresholds = [
  { min: 95, color: 'success' },
  { min: 80, color: 'warning' },
  { min: 0, color: 'error' },
]

const { isHyperfoxUser } = useIsHyperfoxUser()

const defaultModel = settingsStore.settings?.defaultModel ?? ''

const { useAssetActionMutation } = AssetQueryFactory
  .getQueryInstance(defaultModel)
  .getMutations()

const assetActionMutation = useAssetActionMutation()

executor.value = new ValidateActionExecutor(assetActionMutation.mutateAsync)

const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(defaultModel)
  .getQueries()

const assetType = assetTypeStore.getByTableName(defaultModel)

const { data: formFields } = useAssetTypeFormFieldsByAssetTypeQuery(assetType.id)
const { data: assetTypeFields } = useAssetTypeFieldsQuery()

const formDefinition = computed<FormDefinition>(() => {
  return new FormGenerator(assetTypeFields.value ?? []).generateFormDefinition(formFields.value ?? [])
})

const { data: assetData } = useAssetQuery(
  assetId,
  { include: settingsStore.environment?.includedRelationships },
)

watch(assetData, async (newValue) => {
  if (!newValue) {
    return
  }
  order.value = cloneDeep(newValue)

  orderInquiry.value = await orderInquiryStore.fetchById(order.value.order_inquiry_id)

  await onCheck()

  loading.value = false
}, { immediate: true })

function showAIResponse() {
  aiResponseModal.open({
    title: 'AI Response',
    json: orderInquiry.value?.data.aiResponse ?? '{}',
  })
}

async function onReject() {
  await executor.value.execute(assetId, new ValidateActionReject())
  await navigateTo('/validate')
}

async function onCheck() {
  try {
    await executor.value.execute(assetId, new ValidateActionCheck(order.value), false, false)
  }
  catch (e: any) {
    if (e instanceof FetchError) {
      errors.value = e.data.errors
    }
  }
}

async function onSave() {
  await executor.value.execute(assetId, new ValidateActionSave(order.value))
}

async function onApprove() {
  try {
    if (!await executor.value.execute(assetId, new ValidateActionSave(order.value), false)) {
      return
    }
    if (!await executor.value.execute(assetId, new ValidateActionValidate(order.value))) {
      return
    }

    await navigateTo('/validate')
  }
  catch (e: any) {
    if (e instanceof FetchError) {
      errors.value = e.data.errors
    }
  }
}

function onBack() {
  navigateTo('/validate')
}

function formatFieldName(fieldName: string): string {
  return fieldName
    .replace(/\./g, ' → ')
    .replace(/(\d+)/g, '#$1')
    .split(/[_ →]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}
</script>

<template>
  <div v-if="!loading" class="flex flex-col overflow-hidden h-full">
    <UDashboardToolbar>
      <template #left>
        <Badge
          v-if="order.accuracy"
          :value="order.accuracy"
          :mode="BadgeConfigMode.Threshold"
          :thresholds="badgeThresholds"
          :statuses="[]"
          suffix="%"
          size="lg"
        />
        <UNavigationMenu
          :items="[
            { label: 'Order', value: 'order', active: true },
          ]"
          highlight
        />
      </template>
      <template #right>
        <UButton v-if="isHyperfoxUser" class="mr-2" variant="outline" color="secondary" @click="showAIResponse">
          AI Response
        </UButton>
        <UButton class="mr-2" variant="outline" color="error" @click="onReject">
          Reject
        </UButton>
        <UButton class="mr-2" variant="outline" @click="onSave">
          Save
        </UButton>
        <UButton color="success" @click="onApprove">
          Save and approve
        </UButton>
        <UButton variant="link" @click="onBack">
          <UIcon name="i-ph-x" class="text-neutral-400" />
        </UButton>
      </template>
    </UDashboardToolbar>
    <div class="grid grid-cols-2 mx-8 h-full">
      <div class="col-span-1 bg-neutral-50 p-4">
        <OrdersOrderInquiry :order-inquiry="orderInquiry.data" />
      </div>
      <div class="col-span-1 p-4">
        <OrderErrorReasonAlert
          :reason="order.error_reason"
        />
        <CollapsibleErrorAlert
          :errors="errors"
          :field-name-formatter="formatFieldName"
        />
        <FormsRendererForm
          v-if="formDefinition"
          v-model="order"
          :form-definition="formDefinition"
          submit-label="Save"
          :errors="errors"
          :is-sub-form="true"
          error-display="border"
          class="hide-form-errors"
        />
        <UCard v-else>
          <p>No form fields defined for this master data.</p>
        </UCard>
      </div>
    </div>
  </div>
</template>
