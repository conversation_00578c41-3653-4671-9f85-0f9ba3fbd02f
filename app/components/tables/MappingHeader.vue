<script setup lang="ts">
import type { MappingItemData } from '~/utils/types/api/api'
import { USelect } from '#components'
import { computed } from 'vue'

const props = defineProps<{
  headers: { id: string, label: string }[]
  mappingMeta: MappingItemData[]
  mappingOptions: { label: string, value: string }[]
}>()

const emit = defineEmits<{
  (e: 'updateMapping', payload: {
    from: string
    to: string
    skip: boolean
    mapping: any | null
  }): void
}>()

const usedToValues = computed(() =>
  new Set(
    props.mappingMeta
      .filter(m => !m.skip && m.to)
      .map(m => m.to),
  ),
)

function getCurrentValue(from: string): string | undefined {
  const mapping = props.mappingMeta.find(m => m.from === from)
  if (!mapping)
    return ''
  return mapping.skip ? 'do_not_import' : mapping.to || undefined
}

function mappedOptions(from: string) {
  return [
    { label: 'Do not import', value: 'do_not_import' },
    ...props.mappingOptions.map(opt => ({
      ...opt,
      disabled:
        usedToValues.value.has(opt.value) && getCurrentValue(from) !== opt.value,
    })),
  ]
}

function updateMapping(from: string, newTo: string) {
  const skip = newTo === 'do_not_import'
  emit('updateMapping', {
    from,
    to: skip ? '' : newTo,
    skip,
    mapping: null,
  })
}
</script>

<template>
  <thead>
    <tr>
      <th
        v-for="header in headers"
        :key="`header-${header.id}`"
        class="text-left font-semibold px-1 py-2 w-[160px] truncate"
      >
        {{ header.label }}
      </th>
    </tr>
    <tr class="bg-neutral-100">
      <th
        v-for="header in headers"
        :key="`mapper-${header.id}`"
        class="text-left px-1 py-1 w-[160px] relative"
      >
        <div class="w-full min-w-0 overflow-hidden">
          <USelect
            :model-value="getCurrentValue(header.id)"
            :items="mappedOptions(header.id)"
            :popper="{
              strategy: 'fixed',
              placement: 'bottom-start',
              modifiers: [
                {
                  name: 'preventOverflow',
                  options: {
                    boundary: 'viewport',
                    padding: 8,
                  },
                },
                {
                  name: 'flip',
                  options: {
                    fallbackPlacements: ['bottom-end', 'top-start', 'top-end'],
                  },
                },
              ],
            }"
            option-attribute="value"
            placeholder="Map to..."
            size="sm"
            class="w-full max-w-full min-w-0"
            @update:model-value="(value) => updateMapping(header.id, value)"
          />
        </div>
      </th>
    </tr>
  </thead>
</template>
