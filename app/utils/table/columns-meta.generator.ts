import type { AssetTypeTableFieldData } from '../types/api/api'
import type { ColumnMeta } from '../types/table/column-meta'
import { AssetTypeFieldCacheService } from '~/api/asset-type-fields/services/asset-type-field-cache.service'
import { ColumnType } from '../types/table/ColumnType'
import { getCellType } from './get-cell-type'

function getIdAndKey(fieldData: AssetTypeTableFieldData): { id: string, key: string } {
  const { asset_type_field_id, asset_type_relationship_id } = fieldData

  if (asset_type_relationship_id) {
    const relationship = useAssetTypeRelationshipStore().getById(asset_type_relationship_id)

    return {
      id: relationship.relationship_key,
      key: relationship.relationship_key,
    }
  }

  const field = AssetTypeFieldCacheService.getByIdOrFail(asset_type_field_id)
  return {
    id: field.field,
    key: field.field,
  }
}

export function generateColumnsMetaFromFields(fields: AssetTypeTableFieldData[]): ColumnMeta[] {
  return fields.map((field) => {
    const type = getCellType(field)
    const { id, key } = getIdAndKey(field)

    const base: ColumnMeta = {
      id,
      key,
      label: field.label,
      type,
      editable: true,
      sortable: true,
      config: {
        suffix: field.suffix,
        filterable: field.filterable,
        type,
        field: {
          template: field.template,
        },
      },
      weight: field.weight,
    }

    if (field.type === 'badge' && field.badge_config?.statuses) {
      const statuses = field.badge_config.statuses || []

      base.config = {
        ...base.config,
        mode: 'status',
        statuses: statuses.reduce((map: Record<string, string>, b) => {
          map[b.name] = b.color
          return map
        }, {}),
      }
    }

    if (field.asset_type_relationship_id) {
      base.config = {
        ...(base.config ?? {}),
        relationship: {
          asset_type_relationship_id: field.asset_type_relationship_id,
          asset_type_field_id: field.asset_type_field_id,
          display_field: AssetTypeFieldCacheService.getByIdOrFail(field.asset_type_field_id).field,
        },
      }
    }

    return base
  })
}

export function generateColumnsMetaFromRows(rows: Record<string, any>[]): ColumnMeta[] {
  const allKeys = new Set<string>()

  for (const row of rows) {
    for (const key of Object.keys(row)) {
      allKeys.add(key)
    }
  }

  return Array.from(allKeys).map((key): ColumnMeta => ({
    id: key,
    key,
    label: key,
    type: ColumnType.Text,
    editable: false,
    sortable: false,
    config: {
      type: ColumnType.Text,
    },
    weight: 1,
  }))
}

export function generateActionsColumn(
  actions: { label: string, onClick: (row: Record<string, any>) => void }[],
): ColumnMeta {
  return {
    id: 'actions',
    key: 'actions',
    label: 'Actions',
    type: ColumnType.Actions,
    config: {
      buttons: actions,
    },
    weight: 1,
  }
}

export function generateErrorColumn(
  hasError: (row: any) => boolean,
): ColumnMeta {
  return {
    id: 'error',
    key: 'error',
    label: '',
    type: ColumnType.Error,
    config: {
      hasError,
    },
    weight: 0,
  }
}
