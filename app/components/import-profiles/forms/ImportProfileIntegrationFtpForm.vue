<script setup lang="ts">
import type { ImportProfileFtpFormData } from '~/schemas/import-profiles/import-profile-ftp.schema'
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { useImportProfileFilesQuery } from '~/api/import-profiles/queries/useImportProfileQueries'
import { importProfileFtpSchema } from '~/schemas/import-profiles/import-profile-ftp.schema'
import { EncodingType } from '~/utils/types/api/api'

const props = defineProps<{
  id: string
  profile: ImportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()

const formRef = ref()

const formState = ref<ImportProfileFtpFormData>({
  path: props.profile.path || '',
  encoding: props.profile.encoding || undefined,
})

const fileEncodingItems: Array<{ value: EncodingType | undefined, label: string }> = [
  ...Object.values(EncodingType).map(encoding => ({
    value: encoding,
    label: encoding.charAt(0).toUpperCase() + encoding.slice(1).replace('-', ' '),
  })),
  {
    value: undefined,
    label: 'None',
  },
]

async function handleSubmit(isSaveAndBackToOverview: boolean = false) {
  await formRef.value.submit()

  emit(
    'submit',
    {
      ...props.profile,
      path: formState.value.path,
      encoding: formState.value.encoding,
    },
    isSaveAndBackToOverview,
  )
}
</script>

<template>
  <UForm
    ref="formRef"
    :state="formState"
    :schema="importProfileFtpSchema"
    class="space-y-4"
  >
    <h2 class="text-lg font-bold">
      Select File from FTP
    </h2>

    <UFormField label="Select the file you want to import from:" name="path" required>
      <BaseFileExplorerSelector
        :id="props.id"
        :initial-path="formState.path"
        selection-type="file"
        :query-fn="useImportProfileFilesQuery"
        @update:path="val => formState.path = val"
      />
    </UFormField>

    <UFormField label="Select a file encoding type:" name="encoding">
      <USelect
        v-model="formState.encoding"
        :items="fileEncodingItems"
        placeholder="No encoding"
        class="w-full"
        block
      />
    </UFormField>

    <div class="flex flex-col gap-2 mt-6">
      <UButton
        type="button"
        size="lg"
        color="neutral"
        block
        @click="handleSubmit(false)"
      >
        <p>Next</p>
        <UIcon name="i-lucide-chevron-right" class="w-4 h-4" />
      </UButton>

      <UButton
        type="button"
        size="lg"
        color="neutral"
        variant="link"
        block
        @click="$emit('previousStep')"
      >
        <p class="underline">
          Back to previous step
        </p>
      </UButton>
    </div>
  </UForm>
</template>
