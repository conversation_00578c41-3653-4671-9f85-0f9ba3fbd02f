import type { ApiResponse, Meta, PaginatedApiResponse } from '~/utils/types/api/api-response'
import qs from 'qs'
import { useApiClient } from '~/utils/api/api-client'
import QueryData from '~/utils/query/QueryData'

function createStore<T extends Record<string, any>>(table: string) {
  return defineStore(`${table}`, () => {
    const records: Ref<T[]> = ref([])
    const meta = ref<Meta | null>(null)

    async function hydrate(queryData?: QueryData) {
      queryData = queryData ?? new QueryData()
      const response = await useApiClient()<PaginatedApiResponse<T[]>>(
        `api/dynamic-assets/dyn/${table}/assets?${queryData.toQueryString()}`,
      )
      records.value = Array.isArray(response.data) ? response.data : [response.data]
      meta.value = response.meta
    }

    async function fetch(id: string, query?: object) {
      const querySuffix = query ? `?${qs.stringify(query)}` : ''
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/assets/${id}${querySuffix}`,
      )
    }

    async function create(data: object) {
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/assets`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
      )
    }

    async function update(id: string, data: object) {
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/assets/${id}`,
        {
          method: 'PUT',
          body: JSON.stringify(data),
        },
      )
    }

    async function destroy(id: string) {
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/assets/${id}`,
        {
          method: 'DELETE',
        },
      )
    }

    async function action(id: string, action: string, data?: object) {
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/assets/${id}/action`,
        {
          method: 'POST',
          body: JSON.stringify({
            action,
            data,
          }),
        },
      )
    }

    async function tableData(queryData?: QueryData) {
      queryData = queryData ?? new QueryData()
      return await useApiClient()<PaginatedApiResponse<T[]>>(
        `api/dynamic-assets/dyn/${table}/table?${queryData.toQueryString()}`,
      )
    }

    async function search(queryData?: QueryData) {
      queryData = queryData ?? new QueryData()
      const response = await useApiClient()<PaginatedApiResponse<T[]>>(
        `api/dynamic-assets/dyn/${table}/search?${queryData.toQueryString()}`,
      )
      records.value = response.data
      meta.value = response.meta
    }

    async function truncate() {
      return await useApiClient()<ApiResponse<T>>(
        `api/dynamic-assets/dyn/${table}/truncate`,
        {
          method: 'DELETE',
        },
      )
    }

    return {
      records,
      meta,
      fetch,
      hydrate,
      create,
      update,
      destroy,
      action,
      tableData,
      search,
      truncate,
    }
  })
}

export const useAssetFactoryStore = defineStore('asset-factory-store', () => {
  const stores = ref(new Map<string, any>())

  function get<T extends Record<string, any>>(table: string) {
    if (!stores.value.has(table)) {
      stores.value.set(table, createStore<T>(table))
    }
    return stores.value.get(table) as ReturnType<typeof createStore<T>>
  }

  return { stores, get }
})
