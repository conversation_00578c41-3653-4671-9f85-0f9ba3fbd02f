import type { WebhookConfigurationFormData } from '~/schemas/webhooks/webhook-form-schema'
import type { WebhookData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { webhookKeys } from '../keys/webhook.keys'
import { WebhookService } from '../services/webhook.service'

export function useCreateWebhookMutation() {
  return useMutation({
    mutationFn: (data: WebhookConfigurationFormData) => WebhookService.create(data),
    onSuccess: () => {
      batchInvalidateKeys(queryClient, [
        webhookKeys.all,
      ])
    },
  })
}

export function useUpdateWebhookMutation() {
  return useMutation({
    mutationFn: ({ webhookId, data }: { webhookId: string, data: WebhookConfigurationFormData }) =>
      WebhookService.update(webhookId, data),
    onSuccess: (_, { webhookId }) => {
      batchInvalidateKeys(queryClient, [
        webhookKeys.all,
        webhookKeys.byId(webhookId),
        webhookKeys.byIdOrFail(webhookId),
      ])
    },
  })
}

export function useDeleteWebhookMutation() {
  return useMutation({
    mutationFn: (webhookId: string) => WebhookService.delete(webhookId),
    onSuccess: (_, webhookId) => {
      batchInvalidateKeys(queryClient, [
        webhookKeys.all,
        webhookKeys.byId(webhookId),
        webhookKeys.byIdOrFail(webhookId),
      ])
    },
  })
}

export function useTestWebhookMutation() {
  return useMutation({
    mutationFn: (data: WebhookData) => WebhookService.test(data),
  })
}
