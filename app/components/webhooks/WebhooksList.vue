<script setup lang="ts">
import type { WebhookData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'

defineProps<{
  webhooks: WebhookData[]
}>()

const emit = defineEmits<{
  configureWebhook: [webhook: WebhookData]
  deleteWebhook: [webhookId: string]
}>()

function configureWebhook(webhook: WebhookData) {
  emit('configureWebhook', webhook)
}

async function showDeleteConfirmation(webhook: WebhookData) {
  const confirmed = await useOverlay().create(ModalsConfirmModal).open({
    title: 'Delete Webhook',
    description: `Are you sure you want to delete the webhook "${webhook.name}"? This action cannot be undone.`,
  })

  if (!confirmed)
    return

  emit('deleteWebhook', webhook.id)
}
</script>

<template>
  <div>
    <h2 class="text-lg font-bold mb-4">
      Active webhooks
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="webhook in webhooks"
        :key="webhook.id"
        class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-1">
          <p class="font-bold flex items-center">
            <slot name="webhook-name" :webhook="webhook">
              {{ webhook.name }}
            </slot>
          </p>
        </div>
        <div class="flex items-center">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            label="Configure"
            @click="configureWebhook(webhook)"
          />
          <UButton
            variant="link"
            color="error"
            class="py-2 px-4 text-sm ml-2"
            label="Delete"
            @click="showDeleteConfirmation(webhook)"
          />
        </div>
      </div>
      <p v-if="!webhooks?.length" class="p-4 text-center text-gray-500">
        No configured webhooks found.
      </p>
    </UPageCard>
  </div>
</template>
