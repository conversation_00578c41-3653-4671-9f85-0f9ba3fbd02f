<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { ModalsDynamicCrudFormModal } from '#components'
import { refDebounced } from '@vueuse/shared'
import { computed } from 'vue'
import { useAssetTypeFieldByIdOrFailQuery } from '~/api/asset-type-fields'
import { AssetTypeFieldCacheService } from '~/api/asset-type-fields/services/asset-type-field-cache.service'
import { AssetQueryFactory } from '~/api/assets'
import { useFormFieldError } from '~/composables/form/useFormFieldError'
import QueryData from '~/utils/query/QueryData'

const props = defineProps<FormInputProps>()
const emit = defineEmits<{
  (e: 'update:model-value', value: string): void
}>()

const model = defineModel({
  type: String,
})
const overlay = useOverlay()

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const items = ref<any[]>([])
const selectedItem = ref<any>()
const searchTerm = ref<string>('')
const searchTermDebounced = refDebounced(searchTerm, 300)
const query: Ref<QueryData> = shallowRef(new QueryData())

const relationship = assetTypeRelationshipStore.getById(props.field.extra?.parentField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')
const { data: assetTypeField } = useAssetTypeFieldByIdOrFailQuery(
  assetType.display_field_id!,
  {
    enabled: !!assetType?.display_field_id,
  },
)

const displayField = computed(() => {
  return assetTypeField.value?.field ?? ''
})
const assetQuery = AssetQueryFactory.getQueryInstance(assetType?.table_name)

const upsertModal = overlay.create(ModalsDynamicCrudFormModal, {
  props: {
    title: relationship?.label ?? 'Untitled',
    assetTypeId: assetType?.id ?? '',
  },
}) as ReturnType<typeof overlay.create> & {
  open: (params?: any) => Promise<{ id: string }>
}

const { useAssetSearchQuery, useAssetQuery } = assetQuery.getQueries()

const { data: individualAsset } = useAssetQuery(model.value!, {
  enabled: !!model.value,
})

const hasMenuBeenOpened = ref(false)

const assetSearchQuery = useAssetSearchQuery(
  query,
  {
    enabled: hasMenuBeenOpened,
  },
)

function handleMenuOpen() {
  hasMenuBeenOpened.value = true
}

watch(assetSearchQuery.data, (newValue) => {
  if (!newValue)
    return

  items.value = newValue.data || []
}, { immediate: true })

watch(individualAsset, () => {
  selectedItem.value = unref(individualAsset.value)
}, { immediate: true })

watch(searchTermDebounced, async () => {
  const newQuery = new QueryData()
  newQuery.setParams({ q: searchTermDebounced.value })
  query.value = newQuery
})

function renderTemplate(item: any, singleLine = false) {
  if (!item.id) {
    return 'No data found'
  }

  const template = props.field.extra?.parentField?.template
  if (!template) {
    return 'No template defined'
  }

  let result = template.replace(/\{\{(.*?)\}\}/g, (match, id) => {
    if (!id) {
      return ''
    }

    try {
      const field = AssetTypeFieldCacheService.getByIdOrFail(id.trim())
      return item[field.field] || ''
    }
    catch {
      return ''
    }
  })

  if (singleLine) {
    result = result.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
  }

  return result
}

function isAddResult(obj: unknown): obj is { id: string } {
  return typeof obj === 'object' && obj !== null && 'id' in obj && typeof (obj as any).id === 'string'
}

async function addForm() {
  const addResult = useOverlay().create(ModalsDynamicCrudFormModal).open({
    title: `Add ${assetType.name}`,
    id: undefined,
    assetTypeId: assetType?.id ?? '',
  })

  const result = await addResult.result

  if (!isAddResult(result)) {
    return
  }

  model.value = result.id

  items.value.push(result)
  selectedItem.value = result
}

async function editForm() {
  upsertModal.open({
    title: `Edit ${assetType.name}`,
    id: model.value,
    assetTypeId: assetType?.id ?? '',
  })
}

function handleUpdate(selectedObject: any) {
  selectedItem.value = selectedObject ? { ...toRaw(selectedObject) } : null

  if (selectedObject && selectedObject.id) {
    selectedItem.value = selectedObject
    emit('update:model-value', selectedObject.id)
  }
}
</script>

<template>
  <UFormField :label="label" :error="error">
    <template v-if="crudOperations" #hint>
      <UButton
        variant="link"
        @click="addForm"
      >
        Add {{ assetType?.name }}
      </UButton>
      <UButton
        v-if="selectedItem && 'external_id' in selectedItem && selectedItem.external_id === null"
        variant="link"
        @click="editForm"
      >
        Edit {{ assetType?.name }}
      </UButton>
    </template>
    <USelectMenu
      :key="field.id"
      v-model:search-term="searchTerm"
      :value="selectedItem"
      :loading="assetSearchQuery.isFetching.value"
      :items="items"
      :label-key="displayField"
      ignore-filter
      class="w-full"
      :disabled="isReadOnly"
      @update:model-value="handleUpdate"
      @focus="handleMenuOpen"
    >
      <template #default>
        <div v-if="selectedItem" class="flex flex-col gap-1 items-start">
          <div class="font-bold text-left">
            {{ selectedItem[displayField] }}
          </div>
          <div class="text-sm text-gray-500 pre-wrap text-left">
            {{ renderTemplate(selectedItem, true) }}
          </div>
        </div>
        <div v-else class="flex flex-col gap-1 items-start">
          <div class="font-bold">
            Select item
          </div>
          <div class="text-sm text-gray-500">
            Select an item first
          </div>
        </div>
      </template>
      <template #item-label="{ item }">
        <div class="flex flex-col gap-1 items-start">
          <div class="font-bold text-left">
            {{ item[displayField] }}
          </div>
          <div class="text-sm text-gray-500 pre-wrap text-left">
            {{ renderTemplate(item) }}
          </div>
        </div>
      </template>
    </USelectMenu>
  </UFormField>
</template>

<style>
.pre-wrap {
  white-space: pre-wrap;
}
</style>
