import type { MetricsData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import type { CalenderDateRange } from '~/utils/types/dates/daterange.type'
import { useApiClient } from '~/utils/api/api-client'
import { MetricsPeriod } from '~/utils/types/api/api'
import { DASHBOARD_API_ROUTES } from './dashboard-service.routes'

export class DashboardService {
  public static async getAll(
    period: MetricsPeriod = MetricsPeriod.LAST_7_DAYS,
    dateRange?: CalenderDateRange,
  ): Promise<MetricsData> {
    const response = await useApiClient()<ApiResponse<MetricsData>>(
      DASHBOARD_API_ROUTES.ALL,
      {
        query: {
          period,
          from: period === MetricsPeriod.CUSTOM ? dateRange?.start.toString() : undefined,
          till: period === MetricsPeriod.CUSTOM ? dateRange?.end.toString() : undefined,
        },
      },
    )

    return response.data
  }
}
