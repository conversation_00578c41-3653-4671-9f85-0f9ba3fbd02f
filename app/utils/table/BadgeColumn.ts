import type { ColumnDef } from '@tanstack/vue-table'
import type { BadgeConfigData } from '~/utils/types/api/api'
import { TablesRendererTypesBadge } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'

export class BadgeColumn<T> extends BaseColumnWithField<T> {
  constructor(
    field: keyof T,
    label: string,
    private config: BadgeConfigData,
    sortable: boolean = false,
  ) {
    super(field, label, sortable, ColumnType.Badge)
  }

  override getField(obj: T): string {
    if (!this.field) {
      return ''
    }

    const result = useGet(obj, this.field) ?? ''

    if (result.length === 0) {
      return 'n/a'
    }
    if (typeof result === 'string') {
      return result.replace(/_/g, ' ').replace(/\b\w/g, (c: string) => c.toUpperCase())
    }
    return result.toString()
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesBadge, {
        value: this.getField(row.original),
        mode: this.config.mode,
        thresholds: this.config.thresholds,
        statuses: this.config.statuses,
        suffix: this.config.suffix || null,
      }),
    }
  }
}

const badgeColorValues = [
  'primary',
  'secondary',
  'success',
  'info',
  'warning',
  'error',
  'neutral',
] as const

export type BadgeColor = typeof badgeColorValues[number]
export const validBadgeColors = new Set<BadgeColor>(badgeColorValues)
