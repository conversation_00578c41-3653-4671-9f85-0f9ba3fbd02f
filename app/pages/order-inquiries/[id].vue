<script setup lang="ts">
definePageMeta({
  middleware: ['auth'],
})

// Store imports
const orderInquiryStore = useOrderInquiryStore()

const id = useRoute().params.id as string
const { data, error } = await useAsyncData(`order-inquiry-${id}`, () => orderInquiryStore.fetchById(id))
</script>

<template>
  <UContainer class="h-full">
    <GeneralError v-if="error" />
    <OrdersOrderInquiry v-if="data" :order-inquiry="data.data" />
  </UContainer>
</template>
