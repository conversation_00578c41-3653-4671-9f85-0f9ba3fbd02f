import type { SettingsData, UpdateSettingsData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { SettingsRoutes } from './settings-service.routes'

export class SettingsService {
  static async getSettings(): Promise<SettingsData> {
    const response = await useApiClient()<ApiResponse<SettingsData>>(
      SettingsRoutes.GET(),
    )

    return response.data
  }

  static async updateSettings(data: UpdateSettingsData): Promise<SettingsData> {
    const response = await useApiClient()<ApiResponse<SettingsData>>(
      SettingsRoutes.UPDATE(),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }
}
