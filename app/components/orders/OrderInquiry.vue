<script setup lang="ts">
import type { AttachmentData, EmailOrderData, OrderInquiryData } from '~/utils/types/api/api'

const props = defineProps({
  orderInquiry: {
    type: Object as PropType<OrderInquiryData>,
    required: true,
  },
})

const supportingTypes = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  'application/msword', // doc
  'application/vnd.ms-excel', // xls
  'text/csv', // csv
  'text/plain', // txt
  'application/octet-stream', // Could be anything
]
const emailOrderData = props.orderInquiry.payload as unknown as EmailOrderData
const attachments: AttachmentData[] = Array.isArray(emailOrderData.attachments)
  ? emailOrderData.attachments
  : []
const filteredAttachments = attachments.filter((attachment) => {
  return supportingTypes.includes(attachment.mime_type)
})
const selectedItem = ref<string>(
  filteredAttachments[0]
    ? filteredAttachments[0].id
    : 'message',
)
const items = computed(() => {
  return [
    {
      label: 'Message',
      icon: 'i-ph-envelope',
      value: 'message',
    },
    ...filteredAttachments.map(attachment => ({
      label: attachment.filename,
      icon: 'i-ph-paperclip',
      value: attachment.id,
    })),
  ]
})

function formatDate(dateString: string) {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }

  return new Date(dateString).toLocaleString(undefined, options)
}

const selectedAttachment = computed(() =>
  attachments.find(a => a.id === selectedItem.value),
)
</script>

<template>
  <div class="flex flex-col gap-2 h-full">
    <div class="mx-4 flex flex-col gap-2">
      <span class="font-bold text-lg">{{ emailOrderData.subject }}</span>
      <div class="flex justify-between">
        <span class="text-sm">From: {{ emailOrderData.from }}</span>
        <span class="text-sm">{{ formatDate(emailOrderData.created_at) }}</span>
      </div>
    </div>
    <GeneralOverflow>
      <UTabs
        v-model="selectedItem"
        variant="link"
        :items="items"
      />
    </GeneralOverflow>
    <div v-if="selectedItem === 'message'" class="h-full">
      <iframe
        v-if="emailOrderData.mail_body_html"
        class="h-full w-full"
        :srcdoc="emailOrderData.mail_body_html ?? undefined"
      />
      <div
        v-else-if="emailOrderData.mail_body_text"
        class="h-full w-full"
      >
        {{ emailOrderData.mail_body_text }}
      </div>
      <div v-else>
        No email message content
      </div>
    </div>
    <div v-else class="h-full">
      <OrdersAttachment
        v-if="selectedAttachment"
        :key="selectedItem"
        :attachment="selectedAttachment"
      />
    </div>
  </div>
</template>
