<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeFormFieldData } from '~/utils/types/api/api'
import { FormsAssetTypeUpsertAssetTypeFormFieldForm, ModalsConfirmModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useDeleteAssetTypeFormFieldMutation, useMoveDownAssetTypeFormFieldMutation, useMoveUpAssetTypeFormFieldMutation } from '~/api/asset-type-form-fields/mutations/useAssetTypeFormFieldMutations'
import { useAssetTypeFormFieldsQuery } from '~/api/asset-type-form-fields/queries/useAssetTypeFormFieldQueries'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const assetTypeRelationship = useAssetTypeRelationshipStore()

function getFieldLabelById(fieldId: string): string {
  try {
    const field = allAssetTypeFields.value?.find(f => f.id === fieldId)
    return field?.label || 'No field'
  }
  catch {
    return 'No field'
  }
}

const { data: allAssetTypeFormFields } = useAssetTypeFormFieldsQuery()

const assetTypeFormFields = computed(() => {
  return allAssetTypeFormFields.value?.filter((field: AssetTypeFormFieldData) =>
    field.asset_type_id === props.assetTypeId,
  ) || []
})

const deleteAssetTypeFormFieldMutation = useDeleteAssetTypeFormFieldMutation()
const moveUpAssetTypeFormFieldMutation = useMoveUpAssetTypeFormFieldMutation()
const moveDownAssetTypeFormFieldMutation = useMoveDownAssetTypeFormFieldMutation()

// TODO: Improve relationship values. Pass store? Handle in store? Service somewhere?
const columns: BaseColumn<AssetTypeFormFieldData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('asset_type_relationship_id', 'Master data relationship', false, (assetTypeFormField) => {
    try {
      return assetTypeRelationship.getById(assetTypeFormField?.asset_type_relationship_id ?? '')?.label
    }
    catch {
      return 'No relationship'
    }
  }),
  new TextColumn('asset_type_field_id', 'Master data field', false, (assetTypeFormField) => {
    return getFieldLabelById(assetTypeFormField?.asset_type_field_id ?? '')
  }),
  new TextColumn('type', 'Type'),
  new CheckboxColumn('required', 'Required'),
  new CheckboxColumn('locked', 'Locked'),
  new CheckboxColumn('crud', 'Crud'),
  new TextColumn('span', 'Span'),
  new ActionColumn('Actions', [
    { label: 'Edit', onClick: onEdit },
    { label: 'Delete', onClick: onDelete },
    { label: 'Move up', onClick: onUp },
    { label: 'Move down', onClick: onDown },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const editModal = overlay.create(FormsAssetTypeUpsertAssetTypeFormFieldForm)

async function onUp(assetTypeFormField: AssetTypeFormFieldData) {
  if (!assetTypeFormField.id) {
    toast.add({
      title: 'Error moving master data form field up',
      description: 'Form field ID is missing',
      color: 'error',
    })
    return
  }

  try {
    await moveUpAssetTypeFormFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      id: assetTypeFormField.id as string,
    })
    toast.add({ title: 'Master data form field moved up', color: 'success' })
  }
  catch (e) {
    const errorMessage = (e as any)?.data?.message
      || (e as any)?.message
      || 'An error occurred'
    toast.add({
      title: 'Error moving master data form field up',
      description: `${errorMessage}. Asset Type ID: ${props.assetTypeId}, Field ID: ${assetTypeFormField.id}`,
      color: 'error',
    })
  }
}

async function onDown(assetTypeFormField: AssetTypeFormFieldData) {
  if (!assetTypeFormField.id) {
    toast.add({
      title: 'Error moving master data form field down',
      description: 'Form field ID is missing',
      color: 'error',
    })
    return
  }

  try {
    await moveDownAssetTypeFormFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      id: assetTypeFormField.id as string,
    })
    toast.add({ title: 'Master data form field moved down', color: 'success' })
  }
  catch (e) {
    const errorMessage = (e as any)?.data?.message
      || (e as any)?.message
      || 'An error occurred'
    toast.add({
      title: 'Error moving master data form field down',
      description: `${errorMessage}. Asset Type ID: ${props.assetTypeId}, Field ID: ${assetTypeFormField.id}`,
      color: 'error',
    })
  }
}

function onEdit(assetTypeFormField: AssetTypeFormFieldData) {
  editModal.open({
    assetTypeId: props.assetTypeId,
    assetTypeFormField,
  })
}

async function onDelete(assetTypeFormField: AssetTypeFormFieldData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await deleteAssetTypeFormFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      id: assetTypeFormField.id as string,
    })
    toast.add({ title: 'Master data form field deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data form field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeFormFields"
  />
</template>
