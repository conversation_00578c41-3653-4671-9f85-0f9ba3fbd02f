<script setup lang="ts">
import { computed } from 'vue'
import { DateFormat } from '~/utils/dates/date.format'

const props = defineProps<{
  value: string | null | undefined
  config?: {
    dateOnly?: boolean
  }
}>()

const dayjs = useDayjs()

const formatted = computed(() => {
  if (!props.value)
    return 'n/a'
  const date = dayjs(props.value).tz()
  return date.format(props.config?.dateOnly ? DateFormat.DD_MM_YYYY : DateFormat.DD_MM_YYYY_HH_MM)
})
</script>

<template>
  <time>{{ formatted }}</time>
</template>
