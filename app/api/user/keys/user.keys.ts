import type QueryData from '~/utils/query/QueryData'
import { createQueryKeys } from '@lukemorales/query-key-factory'
import { IdentityService } from '../services/identity.service'

export const userKeys = createQueryKeys('user', {
  me: {
    queryKey: null,
    queryFn: () => IdentityService.getUserInfo(),
  },
  all: (queryData: QueryData) => ({
    queryKey: [queryData ? queryData.toQueryString() : ''],
    queryFn: () => IdentityService.getAll(queryData),
  }),
  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => IdentityService.getById(id),
  }),
})
