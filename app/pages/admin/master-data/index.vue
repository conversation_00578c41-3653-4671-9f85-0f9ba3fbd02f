<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeData } from '~/utils/types/api/api'
import { FormsAssetTypeAddAssetTypeForm, FormsAssetTypeEditAssetTypeForm, ModalsConfirmModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const overlay = useOverlay()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()
const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()

function getFieldLabelById(fieldId: string): string {
  try {
    const field = allAssetTypeFields.value?.find(f => f.id === fieldId)
    return field?.label || '---'
  }
  catch {
    return '---'
  }
}

const assetFactoryStore = useAssetFactoryStore()

const columns: BaseColumn<AssetTypeData>[] = [
  new CopyColumn<AssetTypeData>('id', 'Id'),
  new TextColumn<AssetTypeData>('label', 'Label'),
  new TextColumn<AssetTypeData>('name', 'Name'),
  new TextColumn<AssetTypeData>('table_name', 'Table name'),
  new TextColumn<AssetTypeData>('display_field_id', 'Display field', false, (assetType) => {
    return getFieldLabelById(assetType?.display_field_id ?? '')
  }),
  new CheckboxColumn<AssetTypeData>('supporting_data', 'Reference data'),
  new ActionColumn<AssetTypeData>('Actions', [
    {
      label: 'Edit',
      onClick: async (row) => {
        await navigateTo(`/admin/master-data/${row.id}`)
      },
    },
    { label: 'Settings', onClick: row => onEdit(row) },
    { label: 'Delete', onClick: row => onDelete(row.id) },
    { label: 'Clear data', onClick: row => onClearData(row), shouldShow: row => canClearData(row) },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const addModal = overlay.create(FormsAssetTypeAddAssetTypeForm)
const editModal = overlay.create(FormsAssetTypeEditAssetTypeForm)
const confirmModal = overlay.create(ModalsConfirmModal)

function onAdd() {
  addModal.open()
}

function onEdit(assetType: AssetTypeData) {
  editModal.open({ assetType })
}

async function onDelete(id: string) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeStore.destroy(id)
    toast.add({ title: 'Master data deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Could not delete master data',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}

function canClearData(assetType: AssetTypeData) {
  return assetType.supporting_data
}

async function onClearData(assetType: AssetTypeData) {
  const overlay = confirmModal.open()
  const store = assetFactoryStore.get(assetType.table_name)()

  if (!await overlay.result)
    return

  try {
    await store.truncate()
    toast.add({ title: 'Reference data truncated', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Could not truncate data',
      description: (e as FetchError).data?.message || JSON.stringify(e) || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Master data">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            label="Add master data"
            leading-icon="i-ph-plus"
            @click="onAdd"
          />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UTable :columns="columnConfig" :data="assetTypeStore.assetTypes" />
    </template>
  </UDashboardPanel>
</template>
