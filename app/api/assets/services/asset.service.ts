import type { ApiResponse, Meta, PaginatedApiResponse } from '~/utils/types/api/api-response'
import qs from 'qs'
import { useApiClient } from '~/utils/api/api-client'
import QueryData from '~/utils/query/QueryData'
import { ASSET_API_ROUTES } from './asset-service.routes'

export class AssetService {
  static async getAll(table: string, queryData?: QueryData): Promise<{ data: any[], meta: Meta | null }> {
    queryData = queryData ?? new QueryData()

    const response = await useApiClient()<PaginatedApiResponse<any>>(
      `${ASSET_API_ROUTES.ASSETS(table)}?${queryData.toQueryString()}`,
    )

    return {
      data: response.data,
      meta: response.meta,
    }
  }

  static async getById(table: string, id: string, query?: object): Promise<any> {
    const querySuffix = query ? `?${qs.stringify(query)}` : ''

    const response = await useApiClient()<ApiResponse<any>>(
      `${ASSET_API_ROUTES.ASSET_BY_ID(table, id)}${querySuffix}`,
    )

    return response.data
  }

  static async create(table: string, data: object): Promise<any> {
    const response = await useApiClient()<ApiResponse<any>>(
      ASSET_API_ROUTES.ASSETS(table),
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async update(table: string, id: string, data: object): Promise<any> {
    const response = await useApiClient()<ApiResponse<any>>(
      ASSET_API_ROUTES.ASSET_BY_ID(table, id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async delete(table: string, id: string): Promise<any> {
    const response = await useApiClient()<ApiResponse<any>>(
      ASSET_API_ROUTES.ASSET_BY_ID(table, id),
      {
        method: 'DELETE',
      },
    )

    return response.data
  }

  static async action(table: string, id: string, action: string, data?: object): Promise<any> {
    await useApiClient()<ApiResponse<any>>(
      ASSET_API_ROUTES.ACTION(table, id),
      {
        method: 'POST',
        body: JSON.stringify({
          action,
          data,
        }),
      },
    )
  }

  static async getTableData(table: string, queryData?: QueryData): Promise<PaginatedApiResponse<any>> {
    queryData = queryData ?? new QueryData()

    return useApiClient()<PaginatedApiResponse<any>>(
      `${ASSET_API_ROUTES.TABLE_DATA(table)}?${queryData.toQueryString()}`,
    )
  }

  static async search(table: string, queryData?: QueryData): Promise<{ data: any[], meta: Meta | null }> {
    queryData = queryData ?? new QueryData()

    const response = await useApiClient()<PaginatedApiResponse<any>>(
      `${ASSET_API_ROUTES.SEARCH(table)}?${queryData.toQueryString()}`,
    )

    return {
      data: response.data,
      meta: response.meta,
    }
  }

  static async truncate(table: string): Promise<any> {
    const response = await useApiClient()<ApiResponse<any>>(
      ASSET_API_ROUTES.TRUNCATE(table),
      {
        method: 'DELETE',
      },
    )

    return response.data
  }
}
