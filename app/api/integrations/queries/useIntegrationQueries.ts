import type { IntegrationData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { integrationKeys } from '../keys/integration.keys'

export function useIntegrationsQuery(
  options?: ComposableQueryOptions<IntegrationData, typeof integrationKeys.byId>,
) {
  return useQuery({
    ...integrationKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useAvailableIntegrationsQuery() {
  return useQuery({
    ...integrationKeys.allAvailable,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useIntegrationByIdQuery(
  id: string,
  options?: ComposableQueryOptions<IntegrationData, typeof integrationKeys.byId>,
) {
  return useQuery({
    ...integrationKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useIntegrationByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<IntegrationData, typeof integrationKeys.byId>,
) {
  return useQuery({
    ...integrationKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}
