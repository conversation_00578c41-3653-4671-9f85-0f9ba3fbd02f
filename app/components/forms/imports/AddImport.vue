<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { ImportData } from '~/utils/types/api/api'

const emit = defineEmits<{ close: [ImportData] }>()
const toast = useToast()

const assetTypeStore = useAssetTypeStore()
const importStore = useImportStore()

const file = ref<File>()
const selectedAssetType = ref<string>()
const delimiter = ref(';')
const isPending = ref(false)

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  file.value = target.files?.[0] ?? undefined
}

async function onSubmit() {
  if (!selectedAssetType.value || !file.value) {
    return
  }

  try {
    emit('close', (await importStore.create(selectedAssetType.value, file.value, delimiter.value)).data)
  }
  catch (e) {
    toast.add({
      title: `Error while adding import`,
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover title="Add import" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Master data" class="col-span-12">
            <USelect
              v-model="selectedAssetType"
              :items="assetTypeStore.getSupportingDataAssetTypes()"
              placeholder="Select an master data"
              label-key="label"
              value-key="id"
              class="w-full"
            />
          </UFormField>
          <UFormField label="Upload file" class="col-span-9">
            <UInput
              type="file"
              placeholder="Select a file"
              @change="handleFileChange"
            />
          </UFormField>
          <UFormField label="delimiter" class="col-span-3">
            <USelect
              v-model="delimiter"
              :items="[',', ';']"
              class="w-full"
            />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Create
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
