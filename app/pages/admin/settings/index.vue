<script setup lang="ts">
import type { UpdateSettingsData } from '~/utils/types/api/api'

const settingsStore = useSettingsStore()

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})
const toast = useToast()

// Component initialize
const loading = ref(false)
const updateSettingsData = ref<UpdateSettingsData>({
  autoProcess: settingsStore.settings?.autoProcess ?? false,
  renderOfficeDocuments: settingsStore.settings?.renderOfficeDocuments ?? false,
})

// Functions
async function updateSettings() {
  try {
    loading.value = true

    await settingsStore.update(updateSettingsData.value)

    toast.add({
      title: 'Settings updated',
      color: 'success',
    })
  }
  catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Settings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              Product
            </h4>
          </div>
          <div>{{ settingsStore.settings?.recipeModel }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              Master data version
            </h4>
          </div>
          <div>{{ settingsStore.settings?.recipeVersion }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              Auto Process Orders
            </h4>
            <p class="text-sm text-neutral-500">
              Automatically process incoming orders without manual review
            </p>
          </div>
          <USwitch
            v-model="updateSettingsData.autoProcess"
            :disabled="loading"
            @change="updateSettings"
          />
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              Render Office Documents
            </h4>
            <p class="text-sm text-neutral-500">
              Display Office documents (Word, Excel) in the attachment viewer.<br>
              Enabling this will send your data to servers outside of the secure Hyperfox network.
            </p>
          </div>
          <USwitch
            v-model="updateSettingsData.renderOfficeDocuments"
            :disabled="loading"
            @change="updateSettings"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
