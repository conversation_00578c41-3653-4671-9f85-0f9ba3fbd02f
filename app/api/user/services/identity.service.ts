import type { UserInfoResponse } from '~/schemas/identity/user-info-response.schema'
import type QueryData from '~/utils/query/QueryData'
import type { CreateUserData, UpdateUserData, UserData } from '~/utils/types/api/api'
import type { ApiResponse, PaginatedApiResponse } from '~/utils/types/api/api-response'
import { userInfoResponseSchema } from '~/schemas/identity/user-info-response.schema'
import { useApiClient } from '~/utils/api/api-client'
import { validateApiResponse } from '~/utils/api/validate-api-response'
import { IDENTITY_SERVICE_API_ROUTES } from './identity-service.routes'

export class IdentityService {
  static async getUserInfo(): Promise<UserInfoResponse> {
    const raw = await useApiClient<ApiResponse<UserInfoResponse>>()(
      IDENTITY_SERVICE_API_ROUTES.ME,
    )

    return validateApiResponse(userInfoResponseSchema, raw.data)
  }

  static async logout(): Promise<void> {
    await useApiClient<void>()(IDENTITY_SERVICE_API_ROUTES.LOGOUT, {
      method: 'POST',
    })
  }

  static async getAll(queryData: QueryData): Promise<PaginatedApiResponse<UserData[]>> {
    return await useApiClient<PaginatedApiResponse<UserData[]>>()(
      `api/identity/users?${queryData.toQueryString()}`,
    )
  }

  static async getById(id: string): Promise<UserData> {
    const response = await useApiClient<ApiResponse<UserData>>()(
      IDENTITY_SERVICE_API_ROUTES.USER_BY_ID(id),
    )

    return response.data
  }

  static async create(data: CreateUserData): Promise<UserData> {
    const response = await useApiClient<ApiResponse<UserData>>()(
      IDENTITY_SERVICE_API_ROUTES.USERS,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async update(id: string, data: UpdateUserData): Promise<UserData> {
    const response = await useApiClient<ApiResponse<UserData>>()(
      IDENTITY_SERVICE_API_ROUTES.USER_BY_ID(id),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async delete(id: string): Promise<boolean> {
    return await useApiClient<ApiResponse<boolean>>()(
      IDENTITY_SERVICE_API_ROUTES.USER_BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }
}
