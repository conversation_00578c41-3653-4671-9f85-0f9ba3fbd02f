import type { FormElement } from '~/utils/forms/FormElement'
import type { FormFieldExtra } from '~/utils/forms/FormFieldExtra'
import type { AssetTypeFormFieldType } from '~/utils/types/api/api'

export class FormField implements FormElement {
  constructor(
    public readonly type: AssetTypeFormFieldType,
    public readonly name: string,
    public readonly label: string,
    public readonly cols: number = 12,
    public readonly crud: boolean = false,
    public readonly extra?: FormFieldExtra,
    public readonly id?: string,
  ) {
  }
}
