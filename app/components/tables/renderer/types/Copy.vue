<script setup lang="ts">
// Supporting types
import { useClipboard } from '@vueuse/core'

interface Props {
  value: string | number
}

const props = defineProps<Props>()
const tooltipText = ref(props.value as string)
const open = ref(false)
const clipboard = useClipboard({
  legacy: true,
})

async function copyValueToClipboard() {
  await writeToClipboard(props.value as string)
  tooltipText.value = 'Copied!'
  open.value = true
  setTimeout(() => {
    tooltipText.value = props.value as string
  }, 3000)
}

async function writeToClipboard(text: string) {
  await clipboard.copy(text)
}
</script>

<template>
  <UTooltip v-model:open="open" :text="tooltipText">
    <UIcon name="i-ph-info" class="size-5" @click="copyValueToClipboard" />
  </UTooltip>
</template>
