import type { ColumnDef } from '@tanstack/vue-table'
import type { SortingStrategy } from './sorting/SortingStrategy'
import { TablesRendererTypesDateTime, UButton } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'
import { MultiSortingStrategy } from './sorting/MultiSortingStrategy'

export class DateTimeColumn<T> extends BaseColumnWithField<T> {
  private sortingStategy: SortingStrategy
  constructor(
    field: keyof T,
    label: string,
      sortable: boolean = false,
      private dateOnly: boolean = false,
  ) {
    super(field, label, sortable, ColumnType.DateTime)

    this.sortingStategy = new MultiSortingStrategy()
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: ({ column }) => {
        if (!column.getCanSort()) {
          return this.label
        }
        return h(UButton, {
          color: 'neutral',
          variant: 'ghost',
          label: this.label,
          icon: this.sortingStategy.getSortIcon(column.getIsSorted()),
          class: '-mx-2.5',
          onClick: e => this.sortingStategy.handleClick(e, column),
        })
      },
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesDateTime, {
        utcDateTime: this.getField(row.original),
        dateOnly: this.dateOnly,
      }),
    }
  }
}
