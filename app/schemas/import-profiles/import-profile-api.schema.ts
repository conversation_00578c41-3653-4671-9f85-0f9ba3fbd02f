import { z } from 'zod'

export const importProfileApiSchema = z.object({
  path: z.string().nullable(),
  page_parameter: z.string().nullable(),
  page_start_index: z
    .union([z.string(), z.number()])
    .nullable()
    .transform((val) => {
      if (val === '' || val == null)
        return null
      return typeof val === 'string' ? Number.parseInt(val, 10) : val
    })
    .pipe(z.number().int().min(0).nullable()),
  page_step_size: z
    .union([z.string(), z.number()])
    .nullable()
    .transform((val) => {
      if (val === '' || val == null)
        return null
      return typeof val === 'string' ? Number.parseInt(val, 10) : val
    })
    .pipe(z.number().int().min(1).nullable()),
  page_size_parameter: z.string().nullable(),
  page_size_length: z
    .union([z.string(), z.number()])
    .nullable()
    .transform((val) => {
      if (val === '' || val == null)
        return null
      return typeof val === 'string' ? Number.parseInt(val, 10) : val
    })
    .pipe(z.number().int().min(1).nullable()),
  data_wrapper: z.string().nullable(),
  additional_parameters: z.string().nullable(),
})

export type ImportProfileApiFormData = z.infer<typeof importProfileApiSchema>
