<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { computed } from 'vue'
import { useFormFieldError } from '~/composables/form/useFormFieldError'

const props = defineProps<FormInputProps>()

const model = defineModel({
  type: String,
})

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)

const convertedDate = computed<string | undefined>({
  get: () => {
    if (!model.value) {
      return undefined
    }

    return model.value.split('T')[0] as string
  },
  set: (value: string | undefined) => {
    model.value = value
  },
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <UFormField :label="label" :error="error">
      <UInput
        v-model="convertedDate"
        type="date"
        :disabled="isReadOnly"
      />
    </UFormField>
  </div>
</template>
