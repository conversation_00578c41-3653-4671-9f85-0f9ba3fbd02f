<script setup lang="ts">
import { useToast } from '#imports'
import { useClipboard } from '@vueuse/core'
import hyperfoxAvatar from '~/assets/img/hyperfox-logo.svg'

const clipboard = useClipboard({ legacy: true })
const toast = useToast()

const store = useChannelsStore()
const { settings } = useSettingsStore()

const firstChannelEmail = ref()
onMounted(async () => {
  await store.hydrate()
  firstChannelEmail.value = store.channels[0]?.label
})

let recentlyCopied = false

async function copyEmail() {
  if (recentlyCopied)
    return

  recentlyCopied = true
  await clipboard.copy(firstChannelEmail.value)

  toast.add({
    title: 'Email copied to clipboard',
    description: 'You can now paste it in your email client.',
    color: 'success',
  })

  setTimeout(() => {
    recentlyCopied = false
  }, 2000)
}
</script>

<template>
  <UCard class="mb-6" variant="soft">
    <template #header>
      <div class="flex items-center gap-4">
        <img :src="hyperfoxAvatar" alt="Hyperfox logo" class="h-10 w-auto">
        <div>
          <h2 class="text-lg font-semibold">
            Hello! Ready to automate your orders?
          </h2>
          <p class="text-sm text-gray-500">
            Let’s get started.
          </p>
        </div>
      </div>
    </template>

    <template #default>
      <p>
        Simply forward any orders you receive by email (with attachments such as PDF, CSV, XLS, DOC, etc.) directly to
      </p>

      <div
        v-if="firstChannelEmail"
        class="flex items-center w-fit justify-between rounded-md border border-gray-200 bg-gray-50 pl-4 pr-2 py-2 font-mono text-sm text-gray-800 mt-2 mb-4"
      >
        <span class="pr-3">{{ firstChannelEmail }}</span>
        <div class="h-4 w-px bg-gray-300 mx-2" />
        <UButton
          size="xs"
          variant="ghost"
          icon="i-lucide-copy"
          @click="copyEmail"
        />
      </div>

      <p v-else class="text-sm text-red-500 mt-2 mb-4">
        Cannot find a channel email. Please check your configuration.
      </p>

      <p v-if="!settings?.autoProcess" class="mb-4">
        Then go to ‘To Process’ and click the red ‘Process’ button. Our Hyperfox AI will extract the order data for you.
      </p>

      <p class="mb-4">
        A few minutes later, the order will appear in the ‘To Validate’ section.<br>
        There, you can review the extracted order details and confirm that everything looks correct before validating the order.
      </p>

      <p class="mb-4">
        <a class="text-primary underline" href="https://www.youtube.com/watch?v=s6hUwaqOWVM" target="_blank"> We’ve created a 60-second introduction video to guide you.</a>
      </p>

      <p class="mb-4">
        If you run into any issues or have questions, feel free to email us at
        <a href="mailto:<EMAIL>" class="text-primary underline"><EMAIL></a>. — we’re happy to help.
      </p>

      <p>
        We wish you happy automating.<br>
        <strong>May the fox be with you.</strong>
      </p>
    </template>
  </UCard>
</template>
