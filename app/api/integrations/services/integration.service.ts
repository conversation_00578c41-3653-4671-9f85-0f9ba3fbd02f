import type { AvailableIntegrationData, CreateIntegrationData, IntegrationData, StatusMessage, TestIntegrationData, UpdateIntegrationData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import { INTEGRATION_API_ROUTES } from './integration-service.routes'

export class IntegrationService {
  static async getAll(): Promise<IntegrationData[]> {
    const response = await useApiClient()<ApiResponse<IntegrationData[]>>(
      INTEGRATION_API_ROUTES.ALL,
    )

    return response.data
  }

  static async getALlAvailable(): Promise<Record<string, AvailableIntegrationData>> {
    const response = await useApiClient()<ApiResponse<Record<string, AvailableIntegrationData>>>(
      INTEGRATION_API_ROUTES.ALL_AVAILABLE,
    )

    return response.data
  }

  static async getById(integrationId: string): Promise<IntegrationData> {
    const response = await useApiClient()<ApiResponse<IntegrationData>>(
      INTEGRATION_API_ROUTES.BY_ID(integrationId),
    )

    return response.data
  }

  static async getByIdOrFail(integrationId: string): Promise<IntegrationData> {
    const integration = await this.getById(integrationId)

    if (!integration) {
      throw new Error(`Integration with ID ${integrationId} not found`)
    }

    return integration
  }

  static async create(data: CreateIntegrationData): Promise<IntegrationData> {
    const response = await useApiClient()<ApiResponse<IntegrationData>>(
      INTEGRATION_API_ROUTES.ALL,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async update(
    integrationId: string,
    data: UpdateIntegrationData,
  ): Promise<IntegrationData> {
    const response = await useApiClient()<ApiResponse<IntegrationData>>(
      INTEGRATION_API_ROUTES.BY_ID(integrationId),
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }

  static async delete(integrationId: string): Promise<void> {
    await useApiClient()(
      INTEGRATION_API_ROUTES.BY_ID(integrationId),
      {
        method: 'DELETE',
      },
    )
  }

  static async test(data: TestIntegrationData): Promise<StatusMessage> {
    const response = await useApiClient()<ApiResponse<StatusMessage>>(
      INTEGRATION_API_ROUTES.TEST,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    return response.data
  }
}
