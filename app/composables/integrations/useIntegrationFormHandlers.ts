import type { FetchError } from 'ofetch'

export function useIntegrationFormHandlers() {
  const toast = useToast()

  function handleFormSubmitError(error: unknown, isCreateMode: boolean) {
    const errorMessage = (error as FetchError).data?.message || 'An error occurred'

    toast.add({
      title: `Error ${isCreateMode ? 'creating' : 'updating'} integration`,
      description: errorMessage,
      color: 'error',
    })
  }

  function handleFormSubmitSuccess(isCreateMode: boolean) {
    toast.add({
      title: `Integration ${isCreateMode ? 'created' : 'updated'} successfully`,
      color: 'success',
    })

    navigateTo('/admin/integrations')
  }

  function handleQueryError(error: FetchError) {
    toast.add({
      title: 'Error loading integration',
      description: error.data?.message || 'An error occurred while loading the integration',
      color: 'error',
    })
  }

  return {
    handleFormSubmitError,
    handleFormSubmitSuccess,
    handleQueryError,
  }
}
