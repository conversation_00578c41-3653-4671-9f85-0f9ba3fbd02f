<script setup lang="ts">
import { refDebounced } from '@vueuse/shared'
import { AssetTypeFieldCacheService } from '~/api/asset-type-fields/services/asset-type-field-cache.service'
import { AssetQueryFactory } from '~/api/assets'
import QueryData from '~/utils/query/QueryData'

const props = defineProps<{
  value: string | null | undefined
  row: Record<string, any>
  config?: {
    filterable?: boolean
    relationship?: {
      asset_type_relationship_id: string
      asset_type_field_id: string
      display_field: string
    }
    field?: {
      template?: string | null
    }
  }
  isReadOnly?: boolean
}>()

const emit = defineEmits<{
  (e: 'update', value: number | string | null | undefined): void
}>()

const items = ref<any[]>([])
const buyerCustomerPartyId = ref<string | null>(null)
const searchTerm = ref('')
const searchTermDebounced = refDebounced(searchTerm, 300)
const query: Ref<QueryData> = shallowRef(new QueryData())

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const assetTypeStore = useAssetTypeStore()

const relationship = computed(() =>
  assetTypeRelationshipStore.getById(props.config?.relationship?.asset_type_relationship_id ?? ''),
)

const assetType = computed(() =>
  assetTypeStore.getById(relationship.value?.relationship_asset_type_id ?? ''),
)

const displayField = computed(() => {
  return props.config?.relationship?.display_field ?? 'id'
})

const assetQuery = AssetQueryFactory.getQueryInstance(assetType.value?.table_name)

const { useAssetSearchQuery, useAssetQuery } = assetQuery.getQueries()

const hasMenuBeenOpened = ref(false)

const assetsQuery = useAssetSearchQuery(
  query,
  {
    enabled: hasMenuBeenOpened,
  },
)

function handleMenuOpen() {
  hasMenuBeenOpened.value = true
}

watch(assetsQuery.data, (newValue) => {
  if (!newValue)
    return

  items.value = newValue.data || []
}, { immediate: true })

const assetId: Ref<string | null> = ref(props.value ?? null)

watch(() => props.value, (newValue) => {
  assetId.value = newValue ?? null
}, { immediate: true })

const assetIdComputed = computed(() => assetId.value || '')

const { data: selectedItemData } = useAssetQuery(
  assetIdComputed,
  {
    enabled: !!assetId.value,
  },
)

const selectedItem = ref<any>(null)
watch(selectedItemData, (newValue) => {
  if (!newValue)
    return

  selectedItem.value = newValue
}, { immediate: true })

interface Order {
  buyer_customer_party_id?: string
}

const order = inject<Ref<Order | null>>('order', ref(null))

const shouldFilterOnCustomerParty = ref(
  props.config?.filterable ?? false,
)

const hasCustomerParty = computed(() => {
  return order.value?.buyer_customer_party_id != null
})

watch(
  order,
  (newVal) => {
    buyerCustomerPartyId.value = newVal?.buyer_customer_party_id ?? null
  },
  { immediate: true, deep: true },
)

async function updateItems() {
  const newQuery = new QueryData()

  const params: Record<string, any> = {}

  if (shouldFilterOnCustomerParty.value && buyerCustomerPartyId.value) {
    params.special = buyerCustomerPartyId.value
  }

  if (searchTermDebounced.value && searchTermDebounced.value.trim() !== '') {
    const template = props.config?.field?.template
    if (!template) {
      params.field = displayField.value
    }

    params.q = searchTermDebounced.value.trim()
  }

  newQuery.setParams(params)
  newQuery.setPerPage(10)
  query.value = newQuery
}
updateItems()

watch(searchTermDebounced, async () => {
  await updateItems()
})

watch(buyerCustomerPartyId, async () => {
  await updateItems()
})

watch(shouldFilterOnCustomerParty, async () => {
  await updateItems()
})

watch(selectedItem, (newValue) => {
  if (newValue && newValue.id) {
    emit('update', newValue.id)
  }
  else {
    emit('update', null)
  }
})

function renderTemplate(item: any, singleLine = false) {
  if (!item.id) {
    return 'No data found'
  }

  const template = props.config?.field?.template
  if (!template) {
    return undefined
  }

  let result = template.replace(/\{\{(.*?)\}\}/g, (match, id) => {
    if (!id) {
      return ''
    }

    try {
      const field = AssetTypeFieldCacheService.getByIdOrFail(id.trim())
      return item[field.field] || ''
    }
    catch {
      return ''
    }
  })

  if (singleLine) {
    result = result.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
  }

  return result
}
</script>

<template>
  <USelectMenu
    v-model="selectedItem"
    v-model:search-term="searchTerm"
    class="w-full"
    :items="items"
    :label-key="displayField"
    :loading="assetsQuery.isFetching.value"
    :disabled="props.isReadOnly"
    ignore-filter
    @focus="handleMenuOpen"
  >
    <template #content-top>
      <UButton
        v-if="config?.filterable && hasCustomerParty"
        type="button"
        variant="subtle"
        :color="shouldFilterOnCustomerParty ? 'info' : 'neutral'"
        icon="i-lucide-filter"
        @click="shouldFilterOnCustomerParty = !shouldFilterOnCustomerParty"
      >
        <span class="flex-1 text-left">Customer Party</span>
      </UButton>
    </template>
    <template #default>
      <div v-if="selectedItem" class="flex flex-col gap-1 items-start">
        <div class="text-left pre-wrap">
          {{ selectedItem[displayField] }}
        </div>
        <div v-if="config?.field?.template" class="text-sm text-gray-500 pre-wrap text-left">
          {{ renderTemplate(selectedItem, true) }}
        </div>
      </div>
      <div v-else class="flex flex-col gap-1 items-start">
        <div>
          Select item
        </div>
      </div>
    </template>
    <template #item-label="{ item }">
      <div class="flex flex-col gap-1 items-start">
        <div class="text-left">
          {{ item[displayField] }}
        </div>
        <div v-if="config?.field?.template" class="text-sm text-gray-500 pre-wrap text-left">
          {{ renderTemplate(item) }}
        </div>
      </div>
    </template>
  </USelectMenu>
</template>
