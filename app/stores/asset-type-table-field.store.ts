import type { AssetTypeTableFieldData, UpsertAssetTypeTableFieldData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useAssetTypeTableFieldStore = defineStore('asset-type-table-field', () => {
  const assetTypeTableFields = ref<AssetTypeTableFieldData[]>([])

  // Local state functions

  function getAll(): AssetTypeTableFieldData[] {
    return assetTypeTableFields.value
  }

  function getByAssetTypeId(assetTypeId: string): AssetTypeTableFieldData[] {
    return assetTypeTableFields.value.filter(field => field.asset_type_id === assetTypeId)
  }

  // API functions

  async function hydrate(
    assetTypeId?: string,
  ) {
    const url = assetTypeId
      ? `api/dynamic-assets/asset-types/${assetTypeId}/table-fields`
      : `api/dynamic-assets/asset-type-table-fields`

    const response = await useApiClient()<ApiResponse<AssetTypeTableFieldData[]>>(url)

    if (assetTypeId) {
      assetTypeTableFields.value = [
        ...assetTypeTableFields.value.filter(field => field.asset_type_id !== assetTypeId),
        ...response.data,
      ]
    }
    else {
      assetTypeTableFields.value = response.data
    }
  }

  async function create(
    assetTypeId: string,
    upsertAssetTypeTableFieldData: UpsertAssetTypeTableFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeTableFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/table-fields`,
      {
        method: 'POST',
        body: JSON.stringify(upsertAssetTypeTableFieldData),
      },
    )

    assetTypeTableFields.value = [...assetTypeTableFields.value, response.data]

    return response
  }

  async function update(
    assetTypeId: string,
    assetTypeTableFieldId: string,
    upsertAssetTypeTableFieldData: UpsertAssetTypeTableFieldData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeTableFieldData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/table-fields/${assetTypeTableFieldId}`,
      {
        method: 'PUT',
        body: JSON.stringify(upsertAssetTypeTableFieldData),
      },
    )

    assetTypeTableFields.value = assetTypeTableFields.value.map(assetTypeTableField =>
      assetTypeTableField.id === assetTypeTableFieldId ? response.data : assetTypeTableField,
    )

    return response
  }

  async function destroy(
    assetTypeId: string,
    assetTypeTableFieldId: string,
  ) {
    await useApiClient()<ApiResponse<void>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/table-fields/${assetTypeTableFieldId}`,
      {
        method: 'DELETE',
      },
    )

    assetTypeTableFields.value = assetTypeTableFields.value.filter(assetTypeTableField => assetTypeTableField.id !== assetTypeTableFieldId)
  }

  async function moveUp(
    assetTypeId: string,
    id: string,
  ) {
    await useApiClient()<ApiResponse<void>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/table-fields/${id}/up`,
    )
  }

  async function moveDown(
    assetTypeId: string,
    id: string,
  ) {
    await useApiClient()<ApiResponse<void>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/table-fields/${id}/down`,
    )
  }

  return {
    assetTypeTableFields,

    getAll,
    getByAssetTypeId,

    hydrate,
    create,
    update,
    destroy,

    moveUp,
    moveDown,
  }
})
