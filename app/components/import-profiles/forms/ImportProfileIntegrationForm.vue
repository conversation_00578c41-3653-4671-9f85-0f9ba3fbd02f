<script setup lang="ts">
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { useIntegrationByIdOrFailQuery } from '~/api/integrations/queries/useIntegrationQueries'
import ImportProfileIntegrationApiForm from './ImportProfileIntegrationApiForm.vue'
import ImportProfileIntegrationFtpForm from './ImportProfileIntegrationFtpForm.vue'

const props = defineProps<{
  id: string
  profile: ImportProfileFormData
}>()

const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  previousStep: []
}>()
const integrationId = computed(() => {
  return props.profile.integration_id
})

const {
  data: integration,
} = useIntegrationByIdOrFailQuery(integrationId.value!, {
  enabled: computed(() => !!integrationId.value),
})

const integrationType = computed(() => integration.value?.type || '')

function handleSubmit(data: ImportProfileFormData, isSaveAndBackToOverview: boolean = false) {
  emit('submit', data, isSaveAndBackToOverview)
}
</script>

<template>
  <ImportProfileIntegrationApiForm
    v-if="integrationType === 'api'"
    :profile="profile"
    @submit="handleSubmit"
    @previous-step="$emit('previousStep')"
  />
  <ImportProfileIntegrationFtpForm
    v-else-if="integrationType === 'ftp'"
    :id="id"
    :profile="profile"
    @submit="handleSubmit"
    @previous-step="$emit('previousStep')"
  />
</template>
