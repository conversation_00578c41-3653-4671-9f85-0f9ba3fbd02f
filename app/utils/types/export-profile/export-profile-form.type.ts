import type {
  UpdateExportProfileGeneralInfoData,
  UpdateExportProfilePathData,
  UpdateExportProfileTemplateData,
} from '../api/api'

type CleanTemplateData = Omit<UpdateExportProfileTemplateData, 'template'>

export interface ExportProfileFormData
  extends Partial<UpdateExportProfileGeneralInfoData>,
  Partial<UpdateExportProfilePathData>,
  Partial<CleanTemplateData> {
  integration_id: string
  status: string
  template?: string | null
  crlf: boolean
}
