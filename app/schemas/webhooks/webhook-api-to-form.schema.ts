import type { WebhookConfigurationFormData } from './webhook-form-schema'
import { z } from 'zod'

export const webhookDataSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  event: z.string(),
  target_url: z.string(),
  secret: z.string().nullable().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
})

export const webhookApiDataToFormSchema = webhookDataSchema.transform((data): WebhookConfigurationFormData => ({
  name: data.name,
  event: data.event as 'order.validated',
  target_url: data.target_url,
}))
