import type { NuxtApp } from '#app'
import type { FetchContext } from 'ofetch'

export default defineAppConfig({
  ui: {
    colors: {
      primary: 'hyperfox',
    },
    selectMenu: {
      slots: {
        content: 'min-w-fit',
      },
    },
    slideover: {
      slots: {
        content: 'm-4 rounded-lg bg-neutral-50 pl-12',
        header: 'bg-white rounded-tr-lg',
        body: 'bg-white rounded-br-lg w-full',
        close: 'absolute top-4 start-2 w-8',
        footer: 'bg-white rounded-tr-lg',
      },
      variants: {
        side: {
          right: {
            content: 'right-0 inset-y-0 w-full max-w-xl md:max-w-lg',
          },
        },
      },
    },
    button: {
      slots: {
        base: 'justify-center cursor-pointer',
      },
      defaultVariants: {
        color: 'neutral',
      },
    },
    input: {
      slots: {
        root: 'flex',
      },
    },
    textarea: {
      slots: {
        root: 'flex',
      },
    },
    table: {
      slots: {
        base: 'table-fixed',
        th: 'pt-0 max-w-48',
        td: 'max-w-48 truncate',
      },
    },
    tabs: {
      slots: {
        trigger: 'cursor-pointer',
      },
    },
  },
  sanctum: {
    interceptors: {
      onRequest: async (
        app: NuxtApp,
        ctx: FetchContext,
      ) => {
        const { protocol, hostname } = window.location

        const parts = hostname.split('.')
        const domain = `${parts[0]}.api.${parts.slice(1).join('.')}`

        ctx.options.baseURL = `${protocol}//${domain}/`
      },
    },
  },
})
