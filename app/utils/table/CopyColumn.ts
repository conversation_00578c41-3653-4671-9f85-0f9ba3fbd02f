import type { ColumnDef } from '@tanstack/vue-table'
import { TablesRendererTypesCopy } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'

export class CopyColumn<T> extends BaseColumnWithField<T> {
  constructor(
    field: keyof T,
    label: string,
      sortable: boolean = false,
  ) {
    super(field, label, sortable, ColumnType.Copy)
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesCopy, {
        value: this.getField(row.original),
      }),
    }
  }
}
