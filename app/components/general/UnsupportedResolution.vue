<script setup lang="ts">
import { useScreenRequirement } from '~/composables/utils/useScreenRequirement'

const { MIN_HEIGHT, MIN_WIDTH, dismissResolutionWarning } = useScreenRequirement()
</script>

<template>
  <div class="fixed inset-0 z-50 bg-background/95 backdrop-blur-sm flex flex-col items-center justify-center text-center p-4">
    <UCard
      class="max-w-sm w-full"
    >
      <template #header>
        <div class="flex items-center justify-end">
          <UButton
            variant="ghost"
            @click="dismissResolutionWarning"
          >
            <UIcon size="20" name="i-ph-x-circle-bold" />
          </UButton>
        </div>
        <img class="w-24 h-24 mx-auto mb-2" src="~/assets/img/sad-fox.png" alt="Sad fox">
        <h2 class="text-xl font-semibold">
          Unsupported Screen Size
        </h2>
      </template>

      <p class="text-sm text-muted-foreground">
        This resolution isn’t supported. Please make your browser window larger (minimum <strong>{{ MIN_WIDTH }}x{{ MIN_HEIGHT }}</strong>).
      </p>
    </UCard>
  </div>
</template>
