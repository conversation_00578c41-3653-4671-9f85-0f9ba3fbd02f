import type {
  AssetTypeRelationshipData,
  CreateAssetTypeRelationshipData,
  UpdateAssetTypeRelationshipData,
} from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export interface RelationshipFilter<T = any> {
  field: keyof AssetTypeRelationshipData
  value: T
}

export const useAssetTypeRelationshipStore = defineStore('asset-type-relationship', () => {
  const assetTypeRelationships = ref<AssetTypeRelationshipData[]>([])

  // Local state functions

  function getAll(): AssetTypeRelationshipData[] {
    return assetTypeRelationships.value
  }

  function getById(assetTypeRelationshipId: string): AssetTypeRelationshipData {
    const relationship = assetTypeRelationships.value.find(relationship => relationship.id === assetTypeRelationshipId)
    if (!relationship)
      throw new Error(`Master data relationship not found for id: ${assetTypeRelationshipId}`)
    return relationship
  }

  function getByAssetTypeId(
    assetTypeId: string,
    filters?: RelationshipFilter[],
  ): AssetTypeRelationshipData[] {
    const results = assetTypeRelationships.value.filter(relationship =>
      relationship.asset_type_id === assetTypeId,
    )

    if (!filters || filters.length === 0) {
      return results
    }

    return applyFilters(results, filters)
  }

  // API functions

  async function hydrate(
    assetTypeId?: string,
  ) {
    const url = assetTypeId
      ? `api/dynamic-assets/asset-types/${assetTypeId}/relationships`
      : `api/dynamic-assets/asset-type-relationships`

    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData[]>>(url)

    if (assetTypeId) {
      assetTypeRelationships.value = [
        ...assetTypeRelationships.value.filter(relationship => relationship.asset_type_id !== assetTypeId),
        ...response.data,
      ]
    }
    else {
      assetTypeRelationships.value = response.data
    }
  }

  async function create(
    assetTypeId: string,
    createAssetTypeRelationshipData: CreateAssetTypeRelationshipData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/relationships`,
      {
        method: 'POST',
        body: JSON.stringify(createAssetTypeRelationshipData),
      },
    )

    assetTypeRelationships.value = [...assetTypeRelationships.value, response.data]

    return response
  }

  async function update(
    assetTypeId: string,
    assetTypeRelationshipId: string,
    updateAssetTypeRelationshipData: UpdateAssetTypeRelationshipData,
  ) {
    const response = await useApiClient()<ApiResponse<AssetTypeRelationshipData>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/relationships/${assetTypeRelationshipId}`,
      {
        method: 'PUT',
        body: JSON.stringify(updateAssetTypeRelationshipData),
      },
    )

    assetTypeRelationships.value = assetTypeRelationships.value.map(assetTypeRelationship =>
      assetTypeRelationship.id === assetTypeRelationshipId ? response.data : assetTypeRelationship,
    )

    return response
  }

  async function destroy(
    assetTypeId: string,
    assetTypeRelationshipId: string,
  ) {
    await useApiClient()<ApiResponse<boolean>>(
      `api/dynamic-assets/asset-types/${assetTypeId}/relationships/${assetTypeRelationshipId}`,
      {
        method: 'DELETE',
      },
    )

    assetTypeRelationships.value = assetTypeRelationships.value.filter(relationship =>
      relationship.id !== assetTypeRelationshipId,
    )
  }

  // Private functions

  function applyFilters(
    data: AssetTypeRelationshipData[],
    filters: RelationshipFilter[],
  ): AssetTypeRelationshipData[] {
    return data.filter((item) => {
      return filters.every((filter) => {
        if (Array.isArray(filter.value)) {
          return filter.value.includes(item[filter.field])
        }

        return item[filter.field] === filter.value
      })
    })
  }

  return {
    assetTypeRelationships,

    getAll,
    getById,
    getByAssetTypeId,

    hydrate,
    create,
    destroy,
    update,
  }
})
