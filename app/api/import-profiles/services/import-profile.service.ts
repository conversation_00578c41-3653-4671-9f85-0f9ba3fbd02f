import type { DirectoryData, ImportProfileData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { createImportProfileApiSchema } from '~/schemas/import-profiles/create-import-profile.schema'
import { updateImportProfileConfigurationSchema } from '~/schemas/import-profiles/update-import-profile-configuration.schema'
import { updateImportProfileGeneralInfoSchema } from '~/schemas/import-profiles/update-import-profile-general-info.schema'
import { updateImportProfileMappingSchema } from '~/schemas/import-profiles/update-import-profile-mapping.schema'
import { updateImportProfileScheduleSchema } from '~/schemas/import-profiles/update-import-profile-schedule.schema'
import { useApiClient } from '~/utils/api/api-client'
import { transformOrThrow } from '~/utils/zod/zod-transformer'
import { IMPORT_PROFILE_API_ROUTES } from './import-profile-service.routes'

export class ImportProfileService {
  public static async getAll(): Promise<ImportProfileData[]> {
    const response = await useApiClient()<ApiResponse<ImportProfileData[]>>(
      IMPORT_PROFILE_API_ROUTES.ALL,
    )

    return response.data
  }

  public static async getById(id: string): Promise<ImportProfileData> {
    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.BY_ID(id),
    )

    return response.data
  }

  public static async getByIdOrFail(id: string): Promise<ImportProfileData> {
    const profile = await this.getById(id)

    if (!profile) {
      throw new Error(`Import profile with ID ${id} not found`)
    }

    return profile
  }

  public static async create(data: ImportProfileFormData): Promise<ImportProfileData> {
    const parsedData = transformOrThrow(createImportProfileApiSchema, data)

    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.ALL,
      {
        method: 'POST',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateGeneralInfo(
    id: string,
    data: ImportProfileFormData,
  ): Promise<ImportProfileData> {
    const parsedData = transformOrThrow(updateImportProfileGeneralInfoSchema, data)

    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.UPDATE_GENERAL_INFO(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateConfiguration(
    id: string,
    data: ImportProfileFormData,
  ): Promise<ImportProfileData> {
    const parsedData = transformOrThrow(updateImportProfileConfigurationSchema, data)

    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.UPDATE_CONFIGURATION(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateMapping(
    id: string,
    data: ImportProfileFormData,
  ): Promise<ImportProfileData> {
    const parsedData = transformOrThrow(updateImportProfileMappingSchema, data)

    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.UPDATE_MAPPING(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async updateSchedule(
    id: string,
    data: ImportProfileFormData,
  ): Promise<ImportProfileData> {
    const parsedData = transformOrThrow(updateImportProfileScheduleSchema, data)

    const response = await useApiClient()<ApiResponse<ImportProfileData>>(
      IMPORT_PROFILE_API_ROUTES.UPDATE_SCHEDULE(id),
      {
        method: 'PUT',
        body: parsedData,
      },
    )

    return response.data
  }

  public static async delete(id: string): Promise<void> {
    await useApiClient()(
      IMPORT_PROFILE_API_ROUTES.BY_ID(id),
      {
        method: 'DELETE',
      },
    )
  }

  public static async files(
    id: string,
    path: string,
  ): Promise<DirectoryData> {
    const response = await useApiClient()<ApiResponse<DirectoryData>>(
      IMPORT_PROFILE_API_ROUTES.FILES(id),
      {
        params: { path },
      },
    )

    return response.data
  }

  public static async records(id: string): Promise<any[]> {
    const response = await useApiClient()<ApiResponse<any[]>>(
      IMPORT_PROFILE_API_ROUTES.RECORDS(id),
    )

    return response.data
  }

  public static async run(id: string): Promise<void> {
    await useApiClient()(
      IMPORT_PROFILE_API_ROUTES.RUN(id),
      {
        method: 'POST',
      },
    )
  }
}
