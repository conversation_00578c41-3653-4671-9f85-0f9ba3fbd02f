import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { batchInvalidateKeys } from '~/utils/vue-query/batch-invalidate-keys'
import { exportProfileKeys } from '../keys/export-profile.keys'
import { ExportProfileService } from '../services/export-profile.service'

export function useCreateExportProfileMutation() {
  return useMutation({
    mutationFn: (data: ExportProfileFormData) => ExportProfileService.create(data),
    onSuccess: () => {
      batchInvalidateKeys(queryClient, [
        exportProfileKeys.all,
      ])
    },
  })
}

export function useUpdateExportProfileGeneralInfoMutation() {
  return useMutation({
    mutationFn: ({ exportProfileId, data }: { exportProfileId: string, data: ExportProfileFormData }) =>
      ExportProfileService.updateGeneralInfo(exportProfileId, data),
    onSuccess: (_, { exportProfileId }) => {
      batchInvalidateKeys(queryClient, [
        exportProfileKeys.all,
        exportProfileKeys.byId(exportProfileId),
        exportProfileKeys.byIdOrFail(exportProfileId),
      ])
    },
  })
}

export function useUpdateExportProfilePathMutation() {
  return useMutation({
    mutationFn: ({ exportProfileId, data }: { exportProfileId: string, data: ExportProfileFormData }) =>
      ExportProfileService.updatePath(exportProfileId, data),
    onSuccess: (_, { exportProfileId }) => {
      batchInvalidateKeys(queryClient, [
        exportProfileKeys.all,
        exportProfileKeys.byId(exportProfileId),
        exportProfileKeys.byIdOrFail(exportProfileId),
      ])
    },
  })
}

export function useUpdateExportProfileTemplateMutation() {
  return useMutation({
    mutationFn: ({ exportProfileId, data }: { exportProfileId: string, data: ExportProfileFormData }) =>
      ExportProfileService.updateTemplate(exportProfileId, data),
    onSuccess: (_, { exportProfileId }) => {
      batchInvalidateKeys(queryClient, [
        exportProfileKeys.all,
        exportProfileKeys.byId(exportProfileId),
        exportProfileKeys.byIdOrFail(exportProfileId),
      ])
    },
  })
}

export function useDeleteExportProfileMutation() {
  return useMutation({
    mutationFn: (exportProfileId: string) => ExportProfileService.delete(exportProfileId),
    onSuccess: (_, exportProfileId) => {
      batchInvalidateKeys(queryClient, [
        exportProfileKeys.all,
        exportProfileKeys.byId(exportProfileId),
        exportProfileKeys.byIdOrFail(exportProfileId),
      ])
    },
  })
}
