import type { ColumnDef } from '@tanstack/vue-table'
import { TablesRendererTypesError } from '#components'
import { BaseColumn } from '~/utils/table/BaseColumn'
import { ColumnType } from '~/utils/types/table/ColumnType'

export class ErrorColumn<T> extends BaseColumn<T> {
  constructor(
    label: string,
    private hasErrorCallback: (obj: T) => boolean,
  ) {
    super(label, false, ColumnType.Checkbox)
  }

  getConfig(): ColumnDef<T> {
    return {
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesError, {
        hasError: this.hasErrorCallback(row.original),
      }),
    }
  }
}
