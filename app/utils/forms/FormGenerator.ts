import type { AssetTypeFieldData, AssetTypeFormFieldData } from '~/utils/types/api/api'
import { FormDefinition } from '~/utils/forms/FormDefinition'
import { FormField } from '~/utils/forms/FormField'
import { FormFieldExtra } from '~/utils/forms/FormFieldExtra'
import { generateNameForRelationship } from '../relationship/relationship-name-generator'

export class FormGenerator {
  private assetTypeRelationshipStore
  private assetTypeFields: AssetTypeFieldData[]

  constructor(
    assetTypeFields: AssetTypeFieldData[],
  ) {
    this.assetTypeRelationshipStore = useAssetTypeRelationshipStore()
    this.assetTypeFields = assetTypeFields
  }

  public generateFormDefinition(formFields: AssetTypeFormFieldData[]): FormDefinition {
    const fields = formFields.map((field) => {
      return this.createFormField(field)
    })

    return new FormDefinition(fields)
  }

  private getAssetTypeFieldById(id: string): AssetTypeFieldData | undefined {
    return this.assetTypeFields.find(field => field.id === id) || undefined
  }

  private createFormField(formField: AssetTypeFormFieldData): FormField {
    let label = ''
    let name = ''

    if (formField.asset_type_field_id) {
      const field = this.getAssetTypeFieldById(formField.asset_type_field_id)
      if (field) {
        label = field.label
        name = field.field
      }
    }

    if (formField.asset_type_relationship_id) {
      const relationship = this.assetTypeRelationshipStore.getById(formField.asset_type_relationship_id)
      if (relationship) {
        label = relationship.label
        name = generateNameForRelationship(relationship)
      }
    }

    return new FormField(
      formField.type,
      name,
      label,
      formField.span,
      formField.crud,
      new FormFieldExtra(undefined, formField),
      formField.id,
    )
  }
}
