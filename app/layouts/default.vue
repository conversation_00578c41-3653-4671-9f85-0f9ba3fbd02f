<script lang="ts" setup>
import UnsupportedResolution from '~/components/general/UnsupportedResolution.vue'
import { useScreenRequirement } from '~/composables/utils/useScreenRequirement'

const { showResolutionWarning } = useScreenRequirement()
</script>

<template>
  <div class="h-screen flex flex-col">
    <AppHeader />
    <div class="flex-1">
      <slot />
    </div>
    <UnsupportedResolution v-if="showResolutionWarning" />
  </div>
</template>
