<script setup lang="ts">
import type { GeneralOverflow } from '#components'
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { ModalsConfirmModal } from '#components'
import { computed } from 'vue'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { useAssetTypeFormFieldsByAssetTypeQuery } from '~/api/asset-type-form-fields'
import { FormGenerator } from '~/utils/forms/FormGenerator'

const props = defineProps<FormInputProps>()

const model = defineModel({
  type: Array as PropType<any[]>,
  default: () => [],
})
const overlay = useOverlay()

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const selectedId = ref<string | undefined>()
const loading = ref<boolean>(true)
const overflowRef = ref<InstanceType<typeof GeneralOverflow> | null>(null)

const relationship = assetTypeRelationshipStore.getById(props.field.extra?.parentField?.asset_type_relationship_id ?? '')
const assetType = assetTypeStore.getById(relationship?.relationship_asset_type_id ?? '')

const { data: formFields } = useAssetTypeFormFieldsByAssetTypeQuery(assetType.id)
const { data: assetTypeFields } = useAssetTypeFieldsQuery()

const formDefinition = computed<FormDefinition>(() => {
  return new FormGenerator(assetTypeFields.value ?? [])
    .generateFormDefinition(formFields.value ?? [])
})

const confirmModal = overlay.create(ModalsConfirmModal)

onMounted(() => {
  if (model.value.length > 0) {
    selectedId.value = model.value[0].id
  }

  loading.value = false
})

async function addItem() {
  const emptyEntry = await assetTypeStore.emptyEntry(assetType?.id ?? '')
  model.value.push(emptyEntry.data)
  selectedId.value = emptyEntry.data.id
  overflowRef.value?.rerender()
}

async function onDelete(tab: any) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  const proxy = model.value.filter(entry => entry.id !== tab.item.value)
  selectedId.value = proxy.length > 0 ? proxy[0].id : undefined
  model.value = proxy
  overflowRef.value?.rerender()
}

const tabItems = computed(() => {
  return model.value.map((item, index) => ({
    value: item.id,
    label: `Delivery ${index + 1}`,
    hasError: Object.keys(props.errors).some(key => key.startsWith(`${props.field.name}.${index}.`)),
  }))
})

const selectedItem = computed(() => {
  if (!selectedId.value)
    return undefined
  return model.value.find(item => item.id === selectedId.value)
})

const selectedZeroBasedIndex = computed(() => {
  if (!selectedId.value)
    return undefined
  return model.value.findIndex(item => item.id === selectedId.value)
})

const fieldErrors = computed(() => {
  const startWith = `${props.field.name}.${selectedZeroBasedIndex.value}.`
  return Object.fromEntries(
    Object.entries(props.errors)
      .filter(([key]) => key.startsWith(startWith))
      .map(([key, value]) => [key.substring(startWith.length), value]),
  )
})
</script>

<template>
  <div v-if="!loading">
    <div class="flex justify-between border-b border-solid text-center my-2">
      <h2 class="font-bold text-lg pb-2">
        {{ relationship?.label }}
      </h2>
      <ULink v-if="!isReadOnly" class="text-sm" @click="addItem">
        Add {{ relationship?.label }}
      </ULink>
    </div>
    <GeneralOverflow ref="overflowRef" bg-color="white">
      <UTabs
        v-model="selectedId"
        :items="tabItems"
        variant="link"
      >
        <template #leading="item">
          <UIcon v-if="item.item.hasError" size="large" class="white" name="i-ph-warning" />
        </template>
        <template v-if="!isReadOnly" #trailing="item">
          <ULink class="flex items-center cursor-pointer" @click="onDelete(item)">
            <UIcon name="i-ph-x-circle" />
          </ULink>
        </template>
      </UTabs>
    </GeneralOverflow>
    <FormsRendererForm
      v-if="formDefinition && selectedItem"
      :key="selectedItem.id"
      v-model="selectedItem"
      :form-definition="formDefinition"
      :is-sub-form="true"
      :errors="fieldErrors"
      :is-read-only="isReadOnly"
    />
  </div>
</template>
