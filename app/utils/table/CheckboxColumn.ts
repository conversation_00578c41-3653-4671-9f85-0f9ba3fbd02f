import type { ColumnDef } from '@tanstack/vue-table'
import { TablesRendererTypesCheckBox } from '#components'
import { ColumnType } from '~/utils/types/table/ColumnType'
import { BaseColumnWithField } from './BaseColumnWithField'

export class CheckboxColumn<T> extends BaseColumnWithField<T> {
  constructor(
    field: keyof T,
    label: string,
      sortable: boolean = false,
  ) {
    super(field, label, sortable, ColumnType.Checkbox)
  }

  override getField(obj: T): string {
    return super.getField(obj)
  }

  getConfig(): ColumnDef<T> {
    return {
      accessorKey: this.field as string,
      header: this.label,
      enableSorting: this.sortable,
      cell: ({ row }) => h(TablesRendererTypesCheckBox, {
        checked: !!this.getField(row.original),
        disabled: true,
      }),
    }
  }
}
