import { z } from 'zod'
import { FtpEncryption, FtpProtocol } from '~/utils/types/api/api'

export const ftpIntegrationFormSchema = z.object({
  label: z.string().min(1, 'Label is required'),
  data: z.object({
    ftp_protocol: z.nativeEnum(FtpProtocol),
    ftp_host: z.string().min(1, 'Host is required'),
    ftp_port: z.number().min(1, 'Port must be greater than 0').max(65535, 'Port must be less than 65536'),
    ftp_username: z.string().min(1, 'Username is required'),
    ftp_password: z.string().nullable(),
    ftp_encryption: z.nativeEnum(FtpEncryption),
    ftp_private_key: z.string().nullable(),
    ftp_passphrase: z.string().nullable(),
    ftp_passive_mode: z.boolean(),
    base_folder: z.string().min(1, 'Base folder is required'),
  }).superRefine((data, ctx) => {
    if (data.ftp_protocol === FtpProtocol.FTP) {
      if (!data.ftp_password || data.ftp_password.trim() === '') {
        ctx.addIssue({
          path: ['ftp_password'],
          code: z.ZodIssueCode.custom,
          message: 'Password is required when protocol is FTP',
        })
      }
    }

    if (data.ftp_protocol === FtpProtocol.SFTP) {
      if ((!data.ftp_password || data.ftp_password.trim() === '')
        && (!data.ftp_private_key || data.ftp_private_key.trim() === '')) {
        ctx.addIssue({
          path: ['ftp_password'],
          code: z.ZodIssueCode.custom,
          message: 'Either password or private key is required when protocol is SFTP',
        })
        ctx.addIssue({
          path: ['ftp_private_key'],
          code: z.ZodIssueCode.custom,
          message: 'Either password or private key is required when protocol is SFTP',
        })
      }
    }
  }),
})

export type FtpIntegrationForm = z.infer<typeof ftpIntegrationFormSchema>
