import { ref, watch } from 'vue'
import { AssetQueryFactory } from '~/api/assets'

/**
 * A composable that provides access to the dynamic asset query system.
 * This allows components to interact with assets from any table without
 * needing to know the specific implementation details.
 *
 * @param tableName - The table name for the asset type as a string or ref
 * @returns An object containing keys, queries, and mutations for the asset type
 */
export function useAssetQueryProvider(tableName: string | Ref<string>) {
  // Handle both string and ref inputs
  const table = ref(typeof tableName === 'string' ? tableName : tableName.value)

  // If tableName is a ref, watch for changes
  if (typeof tableName !== 'string') {
    watch(tableName, (newTable) => {
      table.value = newTable
    })
  }

  // Get the asset instance from the factory
  const assetInstance = computed(() => {
    if (!table.value)
      return null
    return AssetQueryFactory.getQueryInstance(table.value)
  })

  // Check if the instance is valid
  const isReady = computed(() => !!assetInstance.value)

  // Helper method to simplify common query operations
  const withAssetId = (assetId: string) => {
    if (!assetInstance.value)
      return {}

    const keys = assetInstance.value.getKeys()
    const queries = assetInstance.value.getQueries()
    const mutations = assetInstance.value.getMutations()

    return {
      keys,
      queries,
      mutations,
      getAsset: () => queries.useAssetQuery(assetId),
      updateAsset: (data: Record<string, any>) =>
        mutations.useUpdateAssetMutation().mutateAsync({ id: assetId, data }),
      deleteAsset: () =>
        mutations.useDeleteAssetMutation().mutateAsync(assetId),
    }
  }

  return {
    isReady,
    table: readonly(table),
    keys: computed(() => assetInstance.value?.getKeys()),
    queries: computed(() => assetInstance.value?.getQueries()),
    mutations: computed(() => assetInstance.value?.getMutations()),
    withAssetId,
  }
}
