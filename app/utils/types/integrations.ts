import type { IntegrationData, StatusMessage } from './api/api'

export interface BaseIntegrationFormProps {
  integrationId?: string
  isCreateMode: boolean
  integration?: IntegrationData
  isSubmitting: boolean
}

export function getTestResultAlertProps(testResult?: StatusMessage) {
  if (!testResult?.status)
    return null

  const status = testResult.status
  return {
    color: status === 'Success' ? 'success' as const : status === 'Warning' ? 'warning' as const : 'error' as const,
    icon: status === 'Success' ? 'i-ph-check-circle' : status === 'Warning' ? 'i-ph-warning-circle' : 'i-ph-x-circle',
    title: status === 'Success' ? 'Test Successful' : status === 'Warning' ? 'Test Warning' : 'Test Failed',
  }
}
