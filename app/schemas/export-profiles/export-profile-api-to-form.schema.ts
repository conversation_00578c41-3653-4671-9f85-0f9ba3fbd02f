import type { ExportProfileFormData } from '~/utils/types/export-profile/export-profile-form.type'
import { z } from 'zod'

export const exportProfileDataSchema = z.object({
  id: z.string().uuid(),
  status: z.string(),
  name: z.string(),
  integration_id: z.string().uuid(),
  path: z.string().nullable(),
  file_name: z.string().nullable(),
  template: z.string().nullable(),
  crlf: z.boolean(),
})

export const exportProfileDataToFormSchema = exportProfileDataSchema.transform((data): ExportProfileFormData => ({
  name: data.name,
  status: data.status,
  integration_id: data.integration_id,
  path: data.path ?? '/',
  file_name: data.file_name ?? '',
  template: data.template ?? '',
  crlf: data.crlf ?? false,
}))
