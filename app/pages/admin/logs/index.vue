<script setup lang="ts">
import type { LogData } from '~/utils/types/api/api'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const toast = useToast()
const dayjs = useDayjs()

const logStore = useLogStore()

const loading = ref(true)
const data = ref()
const openItems = ref<string[]>([])

onMounted(async () => {
  await fetchLogs()
})

async function fetchLogs() {
  loading.value = true
  try {
    data.value = await logStore.fetchAll()
  }
  catch (error: any) {
    toast.add({
      title: 'Failed to fetch logs',
      description: error.data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}

function getAccordionItems() {
  if (!data.value?.data)
    return []

  return data.value.data.map((log: LogData) => ({
    id: log.id.toString(),
    label: `[${getDate(log.created_at)}] - [${log.module}]  -  [${log.service}]: ${log.reason}`,
    icon: log.severity === 'error' ? 'i-ph-warning' : 'i-ph-info',
    content: log.technical_reason,
  }))
}

function getDate(time: string): string {
  return dayjs(time).tz().format('DD-MM-YYYY HH:mm')
}

const accordionItems = computed(() => getAccordionItems())
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="System Logs">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="!loading" #body>
      <UAccordion
        v-model="openItems"
        :items="accordionItems"
        type="multiple"
      />
      <div class="flex items-center justify-between gap-3 border-t border-(--ui-border) pt-4 mt-auto">
        <div class="text-sm text-(--ui-text-muted)">
          <GeneralEntries :meta-data="data?.meta" />
        </div>
        <div class="flex items-center gap-1.5">
          <UPagination
            v-model="data.meta.current_page"
            :page-count="data.meta.last_page"
            :items-per-page="data.meta.per_page"
            :total="data.meta.total"
            @update:model-value="fetchLogs"
          />
        </div>
      </div>
    </template>
    <template v-else #body>
      <div class="flex justify-center p-8">
        <UIcon name="i-ph-spinner-gap" class="animate-spin h-8 w-8" />
      </div>
    </template>
  </UDashboardPanel>
</template>
