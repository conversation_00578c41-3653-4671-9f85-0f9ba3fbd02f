import type { LogData } from '~/utils/types/api/api'
import type { Meta, PaginatedApiResponse } from '~/utils/types/api/api-response'
import { useApiClient } from '~/utils/api/api-client'
import QueryData from '~/utils/query/QueryData'

export const useLogStore = defineStore('log-store', () => {
  const logs = ref<LogData[]>([])
  const meta = ref<Meta | null>(null)

  async function fetchAll(queryData?: QueryData) {
    queryData = queryData ?? new QueryData()

    const response = await useApiClient()<PaginatedApiResponse<LogData[]>>(
      `api/log/logs?${queryData.toQueryString()}`,
    )

    logs.value = response.data
    meta.value = response.meta

    return response
  }

  return {
    logs,
    meta,
    fetchAll,
  }
})
