<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { UpdateUserData, UserData } from '~/utils/types/api/api'
import { useUpdateUserMutation } from '~/api/user/mutations/useUserMutations'

const props = defineProps({
  user: {
    type: Object as PropType<UserData>,
    required: true,
  },
})
const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()

const isPending = ref(false)
const data = ref<UpdateUserData>({
  first_name: props.user.first_name,
  last_name: props.user.last_name,
  password: undefined,
})

const updateUserMutation = useUpdateUserMutation()
async function onSubmit() {
  try {
    isPending.value = true
    await updateUserMutation.mutateAsync({ userId: props.user.id, data: data.value })
    toast.add({ title: 'User updated', color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: 'Error updating the user',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover
    title="Edit user"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Email" class="col-span-12">
            <UInput :model-value="user.email" type="email" placeholder="<EMAIL>" readonly />
          </UFormField>
          <UFormField label="First name" class="col-span-6">
            <UInput v-model="data.first_name" placeholder="john" />
          </UFormField>
          <UFormField label="Last name" class="col-span-6">
            <UInput v-model="data.last_name" placeholder="doe" />
          </UFormField>
          <UFormField label="Password" class="col-span-12">
            <GeneralPasswordInput v-model="data.password" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          Update
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
