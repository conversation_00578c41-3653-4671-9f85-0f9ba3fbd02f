import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory'
import { AssetTypeIndexCacheService } from '../services/asset-type-index-cache.service'
import { AssetTypeIndexService } from '../services/asset-type-index.service'

export const assetTypeIndexKeys = createQueryKeys('assetTypeIndexes', {
  all: {
    queryKey: null,
    queryFn: () => AssetTypeIndexService.getAll(),
  },
  byAssetTypeId: (assetTypeId: string) => ({
    queryKey: [assetTypeId],
    queryFn: () => AssetTypeIndexCacheService.getByAssetTypeId(assetTypeId),
  }),
  byAssetTypeFieldId: (assetTypeFieldId: string) => ({
    queryKey: [assetTypeFieldId],
    queryFn: () => AssetTypeIndexCacheService.getByAssetTypeFieldId(assetTypeFieldId),
  }),
})
