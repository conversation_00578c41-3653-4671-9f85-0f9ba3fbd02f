# Table System Working Guide

## Architecture Overview

The table system is a sophisticated data display and editing framework built on **TanStack Vue Table** with a dynamic cell component system.

### Core Components
```
tables/
├── DataTable.vue           # Main table engine (TanStack integration)
├── DefaultHeader.vue       # Standard column headers
├── MappingHeader.vue       # Import mapping headers with dropdowns
├── TableSkeleton.vue       # Loading skeleton
├── table-cells/           # Cell component system (13 types)
└── renderer/              # Advanced table rendering system
```

## DataTable.vue - Core Engine

### Props Interface
```typescript
const props = withDefaults(defineProps<{
  columnsMeta: ColumnMeta[] // Column definitions
  data: Record<string, any>[] // Table data
  rowErrors?: Record<number, string[]> // Row-level error highlighting
  editable?: boolean // Enable inline editing
  sortable?: boolean // Enable column sorting
  mappingMeta?: MappingItemData[] // For import mapping use case
  mappingOptions?: { label: string, value: string }[] // Mapping dropdown options
}>(), {
  editable: false,
  sortable: false,
})
```

### Events
```typescript
const emit = defineEmits<{
  cellUpdate: [payload: { rowIndex: number, columnKey: string, value: any }]
  updateMapping: [payload: { from: string, to: string, skip: boolean, mapping: any }]
}>()
```

### Key Features

#### Dynamic Cell Rendering
```typescript
// DataTable dynamically selects view/edit components
function getCellComponent(columnType: ColumnType, isEditable: boolean) {
  const componentSet = columnComponentMap[columnType]
  return markRaw(
    isEditable && 'edit' in componentSet
      ? componentSet.edit // EditableTextCell, EditableNumericCell, etc.
      : componentSet.view // TextCell, NumericCell, etc.
  )
}
```

#### Column Weight System
```typescript
// Responsive column sizing based on weight
const columnWidths = computed(() => {
  const totalWeight = columns.reduce((sum, col) => sum + (col.weight ?? 1), 0)
  return columns.map((col) => {
    const weight = col.weight ?? 1
    if (weight === 0)
      return 'min-content' // Fixed width columns
    const percentage = (weight / totalWeight) * 100
    return `${percentage}%`
  })
})
```

#### Path-based Data Access
```typescript
// Supports nested object access like 'user.profile.name'
function getValueFromPath(obj: any, path: string): any {
  if (Object.prototype.hasOwnProperty.call(obj, path)) {
    return obj[path] // Direct property
  }

  return path.split('.').reduce((current, key) => {
    return current && typeof current === 'object' && key in current
      ? current[key] // Nested property
      : undefined
  }, obj)
}
```

## Cell Component System

### Available Cell Types (ColumnType enum)
```typescript
enum ColumnType {
  Text = 'text', // ✅ TextCell + EditableTextCell
  Numeric = 'numeric', // ✅ NumericCell + EditableNumericCell
  Badge = 'badge', // ✅ BadgeCell (view only)
  Date = 'date', // ✅ DateCell + EditableDateCell
  DateTime = 'datetime', // ✅ DateTimeCell + EditableDateTimeCell
  Actions = 'actions', // ✅ ActionsCell (view only)
  Relationship = 'relationship', // ✅ RelationshipCell + EditableRelationshipCell
  Error = 'error', // ✅ ErrorCell (view only)
  Checkbox = 'checkbox', // 🚧 Placeholder (not implemented)
  Enum = 'enum', // 🚧 Placeholder (not implemented)
  Image = 'image', // 🚧 Placeholder (not implemented)
  Copy = 'copy', // 🚧 Placeholder (not implemented)
}
```

### Cell Component Props Interface
```typescript
// Standard props for all cell components
interface CellProps {
  value: any // Cell value
  row?: Record<string, any> // Full row data (for complex cells)
  config?: any // Column-specific configuration
  rowIndex?: number // Row position (for editing)
  columnKey?: string // Column identifier (for editing)
}

// Editable cells add update event
interface EditableCellProps extends CellProps {
  onUpdate?: (value: any) => void
}
```

### Cell Component Examples

#### Simple View Cell
```vue
<!-- TextCell.vue -->
<script setup lang="ts">
defineProps<{
  value: string | null | undefined
}>()
</script>

<template>
  <span>{{ value }}</span>
</template>
```

#### Editable Cell with Two-Way Binding
```vue
<!-- EditableTextCell.vue -->
<script setup lang="ts">
const props = defineProps<{
  value: string | null | undefined
}>()

const emit = defineEmits<{
  update: [value: string | null | undefined]
}>()

const model = ref(props.value)

// Sync prop changes to local model
watch(() => props.value, (val) => {
  model.value = val
})

// Emit changes from local model
watch(model, (val) => {
  emit('update', val)
})
</script>

<template>
  <UInput v-model="model" class="input input-sm w-full" />
</template>
```

#### Complex Relationship Cell (Vue Query Integration)
```vue
<!-- RelationshipCell.vue -->
<script setup lang="ts">
const props = defineProps<{
  value: string | null | undefined
  row: Record<string, any>
  config?: {
    relationship?: {
      asset_type_relationship_id: string
      asset_type_field_id: string
    }
  }
}>()

// Legacy Pinia usage (being migrated)
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const assetTypeStore = useAssetTypeStore()

// Modern Vue Query usage
const { useAssetQuery } = AssetQueryFactory
  .getQueryInstance(assetType.value.table_name)
  .getQueries()

const { data: assetData, isFetching } = useAssetQuery(relationshipId, {
  enabled: !!relationshipId,
})

// Complex display logic with async data resolution
function getDisplayFieldName() {
  if (!displayField.value || !assetData.value) {
    return 'n/a'
  }
  return assetData.value[displayField.value] ?? 'n/a'
}
</script>

<template>
  <span v-if="!isFetching">{{ displayFieldName }}</span>
</template>
```

## Column Configuration System

### ColumnMeta Interface
```typescript
interface ColumnMeta {
  id: string // Unique identifier
  key: string // Data property path (supports 'nested.property')
  label: string // Display name
  type: ColumnType // Cell component type
  sortable?: boolean // Enable sorting
  editable?: boolean // Enable inline editing
  weight: number // Column width weight (0 = min-content)
  config?: { // Type-specific configuration
    suffix?: string | null // Display suffix (%, $, etc.)
    filterable?: boolean // Enable filtering
    relationship?: { // For relationship columns
      asset_type_relationship_id: string
      asset_type_field_id: string
      display_field: string
    }
    buttons?: { // For action columns
      label: string
      onClick: (row: Record<string, any>) => void
    }[]
    hasError?: (row: any) => boolean // For error columns
    [key: string]: unknown // Extensible config
  }
}
```

### Column Generation Utilities

#### From Database Fields
```typescript
// Generate columns from asset type field definitions
function generateColumnsMetaFromFields(fields: AssetTypeTableFieldData[]): ColumnMeta[] {
  return fields.map((field) => {
    const type = getCellType(field) // Maps field type to ColumnType
    const { id, key } = getIdAndKey(field)

    const base: ColumnMeta = {
      id,
      key,
      label: field.label,
      type,
      editable: true,
      sortable: true,
      weight: field.weight,
      config: {
        suffix: field.suffix,
        filterable: field.filterable,
        field: { template: field.template },
      },
    }

    // Add relationship configuration
    if (field.asset_type_relationship_id) {
      base.config.relationship = {
        asset_type_relationship_id: field.asset_type_relationship_id,
        asset_type_field_id: field.asset_type_field_id,
        display_field: AssetTypeFieldCacheService.getByIdOrFail(field.asset_type_field_id).field,
      }
    }

    return base
  })
}
```

#### From Raw Data (Import Mapping)
```typescript
// Generate columns from data structure (for mapping)
function generateColumnsMetaFromRows(rows: Record<string, any>[]): ColumnMeta[] {
  const allKeys = new Set<string>()

  // Extract all possible keys from data
  for (const row of rows) {
    for (const key of Object.keys(row)) {
      allKeys.add(key)
    }
  }

  return Array.from(allKeys).map((key): ColumnMeta => ({
    id: key,
    key,
    label: key,
    type: ColumnType.Text, // Default to text for raw data
    editable: false,
    sortable: false,
    weight: 1,
    config: { type: ColumnType.Text },
  }))
}
```

#### Special Purpose Columns
```typescript
// Actions column with custom buttons
function generateActionsColumn(
  actions: { label: string, onClick: (row: Record<string, any>) => void }[],
): ColumnMeta {
  return {
    id: 'actions',
    key: 'actions',
    label: 'Actions',
    type: ColumnType.Actions,
    weight: 1,
    config: { buttons: actions },
  }
}

// Error indicator column
function generateErrorColumn(hasError: (row: any) => boolean): ColumnMeta {
  return {
    id: 'error',
    key: 'error',
    label: '',
    type: ColumnType.Error,
    weight: 0, // Minimal width
    config: { hasError },
  }
}
```

## Real-World Usage Patterns

### 1. Basic Data Display
```vue
<script setup lang="ts">
const columnsMeta: ColumnMeta[] = [
  {
    id: 'name',
    key: 'name',
    label: 'Name',
    type: ColumnType.Text,
    sortable: true,
    weight: 2,
  },
  {
    id: 'email',
    key: 'email',
    label: 'Email',
    type: ColumnType.Text,
    sortable: true,
    weight: 3,
  }
]
</script>

<template>
  <TablesDataTable
    :columns-meta="columnsMeta"
    :data="tableData"
    :sortable="true"
  />
</template>
```

### 2. Inline Editing
```vue
<script setup lang="ts">
function handleCellUpdate(payload: { rowIndex: number, columnKey: string, value: any }) {
  // Update your data source
  tableData.value[payload.rowIndex][payload.columnKey] = payload.value

  // Trigger save mutation
  saveDataMutation.mutate({
    id: tableData.value[payload.rowIndex].id,
    [payload.columnKey]: payload.value
  })
}
</script>

<template>
  <TablesDataTable
    :columns-meta="columnsMeta"
    :data="tableData"
    :editable="true"
    @cell-update="handleCellUpdate"
  />
</template>
```

### 3. Import Field Mapping
```vue
<script setup lang="ts">
// Raw import data columns
const columnsMeta = computed(() =>
  generateColumnsMetaFromRows(importData.value ?? [])
)

// Field mapping configuration
const mappingConfig = ref<MappingItemData[]>([
  { from: 'csv_column_1', to: 'database_field_id', skip: false },
  { from: 'csv_column_2', to: '', skip: true },
])

// Available target fields
const availableFields = [
  { label: 'Product Name', value: 'field_1' },
  { label: 'Price', value: 'field_2' },
]
</script>

<template>
  <TablesDataTable
    :columns-meta="columnsMeta"
    :data="importData"
    :mapping-meta="mappingConfig"
    :mapping-options="availableFields"
    @update-mapping="handleMappingUpdate"
  />
</template>
```

### 4. Error Highlighting
```vue
<script setup lang="ts">
// Add error indicator column
const errorColumn = generateErrorColumn(row =>
  validationErrors.value[row.index]?.length > 0
)

// Row-level validation errors
const validationErrors = ref<Record<number, string[]>>({
  0: ['Name is required'],
  2: ['Invalid email format', 'Phone number required']
})
</script>

<template>
  <TablesDataTable
    :columns-meta="[...columnsMeta, errorColumn]"
    :data="tableData"
    :row-errors="validationErrors"
  />
</template>
```

## Advanced Features

### TanStack Table Integration
- **Core functions**: `getCoreRowModel()`, `getSortedRowModel()`
- **Dynamic options**: Table options update when props change
- **Performance**: Uses `markRaw()` for component caching

### Component Mapping System
- **Lazy loading**: `defineAsyncComponent()` for cell components
- **View/Edit pairs**: Automatically selects based on `editable` prop
- **Fallback handling**: Placeholder component for unimplemented types

### State Management Integration
- **Legacy Pinia**: Existing relationship/asset type data
- **Modern Vue Query**: New data fetching (see RelationshipCell example)
- **Mixed usage**: Both patterns coexist during migration

## Creating New Cell Components

### 1. Add ColumnType
```typescript
// utils/types/table/ColumnType.ts
export enum ColumnType {
  // ... existing types
  YourNewType = 'your-new-type',
}
```

### 2. Create Cell Components
```vue
<!-- components/tables/table-cells/YourNewTypeCell.vue -->
<script setup lang="ts">
defineProps<{
  value: YourDataType
  config?: YourConfigType
}>()
</script>

<template>
  <div>
    <!-- Your display logic -->
  </div>
</template>
```

```vue
<!-- components/tables/table-cells/EditableYourNewTypeCell.vue -->
<script setup lang="ts">
const props = defineProps<{
  value: YourDataType
  config?: YourConfigType
}>()

const emit = defineEmits<{
  update: [value: YourDataType]
}>()

// Implement editing logic
</script>
```

### 3. Register in Component Map
```typescript
// composables/table/useColumnComponentMap.ts
export function useColumnComponentMap() {
  return {
    // ... existing mappings
    [ColumnType.YourNewType]: {
      view: resolve('YourNewTypeCell'),
      edit: resolve('EditableYourNewTypeCell'), // Optional
    },
  }
}
```

### 4. Use in Column Configuration
```typescript
const columnMeta: ColumnMeta = {
  id: 'your-field',
  key: 'your_field',
  label: 'Your Field',
  type: ColumnType.YourNewType,
  editable: true,
  config: {
    // Your custom configuration
  },
}
```

## Performance Considerations

### Component Caching
- Use `markRaw()` for component references to prevent reactivity overhead
- `defineAsyncComponent()` enables code splitting for cell components

### Data Handling
- Avoid deep watching large datasets
- Use `toRef()` for TanStack Table data binding
- Implement proper `enabled` conditions for async cells

### Memory Management
- Complex cells like RelationshipCell cache queries appropriately
- Clean up watchers in cell components
- Use proper TypeScript types to prevent unnecessary re-renders

## Common Patterns to Avoid

### ❌ Don't Create Custom Table Components
```typescript
// ❌ Wrong - creates maintenance overhead
// <MyCustomTable />

// ✅ Correct - use DataTable with configuration
// <TablesDataTable :columns-meta="myColumns" :data="myData" />
```

### ❌ Don't Bypass Cell Component System
```html
<!-- ❌ Wrong - inline rendering -->
<template #cell="{ value }">
  <span>{{ formatValue(value) }}</span>
</template>

<!-- ✅ Correct - create proper cell component -->
<YourNewTypeCell :value="value" :config="config" />
```

### ❌ Don't Mix State Management Approaches in New Code
```typescript
// ❌ Wrong - using Pinia in new cell components
const store = useYourStore()

// ✅ Correct - use Vue Query for new components
const { data } = useYourDataQuery()
```
