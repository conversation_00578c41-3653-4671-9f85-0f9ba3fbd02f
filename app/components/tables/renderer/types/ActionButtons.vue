<script setup lang="ts">
import type { Action } from '~/utils/table/ActionColumn'

const props = defineProps({
  actions: {
    type: Array as PropType<Action<any>[]>,
    required: true,
  },
  row: {
    type: Object as PropType<any>,
    required: true,
  },
})

const enabledActions = computed(() => {
  return props.actions.filter(action => action.shouldShow ? action.shouldShow(props.row) : true)
})
</script>

<template>
  <div class="flex gap-2">
    <ULink
      v-for="(action, index) in enabledActions"
      :key="index"
      :active="true"
      class="cursor-pointer"
      @click="action.onClick(row)"
    >
      {{ action.label }}
    </ULink>
  </div>
</template>
