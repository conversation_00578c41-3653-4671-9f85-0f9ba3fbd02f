import type { MetricsData, MetricsPeriod } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import type { CalenderDateRange } from '~/utils/types/dates/daterange.type'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { dashboardKeys } from '../keys/dashboard.keys'
import { DashboardService } from '../services/dashboard.service'

export function useDashboardQuery(
  period?: MaybeRef<MetricsPeriod>,
  dateRange?: MaybeRef<CalenderDateRange>,
  options?: ComposableQueryOptions<MetricsData, typeof dashboardKeys.all>,
) {
  return useQuery({
    queryKey: computed(() => {
      return dashboardKeys.all(unref(period), unref(dateRange)).queryKey
    }),
    queryFn: () => {
      return DashboardService.getAll(unref(period), unref(dateRange))
    },
    staleTime: CACHE_CONFIG.IMMEDIATE,
    ...options,
  })
}
