<script setup lang="ts">
import type { ImportProfileFormData } from '~/utils/types/import-profile/import-profile-form.type'
import { IMPORT_PROFILE_STATUSES } from '~/utils/import-profiles/import-profile-statusses'
import ImportProfileConfigurationForm from './forms/ImportProfileConfigurationForm.vue'
import ImportProfileIntegrationForm from './forms/ImportProfileIntegrationForm.vue'
import ImportProfileLoadMapping from './forms/ImportProfileLoadMapping.vue'
import ImportProfileMapping from './forms/ImportProfileMappingForm.vue'
import ImportProfileScheduleForm from './forms/ImportProfileScheduleForm.vue'

defineProps<{
  step: string
  profile: ImportProfileFormData
  id: string | null
  isCreateMode: boolean
}>()
const emit = defineEmits<{
  submit: [data: ImportProfileFormData, isSaveAndBackToOverview?: boolean]
  nextStep: []
  previousStep: []
}>()

function handleSubmit(data: ImportProfileFormData, isSaveAndBackToOverview: boolean = false) {
  emit('submit', data, isSaveAndBackToOverview)
}
</script>

<template>
  <div>
    <ImportProfileConfigurationForm
      v-if="step === IMPORT_PROFILE_STATUSES.BASIC_INFORMATION"
      :id="id"
      :profile="profile"
      :is-create-mode="isCreateMode"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ImportProfileIntegrationForm
      v-else-if="id && step === IMPORT_PROFILE_STATUSES.INCOMPLETE"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ImportProfileLoadMapping
      v-else-if="id && step === IMPORT_PROFILE_STATUSES.MAPPING && profile.status === IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ImportProfileMapping
      v-else-if="id && step === IMPORT_PROFILE_STATUSES.MAPPING && profile.status !== IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
    <ImportProfileScheduleForm
      v-else-if="step === IMPORT_PROFILE_STATUSES.SCHEDULING"
      :id="id"
      :profile="profile"
      @submit="handleSubmit"
      @previous-step="$emit('previousStep')"
    />
  </div>
</template>
