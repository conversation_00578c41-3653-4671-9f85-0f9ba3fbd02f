import type { AvailableChannelData, ChannelData, CreateChannelData, UpdateChannelData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'

export const useChannelsStore = defineStore('channels', () => {
  const availableChannels = ref<Record<string, AvailableChannelData>>({})
  const channels = ref<ChannelData[]>([])

  // Local state functions

  function getAvailableChannelByProvider(channelClass: string): AvailableChannelData {
    const availableChannel = availableChannels.value[channelClass]
    if (!availableChannel)
      throw new Error(`Available channel not found for class: ${channelClass}`)
    return availableChannel
  }

  // API functions

  async function hydrateAvailable() {
    const response = await useApiClient()<ApiResponse<Record<string, AvailableChannelData>>>(
      `api/connectivity/available-channels`,
    )

    availableChannels.value = response.data
  }

  async function hydrate() {
    const response = await useApiClient()<ApiResponse<ChannelData[]>>(
      `api/connectivity/channels`,
    )

    channels.value = response.data
  }

  async function create(data: CreateChannelData) {
    const response = await useApiClient()<ApiResponse<ChannelData>>(
      `api/connectivity/channels`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
    )

    channels.value = [...channels.value, response.data]

    return response
  }

  async function update(channelId: string, data: UpdateChannelData) {
    const response = await useApiClient()<ApiResponse<ChannelData>>(
      `api/connectivity/channels/${channelId}`,
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
    )

    channels.value = channels.value.map(channel => channel.id === channelId ? response.data : channel)

    return response
  }

  async function remove(channelId: string) {
    await useApiClient()(
      `api/connectivity/channels/${channelId}`,
      {
        method: 'DELETE',
      },
    )

    channels.value = channels.value.filter(channel => channel.id !== channelId)
  }

  return {
    availableChannels,
    channels,

    getAvailableChannelByProvider,

    hydrateAvailable,
    hydrate,
    create,
    update,
    remove,
  }
})
