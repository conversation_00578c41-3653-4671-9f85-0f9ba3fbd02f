<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  value: string | null | undefined
}>()

const emit = defineEmits<{
  (e: 'update', value: string | null | undefined): void
}>()

const model = ref(props.value)

watch(() => props.value, (val) => {
  model.value = val
})

watch(model, (val) => {
  emit('update', val)
})
</script>

<template>
  <UInput v-model="model" class="input input-sm w-full" />
</template>
