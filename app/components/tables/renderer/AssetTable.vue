<script setup lang="ts">
import type { AssetTypeData } from '~/utils/types/api/api'
import { AssetQueryFactory } from '~/api/assets'
import QueryData from '~/utils/query/QueryData'
import { ColumnGenerator } from '~/utils/table/ColumnGenerator'
import { CopyColumn } from '~/utils/table/CopyColumn'

const props = defineProps({
  assetType: {
    type: Object as PropType<AssetTypeData>,
    required: true,
  },
})

const assetTypeTableFieldStore = useAssetTypeTableFieldStore()

const columns = ref()
const pageNumber = ref(1)
const query: Ref<QueryData> = shallowRef(new QueryData())

const { useAssetsQuery } = AssetQueryFactory
  .getQueryInstance(props.assetType.table_name)
  .getQueries()

const { data: assetsData, isFetching: isFetchingAssets } = useAssetsQuery(query)

function pageChanged() {
  const newQuery = new QueryData()
  newQuery.setPage(pageNumber.value)

  query.value = newQuery
}

const tableFields = assetTypeTableFieldStore.getByAssetTypeId(props.assetType?.id)
const generatedColumns = new ColumnGenerator<Record<string, any>>().generateColumns(tableFields)

generatedColumns.unshift(
  new CopyColumn<Record<string, any>>('id', 'Id'),
)
columns.value = generatedColumns.map(column => column.getConfig())
</script>

<template>
  <div v-if="!isFetchingAssets && assetsData">
    <UTable
      :columns="columns"
      :data="assetsData.data"
    />
    <div class="flex justify-between mt-4">
      <div>
        Showing {{ assetsData.meta?.from }} to {{ assetsData.meta?.to }} of {{ assetsData.meta?.total }} entries
      </div>
      <UPagination
        v-model:page="pageNumber"
        class="justify-center"
        :total="assetsData.meta?.total"
        @click="pageChanged"
      />
    </div>
  </div>
</template>
