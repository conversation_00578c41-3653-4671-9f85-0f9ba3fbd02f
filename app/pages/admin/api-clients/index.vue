<script setup lang="ts">
import type { ApiClientData } from '~/utils/types/api/api'
import { useApiClientsQuery, useDeleteApiClientMutation } from '~/api/api-clients'
import ApiClientList from '~/components/api-clients/ApiClientList.vue'

definePageMeta({
  layout: 'admin',
})

const {
  data: apiClients,
  isFetching: isFetchingApiClients,
} = useApiClientsQuery()

const router = useRouter()

function configureApiClient(client: ApiClientData) {
  router.push(`/admin/api-clients/${client.id}`)
}

const deleteApiClientMutation = useDeleteApiClientMutation()

async function deleteApiClient(clientId: string) {
  await deleteApiClientMutation.mutateAsync(clientId)

  useToast().add({
    title: 'API Client deleted',
    description: 'The API client has been successfully deleted.',
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="API Clients">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            API Clients
          </h1>
          <p class="text-gray-500 mt-3">
            Manage your API clients. Create, configure, and delete API clients as needed.
          </p>
        </div>
        <UButton
          label="Create API Client"
          size="lg"
          @click="$router.push('/admin/api-clients/new')"
        />
      </div>

      <ApiClientList
        v-if="!isFetchingApiClients && apiClients"
        :api-clients="apiClients"
        @configure-api-client="configureApiClient"
        @delete-api-client="deleteApiClient"
      />
    </template>
  </UDashboardPanel>
</template>
