<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: 'Confirm your action',
  },
  description: {
    type: String,
    default: '',
  },
})

const emit = defineEmits<{ close: [boolean] }>()
</script>

<template>
  <UModal>
    <template #header>
      <div class="my-2">
        <h2 class="text-lg font-bold">
          {{ title }}
        </h2>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
          {{ description }}
        </p>

        <div class="mt-4 flex gap-2 justify-end w-full">
          <UButton variant="outline" @click="emit('close', false)">
            Cancel
          </UButton>
          <UButton color="error" variant="solid" @click="emit('close', true)">
            Confirm
          </UButton>
        </div>
      </div>
    </template>
  </UModal>
</template>
