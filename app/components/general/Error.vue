<script setup lang="ts">
const props = defineProps({
  errorTitle: {
    type: String,
    required: false,
    default: 'Something went wrong',
  },
  errorDescription: {
    type: String,
    required: false,
    default: 'Try refreshing the page or contact support',
  },
  error: {
    type: Object,
    required: false,
    default: null,
  },
})

console.error(props.error)
</script>

<template>
  <UError :error="{ statusMessage: errorTitle, message: errorDescription }" />
</template>
