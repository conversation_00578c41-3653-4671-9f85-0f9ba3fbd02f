import type { AssetTypeFormFieldData } from '~/utils/types/api/api'
import { useCachedQueries } from '~/composables/utils/useCachedQueries'
import { assetTypeFormFieldKeys } from '../keys/asset-type-form-field.keys'

export class AssetTypeFormFieldCacheService {
  private static getCachedAssetTypeFormFields(): AssetTypeFormFieldData[] | undefined {
    const { getCachedQuery } = useCachedQueries()

    return getCachedQuery<AssetTypeFormFieldData[]>(
      assetTypeFormFieldKeys.all.queryKey,
    ).value
  }

  static getByAssetTypeId(assetTypeId: string): AssetTypeFormFieldData[] {
    const fields = this.getCachedAssetTypeFormFields()
    return fields?.filter((f: AssetTypeFormFieldData) => f.asset_type_id === assetTypeId) || []
  }
}
