import type { MetricsData } from '~/utils/types/api/api'
import type { ApiResponse } from '~/utils/types/api/api-response'
import type { CalenderDateRange } from '~/utils/types/dates/daterange.type'
import { defineStore } from 'pinia'
import { useApiClient } from '~/utils/api/api-client'
import { MetricsPeriod } from '~/utils/types/api/api'

export const useDashboardStore = defineStore('dashboard', () => {
  async function fetchAll(period: MetricsPeriod = MetricsPeriod.LAST_7_DAYS, dateRange?: CalenderDateRange) {
    return await useApiClient()<ApiResponse<MetricsData>>('api/metrics/dashboard', {
      query: {
        period,
        from: dateRange?.start.toString(),
        till: dateRange?.end.toString(),
      },
    })
  }

  return {
    fetchAll,
  }
})
