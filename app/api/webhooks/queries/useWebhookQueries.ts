import type { WebhookData } from '~/utils/types/api/api'
import type { ComposableQueryOptions } from '~/utils/types/api/vue-query'
import { useQuery } from '@tanstack/vue-query'
import { CACHE_CONFIG } from '~/utils/types/cache/cache.config'
import { webhookKeys } from '../keys/webhook.keys'

export function useWebhooksQuery() {
  return useQuery({
    ...webhookKeys.all,
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
  })
}

export function useWebhookByIdQuery(
  id: string,
  options?: ComposableQueryOptions<WebhookData, typeof webhookKeys.byId>,
) {
  return useQuery({
    ...webhookKeys.byId(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}

export function useWebhookByIdOrFailQuery(
  id: string,
  options?: ComposableQueryOptions<WebhookData, typeof webhookKeys.byIdOrFail>,
) {
  return useQuery({
    ...webhookKeys.byIdOrFail(id),
    staleTime: CACHE_CONFIG.MEDIUM_TERM,
    ...options,
  })
}
