import { computed } from 'vue'
import { createAssetKeysFactory } from './keys/asset.keys'
import { createAssetMutationsFactory } from './mutations/useAssetMutations'
import { createAssetQueriesFactory } from './queries/useAssetQueries'

export class AssetQueryFactory {
  private static instances = new Map<string, AssetQueryInstance>()

  static getQueryInstance(table: string): AssetQueryInstance {
    if (!this.instances.has(table)) {
      this.instances.set(table, new AssetQueryInstance(table))
    }

    return this.instances.get(table)!
  }
}

class AssetQueryInstance {
  private readonly table: string
  private readonly keys: ReturnType<typeof createAssetKeysFactory>
  private readonly queries: ReturnType<typeof createAssetQueriesFactory>
  private readonly mutations: ReturnType<typeof createAssetMutationsFactory>

  constructor(table: string) {
    this.table = table
    this.keys = createAssetKeysFactory(table)
    this.queries = createAssetQueriesFactory(table)
    this.mutations = createAssetMutationsFactory(table)
  }

  getKeys() {
    return this.keys
  }

  getQueries() {
    return this.queries
  }

  getMutations() {
    return this.mutations
  }
}

export function useAssetQuery(table: string) {
  const instance = computed(() => AssetQueryFactory.getQueryInstance(table))

  return {
    keys: computed(() => instance.value.getKeys()),
    queries: computed(() => instance.value.getQueries()),
    mutations: computed(() => instance.value.getMutations()),
  }
}
