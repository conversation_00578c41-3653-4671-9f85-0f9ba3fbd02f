<script setup lang="ts">
import type { ApiClientCredentialsData, ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import type { ApiClientData } from '~/utils/types/api/api'
import { useClipboard } from '@vueuse/core'
import { ref, watchEffect } from 'vue'
import { ZodError } from 'zod'
import { useApiClientByIdOrFailQuery, useCreateApiClientMutation, useRevokeAndGenerateApiClientMutation, useUpdateApiClientMutation } from '~/api/api-clients'
import ApiClientStepWrapper from '~/components/api-clients/ApiClientStepWrapper.vue'
import SteppedHeader from '~/components/general/SteppedHeader.vue'
import { useStepperWizard } from '~/composables/stepped-header/useStepperWizard'
import { apiClientToFormSchema } from '~/schemas/api-client/api-client-api-transform.schema'
import { apiClientRouteParamsSchema } from '~/schemas/api-client/apiClientRouteParamsSchema.schema'
import { API_CLIENT_STEPS } from '~/utils/api-clients/api-client-steps'

definePageMeta({ layout: 'empty' })

const clipboard = useClipboard({ legacy: true })
const apiClient = ref<ApiClientFormData | null>(null)
const credentials = ref<ApiClientCredentialsData>({
  key: '',
  secret: '',
  secretVisible: false,
  actualSecret: '',
})

const apiClientId = ref<string | null>(null)
const isCreateMode = ref<boolean>(false)
const routeError = ref<string | null>(null)

const {
  currentStep,
  currentStepIndex,
  currentStepNumber,
  highestReachedStepNumber,
  goToStep,
  goToNextStep,
  goToPreviousStep,
  totalSteps,
  isLastStep,
} = useStepperWizard(API_CLIENT_STEPS)

const route = useRoute()
const router = useRouter()

try {
  const parsedParams = apiClientRouteParamsSchema.parse(route.params)

  isCreateMode.value = parsedParams.id === 'new'
  if (!isCreateMode.value) {
    apiClientId.value = parsedParams.id
  }
  else {
    apiClient.value = {
      tokenName: '',
      hasExpiration: false,
      expiration_date: null,
      permissions: {
        readReferenceData: false,
        writeReferenceData: false,
        readOrderData: false,
        writeOrderData: false,
      },
    }
  }
}
catch (error) {
  if (error instanceof ZodError) {
    routeError.value = error.issues.map(issue => issue.message).join(', ')
  }
  else {
    routeError.value = 'Unexpected route error'
  }
}

const {
  data: fetchedApiClient,
  isError: isApiClientError,
  error: apiClientError,
} = useApiClientByIdOrFailQuery(apiClientId.value!, {
  enabled: !isCreateMode.value && !!apiClientId.value,
})

const latestSecret = ref<string | null>(null)

function setCredentialsFromApiClient(apiClientData: ApiClientData) {
  const secret = latestSecret.value || apiClientData.secret || ''

  credentials.value.key = apiClientData.key
  credentials.value.actualSecret = secret
  credentials.value.secret = '*'.repeat(secret.length || 0)
  credentials.value.secretVisible = false
}

watchEffect(() => {
  if (fetchedApiClient.value) {
    const parsed = apiClientToFormSchema.parse(fetchedApiClient.value)
    apiClient.value = {
      ...parsed,
      expiration_date: parsed.expiration_date ?? null,
    }

    if (!isCreateMode.value) {
      setCredentialsFromApiClient(fetchedApiClient.value)
    }
  }
})

function handleCancel() {
  router.push('/admin/api-clients')
}

async function copyToClipboard(text: string, label: string) {
  try {
    await clipboard.copy(text)
    useToast().add({
      title: 'Copied!',
      description: `${label} copied to clipboard`,
      color: 'success',
    })
  }
  catch (error) {
    console.error('Failed to copy to clipboard:', error)
    useToast().add({
      title: 'Copy Failed',
      description: 'Unable to copy to clipboard. Please copy manually.',
      color: 'error',
    })
  }
}

function toggleSecretVisibility() {
  credentials.value.secretVisible = !credentials.value.secretVisible
  if (credentials.value.secretVisible) {
    credentials.value.secret = credentials.value.actualSecret
  }
  else {
    credentials.value.secret = '*'.repeat(credentials.value.actualSecret.length)
  }
}

const revokeAndGenerateMutation = useRevokeAndGenerateApiClientMutation()

async function revokeAndGenerate() {
  const updatedClient = await revokeAndGenerateMutation.mutateAsync(apiClientId.value!)

  latestSecret.value = updatedClient.secret

  setCredentialsFromApiClient(updatedClient)

  useToast().add({
    title: 'Credentials Regenerated',
    description: 'New credentials have been created successfully',
    color: 'info',
  })
}

function testConnection() {
  useToast().add({
    title: 'Connection Test Successful',
    description: 'API credentials are working correctly',
    color: 'info',
  })
}

const createApiClientMutation = useCreateApiClientMutation()
const updateApiClientMutation = useUpdateApiClientMutation()

async function submitForm(data: ApiClientFormData) {
  try {
    if (currentStep.value === 'scope') {
      let apiClientResult
      if (isCreateMode.value) {
        apiClientResult = await createApiClientMutation.mutateAsync(data)
        apiClientId.value = apiClientResult.id

        useToast().add({
          title: 'API Client saved successfully',
          description: `${data.tokenName} has been configured successfully.`,
          color: 'success',
        })
      }
      else {
        if (!apiClientId.value) {
          throw new Error('API Client ID is required for update')
        }

        apiClientResult = await updateApiClientMutation.mutateAsync({
          apiClientId: apiClientId.value,
          data,
        })

        useToast().add({
          title: 'API Client updated successfully',
          description: `${data.tokenName} has been updated successfully.`,
          color: 'success',
        })
      }

      if (apiClientResult) {
        credentials.value.key = apiClientResult.key
        credentials.value.actualSecret = apiClientResult.secret || ''
        credentials.value.secret = '*'.repeat(apiClientResult.secret?.length || 0)
        credentials.value.secretVisible = false
      }
      apiClient.value = data

      goToNextStep()
      return
    }

    if (isLastStep.value) {
      return router.push('/admin/api-clients')
    }
  }
  catch (error: any) {
    const toast = useToast()
    const message = error?.data?.message || error?.message || 'An unexpected error occurred'

    toast.add({
      title: `Error ${isCreateMode.value ? 'creating' : 'updating'} API client`,
      description: message,
      color: 'error',
    })
  }
}

function handlePreviousStep() {
  if (currentStepIndex.value > 0) {
    goToPreviousStep()
  }
  else {
    handleCancel()
  }
}
</script>

<template>
  <div class="min-h-screen bg-neutral-50 flex flex-col">
    <SteppedHeader
      :title="isCreateMode ? 'Create API Client' : 'Update API Client'"
      :show-stepper="true"
      :current-step="currentStepNumber"
      :highest-reached-step="highestReachedStepNumber"
      :amount-of-steps="totalSteps"
      @change-step="goToStep"
      @cancel="handleCancel"
    />

    <div v-if="routeError || isApiClientError" class="p-8 text-red-500 text-center">
      {{ routeError || apiClientError?.message || 'Failed to load API client' }}
      <div class="mt-4">
        <UButton color="error" variant="solid" @click="handleCancel">
          Back to API Clients
        </UButton>
      </div>
    </div>

    <template v-else-if="apiClient">
      <UContainer class="hidden md:block md:py-16 max-w-3xl">
        <UCard class="px-6 py-4 shadow-md">
          <ApiClientStepWrapper
            :id="apiClientId"
            :step="currentStep"
            :api-client="apiClient"
            :credentials="credentials"
            @previous-step="handlePreviousStep"
            @submit="submitForm"
            @toggle-secret-visibility="toggleSecretVisibility"
            @copy-to-clipboard="copyToClipboard"
            @revoke-and-generate="revokeAndGenerate"
            @test-connection="testConnection"
          />
        </UCard>
      </UContainer>

      <UCard class="md:hidden px-6 py-4 bg-white flex-1">
        <ApiClientStepWrapper
          :id="apiClientId"
          :step="currentStep"
          :api-client="apiClient"
          :credentials="credentials"
          @previous-step="handlePreviousStep"
          @submit="submitForm"
          @toggle-secret-visibility="toggleSecretVisibility"
          @copy-to-clipboard="copyToClipboard"
          @revoke-and-generate="revokeAndGenerate"
          @test-connection="testConnection"
        />
      </UCard>
    </template>
  </div>
</template>
