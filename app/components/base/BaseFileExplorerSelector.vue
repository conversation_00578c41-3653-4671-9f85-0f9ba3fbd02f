<script setup lang="ts">
import type { DirectoryData, FileSystemItemData } from '~/utils/types/api/api'
import { FileType } from '~/utils/types/api/api'

interface TemplateFileSystemItem extends FileSystemItemData {
  fullPath: string
  isBack?: boolean
}

const props = defineProps<{
  id: string
  initialPath: string | null
  selectionType: 'file' | 'folder' | 'file-or-folder'
  queryFn: (id: string, path: MaybeRefOrGetter<string>) => any
}>()

const emit = defineEmits<{
  'update:path': [selectedPath: string]
}>()

function getInitialDirectory(path: string | null | undefined): string {
  if (!path || path === '/')
    return '/'
  if (path.endsWith('/'))
    return path
  const segments = path.split('/')
  segments.pop()
  const parentPath = segments.join('/') || '/'
  return parentPath.endsWith('/') ? parentPath : `${parentPath}/`
}

const currentPath = ref(getInitialDirectory(props.initialPath))
const selectedPath = ref<string>(props.initialPath || '')

const {
  data,
  isPending,
  isError: queryHasError,
  error: queryError,
} = props.queryFn(props.id, computed(() => currentPath.value)) as {
  data: Ref<DirectoryData | undefined>
  isPending: Ref<boolean>
  isError: Ref<boolean>
  error: any
}

const files = computed((): TemplateFileSystemItem[] => {
  if (!data.value)
    return []
  const path = data.value.path
  const items = data.value.items.map(item => ({
    ...item,
    fullPath: item.type === FileType.DIRECTORY ? `${path}${item.name}/` : `${path}${item.name}`,
  })) as TemplateFileSystemItem[]

  if (path !== '/') {
    const parentPath = path.replace(/[^/]+\/$/, '')
    items.unshift({
      name: '..',
      type: FileType.DIRECTORY,
      fullPath: parentPath,
      isBack: true,
    })
  }

  return items.sort((a, b) => {
    if (a.isBack)
      return -1
    if (b.isBack)
      return 1
    if (a.type !== b.type)
      return a.type === FileType.DIRECTORY ? -1 : 1
    return a.name.localeCompare(b.name)
  })
})

function handleItemClick(item: TemplateFileSystemItem) {
  if (item.type === FileType.DIRECTORY) {
    currentPath.value = item.fullPath
    if (props.selectionType !== 'file') {
      selectedPath.value = item.fullPath
    }
  }
  else if (props.selectionType !== 'folder') {
    selectedPath.value = item.fullPath
  }
}

function isSelected(item: TemplateFileSystemItem): boolean {
  return selectedPath.value === item.fullPath
}

function getIcon(item: TemplateFileSystemItem): string {
  if (item.isBack)
    return 'i-lucide-arrow-up-left'
  if (item.type === FileType.DIRECTORY)
    return 'i-lucide-folder'
  return 'i-lucide-file'
}

watch(selectedPath, (path) => {
  emit('update:path', path)
})
</script>

<template>
  <div class="space-y-2">
    <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded">
      <p v-if="selectionType !== 'folder'" class="text-sm text-gray-600 dark:text-gray-400">
        Current path: <span class="font-mono">{{ currentPath }}</span>
      </p>
      <p v-if="selectedPath" class="text-sm text-green-600 dark:text-green-400 mt-1">
        Selected: <span class="font-mono">{{ selectedPath }}</span>
      </p>
    </div>

    <div class="border border-gray-200 dark:border-gray-700 rounded-md max-h-80 overflow-y-auto">
      <div v-if="queryHasError" class="p-4 text-center text-red-500">
        {{ queryError }}
      </div>

      <div v-else-if="!isPending && files.length === 0" class="p-4 text-center text-gray-500">
        No files or folders found in this directory
      </div>

      <div v-else>
        <button
          v-for="item in files"
          :key="item.fullPath"
          type="button"
          class="w-full flex items-center gap-3 p-3 hover:bg-neutral-100 dark:hover:bg-neutral-800 text-left transition-colors"
          :class="{ 'bg-neutral-100 dark:bg-neutral-800': isSelected(item) }"
          @click="handleItemClick(item)"
        >
          <UIcon :name="getIcon(item)" class="h-5 w-5 flex-shrink-0" />
          <span class="font-mono text-sm truncate">{{ item.name }}</span>
        </button>
      </div>

      <UProgress v-if="isPending" />
    </div>
  </div>
</template>

<style scoped>
</style>
