import type { UpdateSettingsData } from '~/utils/types/api/api'
import { useMutation } from '@tanstack/vue-query'
import { queryClient } from '~/plugins/vue-query'
import { settingsKeys } from '../keys/settings.keys'
import { SettingsService } from '../services/settings.service'

export function useUpdateSettingsMutation() {
  return useMutation({
    mutationFn: (data: UpdateSettingsData) => SettingsService.updateSettings(data),
    onSuccess: (data) => {
      queryClient.setQueryData(settingsKeys.all.queryKey, data)

      queryClient.invalidateQueries({
        queryKey: settingsKeys.all.queryKey,
      })
    },
  })
}
