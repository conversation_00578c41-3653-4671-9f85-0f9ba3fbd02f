export interface ValidateAction {
  get successMessage(): string
  get action(): string
}

export class ValidateActionReject implements ValidateAction {
  get successMessage(): string {
    return 'Rejected'
  }

  get action(): string {
    return '\\Modules\\DynamicAssets\\Actions\\TransportOrder\\RejectOrder'
  }
}

export class ValidateActionSave implements ValidateAction, DataContainable {
  constructor(public readonly data: any) {
  }

  get successMessage(): string {
    return 'Saved'
  }

  get action(): string {
    return '\\Modules\\DynamicAssets\\Actions\\TransportOrder\\SaveOrder'
  }
}

export class ValidateActionValidate implements ValidateAction, DataContainable {
  constructor(public readonly data: any) {
  }

  get successMessage(): string {
    return 'Approved'
  }

  get action(): string {
    return '\\Modules\\DynamicAssets\\Actions\\TransportOrder\\ValidateOrder'
  }
}

export class ValidateActionCheck implements ValidateAction, DataContainable {
  constructor(public readonly data: any) {
  }

  get successMessage(): string {
    return 'Checked'
  }

  get action(): string {
    return '\\Modules\\DynamicAssets\\Actions\\TransportOrder\\CheckOrder'
  }
}

export interface DataContainable {
  get data(): any
}
export function isDataContainableAction(action: ValidateAction): action is ValidateAction & DataContainable {
  return 'data' in action
}
