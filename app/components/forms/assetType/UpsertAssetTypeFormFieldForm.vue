<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeRelationshipData, UpsertAssetTypeFormFieldData } from '~/utils/types/api/api'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import {
  useCreateAssetTypeFormFieldMutation,
  useUpdateAssetTypeFormFieldMutation,
} from '~/api/asset-type-form-fields/mutations/useAssetTypeFormFieldMutations'
import { useFieldTemplate } from '~/composables/form/useFieldTemplate'
import {
  AssetTypeFormFieldForm,
  AssetTypeFormFieldType,

} from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
  assetTypeFormField: {
    type: Object as PropType<UpsertAssetTypeFormFieldData>,
    required: false,
    default: null,
  },
})
const emit = defineEmits<{ close: [boolean] }>()

const toast = useToast()

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()
const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()

const assetTypeFields = computed(() => {
  if (!allAssetTypeFields.value)
    return []
  return allAssetTypeFields.value.filter(field => field.asset_type_id === props.assetTypeId)
})

const data = ref<UpsertAssetTypeFormFieldData>(
  useCloneDeep(props.assetTypeFormField) || {
    asset_type_field_id: null,
    asset_type_relationship_id: null,
    form: AssetTypeFormFieldForm.Edit,
    type: AssetTypeFormFieldType.Text,
    required: false,
    locked: false,
    span: 12,
    crud: false,
    filterable: false,
  },
)
const isEditing = computed(() => !!props.assetTypeFormField)
const selectedFormFieldRelatable = ref<string>()
const relationships = ref<AssetTypeRelationshipData[]>([])

onMounted(() => {
  relationships.value = assetTypeRelationshipStore.getByAssetTypeId(props.assetTypeId)
  if (props.assetTypeFormField) {
    if (props.assetTypeFormField.asset_type_field_id) {
      selectedFormFieldRelatable.value = 'Field'
    }
    else if (props.assetTypeFormField.asset_type_relationship_id) {
      selectedFormFieldRelatable.value = 'Relationship'
    }
  }
})

const selectedFormFieldRelatableComputable = computed({
  get: () => selectedFormFieldRelatable.value,
  set: (value: string) => {
    selectedFormFieldRelatable.value = value
    resetRelationshipFields()
  },
})

const selectedRelationship = computed(() => {
  return relationships.value.find(x => x.id === data.value.asset_type_relationship_id)
})

const allowsTemplate = computed(() => {
  return data.value.type === AssetTypeFormFieldType.AdvancedMultilineSelect
})

function getFieldsByAssetTypeId(assetTypeId: string) {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}

function resetRelationshipFields() {
  data.value.asset_type_field_id = null
  data.value.asset_type_relationship_id = null
}

const { selectedTemplateField, getTemplate, setTemplate, addFieldToTemplate } = useFieldTemplate(
  data,
  allAssetTypeFields,
)

const createAssetTypeFormFieldMutation = useCreateAssetTypeFormFieldMutation()
const updateAssetTypeFormFieldMutation = useUpdateAssetTypeFormFieldMutation()
const isPending = computed(() =>
  createAssetTypeFormFieldMutation.isPending.value
  || updateAssetTypeFormFieldMutation.isPending.value,
)

async function onSubmit() {
  try {
    if (isEditing.value && data.value.id) {
      await updateAssetTypeFormFieldMutation.mutateAsync({
        assetTypeId: props.assetTypeId,
        id: data.value.id,
        data: data.value,
      })
    }
    else {
      await createAssetTypeFormFieldMutation.mutateAsync({
        assetTypeId: props.assetTypeId,
        data: data.value,
      })
    }
    emit('close', false)
    toast.add({ title: `Master data form field ${isEditing.value ? 'updated' : 'created'}`, color: 'success' })
  }
  catch (e) {
    toast.add({
      title: `Error ${isEditing.value ? 'updating' : 'creating'} master data form field`,
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <USlideover :title="isEditing ? 'Edit form field' : 'Add form field'" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between gap-4" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Select form" class="col-span-6">
            <USelect
              v-model="data.form"
              :items="Object.values(AssetTypeFormFieldForm)"
              placeholder="Select form"
              class="w-full"
            />
          </UFormField>

          <UFormField label="Span" class="col-span-6">
            <USelect
              v-model="data.span"
              :items="[2, 3, 4, 6, 9, 12]"
              placeholder="Select span size"
              class="w-full"
            />
          </UFormField>

          <UFormField label="Select relatable type" class="col-span-12">
            <URadioGroup
              v-model="selectedFormFieldRelatableComputable"
              orientation="horizontal"
              :items="['Field', 'Relationship']"
            />
          </UFormField>

          <UFormField
            v-if="selectedFormFieldRelatable === 'Field'"
            label="Select field"
            class="col-span-12"
          >
            <USelect
              :model-value="data.asset_type_field_id ?? undefined"
              :items="assetTypeFields as any"
              :label-key="'label' as any"
              :value-key="'id' as any"
              placeholder="Select field"
              class="w-full"
              @update:model-value="data.asset_type_field_id = $event ?? null"
            />
          </UFormField>

          <UFormField
            v-if="selectedFormFieldRelatable === 'Relationship'"
            label="Select relationship"
            class="col-span-12"
          >
            <USelect
              :model-value="data.asset_type_relationship_id ?? undefined"
              :items="relationships as any"
              :label-key="'label' as any"
              :value-key="'id' as any"
              placeholder="Select relationship"
              class="w-full"
              @update:model-value="data.asset_type_relationship_id = $event ?? null"
            />
          </UFormField>

          <UFormField
            v-if="selectedFormFieldRelatable"
            label="Select type"
            class="col-span-12"
          >
            <USelect
              v-model="data.type"
              :items="Object.values(AssetTypeFormFieldType)"
              placeholder="Select type"
              class="w-full"
            />
          </UFormField>

          <div v-if="allowsTemplate && selectedRelationship" class="w-full gap-4 col-span-12">
            <hr class="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700">
            <UFormField
              label="Add field to template"
              class="col-span-12"
            >
              <USelect
                v-model="selectedTemplateField"
                :items="getFieldsByAssetTypeId(selectedRelationship?.relationship_asset_type_id ?? '').map(field => ({ id: field.field, label: field.label, type: 'item' as const }))"
                :label-key="'label' as any"
                :value-key="'id' as any"
                placeholder="Select field"
                class="w-full"
                @change="addFieldToTemplate"
              />
            </UFormField>

            <UFormField
              label="Template"
              class="w-full"
            >
              <UTextarea :model-value="getTemplate()" @update:model-value="setTemplate" />
            </UFormField>
            <hr class="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700">
          </div>

          <UFormField
            label="Required"
            class="col-span-6"
          >
            <USwitch v-model="data.required" />
          </UFormField>

          <UFormField
            label="Locked"
            class="col-span-6"
          >
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField
            label="Crud"
            class="col-span-6"
          >
            <USwitch v-model="data.crud" />
          </UFormField>
          <UFormField
            label="Filterable"
            class="col-span-6"
          >
            <USwitch v-model="data.filterable" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          {{ isEditing ? 'Update' : 'Create' }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
