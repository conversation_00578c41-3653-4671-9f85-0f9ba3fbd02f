<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'
import type { UseMutationReturnType } from '@tanstack/vue-query'
import type { FetchError } from 'ofetch'
import type { FtpIntegrationForm } from '~/schemas/integrations/ftp-integration-form.schema'
import type { CreateIntegrationData, FtpConfiguration, StatusMessage } from '~/utils/types/api/api'
import type { BaseIntegrationFormProps } from '~/utils/types/integrations'
import { ftpIntegrationFormSchema } from '~/schemas/integrations/ftp-integration-form.schema'
import { FtpEncryption, FtpProtocol } from '~/utils/types/api/api'
import { getTestResultAlertProps } from '~/utils/types/integrations'

export type TestIntegrationMutation = UseMutationReturnType<
  StatusMessage,
  Error,
  CreateIntegrationData,
  unknown
>

interface IntegrationsUpsertFtpProps extends BaseIntegrationFormProps {
  testResult?: StatusMessage
  testMutation: TestIntegrationMutation
}

const props = defineProps<IntegrationsUpsertFtpProps>()

const emit = defineEmits<{
  test: [data: FtpIntegrationForm]
  submit: [formData: FtpIntegrationForm]
}>()

const form = ref()
const defaultConfig: FtpConfiguration = {
  ftp_protocol: FtpProtocol.FTP,
  ftp_host: '',
  ftp_port: 21,
  ftp_username: '',
  ftp_password: '',
  ftp_encryption: FtpEncryption.AUTO,
  ftp_private_key: '',
  ftp_passphrase: '',
  ftp_passive_mode: true,
  base_folder: '/',
}

const state = ref<FtpIntegrationForm>({
  label: props.integration?.label || '',
  data: {
    ...defaultConfig,
    ftp_username: defaultConfig.ftp_username ?? '',
    ...props.integration?.data,
  },
})

const protocolOptions = [
  { label: 'FTP - File Transfer Protocol', value: FtpProtocol.FTP },
  { label: 'SFTP - SSH File Transfer Protocol', value: FtpProtocol.SFTP },
]

const encryptionOptions = [
  { label: 'No encryption', value: FtpEncryption.NONE },
  { label: 'Use explicit FTP over TLS if available', value: FtpEncryption.AUTO },
  { label: 'Require explicit FTP over TLS', value: FtpEncryption.TLS },
  { label: 'Require implicit FTP over SSL', value: FtpEncryption.SSL },
]

watch(() => props.integration, (newValue) => {
  if (!newValue) {
    return
  }

  state.value = {
    label: newValue.label,
    data: {
      ...state.value.data,
      ...newValue.data,
    },
  }
})

async function testConnection() {
  try {
    await form.value?.validate()
    emit('test', state.value)
  }
  catch {

  }
}

function onSubmit(event: FormSubmitEvent<FtpIntegrationForm>) {
  const formData = {
    label: event.data.label,
    data: event.data.data,
  }
  emit('submit', formData)
}
</script>

<template>
  <UForm
    ref="form"
    :schema="ftpIntegrationFormSchema"
    :state="state"
    @submit="onSubmit"
  >
    <div class="space-y-4">
      <h2 class="text-lg font-bold">
        FTP
      </h2>

      <UFormField label="Label" name="label" required>
        <UInput v-model="state.label" placeholder="Enter a name for this integration" />
      </UFormField>

      <UFormField label="Protocol" name="data.ftp_protocol" required>
        <USelect
          v-model="state.data.ftp_protocol"
          :items="protocolOptions"
          option-attribute="label"
          value-attribute="value"
          class="w-full"
        />
      </UFormField>

      <div class="flex gap-4">
        <UFormField label="Host" name="data.ftp_host" class="flex-1" required>
          <UInput v-model="state.data.ftp_host" placeholder="hostname.example.com" />
        </UFormField>

        <UFormField label="Port" name="data.ftp_port" class="w-32" required>
          <UInput
            v-model="state.data.ftp_port"
            type="number"
            :min="1"
            :max="65535"
          />
        </UFormField>
      </div>

      <UFormField label="Encryption" name="data.ftp_encryption" required>
        <USelect
          v-model="state.data.ftp_encryption"
          :items="encryptionOptions"
          option-attribute="label"
          value-attribute="value"
          class="w-full"
        />
      </UFormField>

      <UFormField label="User" name="data.ftp_username" required>
        <UInput v-model="state.data.ftp_username" placeholder="username" />
      </UFormField>

      <UFormField
        label="Password"
        name="data.ftp_password"
      >
        <GeneralPasswordInput v-model="state.data.ftp_password" />
      </UFormField>

      <UFormField
        v-if="state.data.ftp_protocol === FtpProtocol.SFTP"
        label="Private Key"
        name="data.ftp_private_key"
      >
        <UTextarea
          v-model="state.data.ftp_private_key"
          placeholder="-----BEGIN OPENSSH PRIVATE KEY-----"
        />
      </UFormField>

      <UFormField
        v-if="state.data.ftp_protocol === FtpProtocol.SFTP"
        label="Private Key Passphrase"
        name="data.ftp_passphrase"
      >
        <UInput
          v-model="state.data.ftp_passphrase"
          type="password"
          placeholder="Enter passphrase if required"
        />
      </UFormField>

      <div class="flex items-center gap-2">
        <USwitch v-model="state.data.ftp_passive_mode" />
        <p v-if="state.data.ftp_passive_mode">
          Passive mode
        </p>
        <p v-else>
          Active mode
        </p>
      </div>

      <UFormField label="Base folder" name="data.base_folder" required>
        <UInput
          v-model="state.data.base_folder"
          placeholder="/path/to/folder"
        />
      </UFormField>
    </div>

    <div class="flex flex-col gap-2 mt-8">
      <UButton
        color="neutral"
        variant="outline"
        size="lg"
        block
        :loading="props.testMutation.isPending?.value"
        @click.prevent="testConnection"
      >
        Test connection
      </UButton>
      <UAlert
        v-if="props.testResult"
        v-bind="getTestResultAlertProps(props.testResult)"
        :description="props.testResult.message"
      />

      <UAlert
        v-if="props.testMutation.isError?.value"
        color="error"
        icon="i-ph-x-circle"
      >
        {{ (props.testMutation.error?.value as FetchError)?.data?.message || 'Test failed' }}
      </UAlert>

      <UButton
        label="Save"
        type="submit"
        color="neutral"
        size="lg"
        block
        :loading="props.isSubmitting"
        :disabled="props.isSubmitting"
      />
    </div>
  </UForm>
</template>
