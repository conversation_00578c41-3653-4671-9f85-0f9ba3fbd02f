<script setup lang="ts">
import type { ApiClientData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'

defineProps<{
  apiClients: ApiClientData[]
}>()

const emit = defineEmits<{
  configureApiClient: [apiClient: ApiClientData]
  deleteApiClient: [apiClientId: string]
}>()

function configureApiClient(apiClient: ApiClientData) {
  emit('configureApiClient', apiClient)
}

async function showDeleteConfirmation(apiClient: ApiClientData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: 'Delete API Client',
    description: `Are you sure you want to delete the API client "${apiClient.name}"? This action cannot be undone.`,
  })

  if (!await overlay.result)
    return

  emit('deleteApiClient', apiClient.id)
}
</script>

<template>
  <div>
    <h2 class="text-lg font-bold mb-4">
      Active API Clients
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="apiClient in apiClients"
        :key="apiClient.id"
        class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-1">
          <p class="font-bold flex items-center">
            <slot name="api-client-name" :api-client="apiClient">
              {{ apiClient.name }}
            </slot>
          </p>
          <div class="flex flex-col gap-1">
            <p class="text-sm text-gray-500">
              API Key: {{ apiClient.key }}
            </p>
            <p class="text-sm text-gray-500">
              Scopes: <span class="font-bold">{{ apiClient.scopes.join(' , ') }}</span>
            </p>
          </div>
        </div>
        <div class="flex items-center">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            label="Configure"
            @click="configureApiClient(apiClient)"
          />
          <UButton
            variant="link"
            color="error"
            class="py-2 px-4 text-sm ml-2"
            label="Delete"
            @click="showDeleteConfirmation(apiClient)"
          />
        </div>
      </div>
      <p v-if="!apiClients?.length" class="p-4 text-center text-gray-500">
        No configured API clients found.
      </p>
    </UPageCard>
  </div>
</template>
