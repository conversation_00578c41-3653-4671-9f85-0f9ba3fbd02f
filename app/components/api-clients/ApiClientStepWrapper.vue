<script setup lang="ts">
import type { ApiClientCredentialsData, ApiClientFormData } from '~/utils/types/api-client/api-client-form.type'
import ApiClientCredentials from './ApiClientCredentials.vue'
import ApiClientScopeForm from './forms/ApiClientScopeForm.vue'

defineProps<{
  step: string
  apiClient: ApiClientFormData
  credentials: ApiClientCredentialsData
  id: string | null
}>()

const emit = defineEmits<{
  submit: [data: ApiClientFormData]
  nextStep: []
  previousStep: []
  toggleSecretVisibility: []
  copyToClipboard: [text: string, label: string]
  revokeAndGenerate: []
  testConnection: []
}>()
</script>

<template>
  <div>
    <ApiClientScopeForm
      v-if="step === 'scope'"
      :api-client="apiClient"
      @submit="emit('submit', $event)"
      @previous-step="$emit('previousStep')"
    />
    <ApiClientCredentials
      v-else-if="step === 'credentials'"
      :api-client="apiClient"
      :credentials="credentials"
      @submit="emit('submit', $event)"
      @toggle-secret-visibility="$emit('toggleSecretVisibility')"
      @copy-to-clipboard="(text: string, label: string) => emit('copyToClipboard', text, label)"
      @revoke-and-generate="$emit('revokeAndGenerate')"
      @test-connection="$emit('testConnection')"
    />
  </div>
</template>
