<script setup lang="ts">
import type { FormDefinition } from '~/utils/forms/FormDefinition'
import { useCloneDeep } from '#imports'

const props = defineProps({
  title: { type: String, default: 'Form' },
  formDefinition: { type: Object as PropType<FormDefinition>, required: true },
  data: { type: Object as PropType<any>, required: true },
  errors: { type: Object as PropType<Record<string, string[]>>, default: () => ({}) },
  isReadOnly: { type: Boolean, default: false },
})

const emit = defineEmits<{ close: [any] }>()
const decoupledData = ref(useCloneDeep(props.data))

function onsubmit() {
  emit('close', {
    ...decoupledData.value,
    id: props.data?.id,
  })
}
</script>

<template>
  <USlideover :title="title" :overlay="false">
    <template #body>
      <FormsRendererForm
        v-model="decoupledData"
        :form-definition="formDefinition"
        :errors="errors"
        :is-read-only="isReadOnly"
        :is-sub-form="isReadOnly"
        @submit="onsubmit"
      />
    </template>
  </USlideover>
</template>
