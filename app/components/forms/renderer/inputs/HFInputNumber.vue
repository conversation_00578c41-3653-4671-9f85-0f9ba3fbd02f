<script setup lang="ts">
import type { FormInputProps } from '~/utils/forms/formInputProps'
import { computed } from 'vue'
import { useFormFieldError } from '~/composables/form/useFormFieldError'
import NumericField from '../../inputs/NumericField.vue'

const props = defineProps<FormInputProps>()

const model = defineModel<string | number>()

const { error } = useFormFieldError(
  computed(() => props.errors),
  computed(() => props.errorDisplay),
)
</script>

<template>
  <div class="flex flex-col gap-2">
    <UFormField :label="label" :error="error">
      <NumericField
        v-model="model"
        :name="name"
        :disabled="isReadOnly"
      />
    </UFormField>
  </div>
</template>
